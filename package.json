{"name": "gambyl-frontend", "version": "0.1.0", "private": true, "dependencies": {"@auth0/auth0-react": "^2.0.0", "@daml.js/da-marketplace": "file:../daml.js/da-marketplace-0.2.0", "@daml.js/enetpulse-integration": "file:../daml.js/enetpulse-integration-model-0.0.1", "@daml.js/gambyl-ledger": "file:../daml.js/gambyl-ledger-0.0.1", "@daml.js/jumio-integration": "file:../daml.js/jumio-integration-model-0.0.1", "@daml.js/moneymatrix-integration": "file:../daml.js/moneymatrix-integration-model-0.0.1", "@daml/hub-react": "1.1.5", "@daml/ledger": "^2.8.8", "@daml/react": "^2.8.8", "@daml/types": "^2.8.8", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-brands-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/react-fontawesome": "^0.1.16", "@tanstack/react-table": "^8.11.2", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "bootstrap": "^5.3.5", "formik": "^2.2.9", "i18next": "^21.6.14", "jwt-decode": "^3.1.2", "jwt-simple": "^0.5.6", "mathjs": "^10.1.1", "moment-timezone": "^0.5.40", "numeral": "^2.0.6", "papaparse": "^5.4.1", "react": "^18.3.1", "react-currency-input-field": "^3.6.0", "react-datepicker": "^4.3.0", "react-dom": "^18.3.1", "react-flags-select": "^2.1.2", "react-gtm-module": "^2.0.11", "react-i18next": "^11.15.6", "react-idle-timer": "^4.6.4", "react-loader-spinner": "^4.0.0", "react-modal": "^3.16.1", "react-query": "^3.27.0", "react-responsive-carousel": "^3.2.22", "react-router-dom": "^6.0.0", "react-scripts": "4.0.3", "react-switch": "^7.0.0", "react-toastify": "^8.1.0", "react-tooltip": "^4.2.21", "sass": "^1.43.2", "typescript": "^4.1.2", "uuid": "^8.3.2", "web-vitals": "^1.0.1", "yup": "^0.32.11"}, "scripts": {"start": "REACT_APP_PUBLIC_TOKEN=https://rrcc59f1s4f1i5nm.daml.app/api/public-party REACT_APP_AUTH0_DOMAIN=https://dev-d51eadyaio4on45e.us.auth0.com REACT_APP_AUTH0_CLIENT_ID=24zqSDB29tkRaWa6Rxd97dea8wNljT6T  REACT_APP_AUTH0_CALLBACK_URL=http://localhost:3000/callback REACT_APP_IS_PROD=false react-scripts start", "start-local": "NODE_OPTIONS=--openssl-legacy-provider REACT_APP_PUBLIC_TOKEN=https://rrcc59f1s4f1i5nm.daml.app/api/public-party REACT_APP_AUTH0_DOMAIN=https://dev-d51eadyaio4on45e.us.auth0.com REACT_APP_AUTH0_CLIENT_ID=24zqSDB29tkRaWa6Rxd97dea8wNljT6T  REACT_APP_AUTH0_CALLBACK_URL=http://localhost:3000/callback REACT_APP_IS_PROD=false react-scripts start", "build": " REACT_APP_AUTH0_DOMAIN=https://dev-d51eadyaio4on45e.us.auth0.com REACT_APP_AUTH0_CLIENT_ID=24zqSDB29tkRaWa6Rxd97dea8wNljT6T  REACT_APP_AUTH0_CALLBACK_URL=http://localhost:3000/callback REACT_APP_IS_PROD=false react-scripts build", "build-qa": "NODE_OPTIONS=--openssl-legacy-provider REACT_APP_PUBLIC_TOKEN=https://rrcc59f1s4f1i5nm.daml.app/api/public-party REACT_APP_AUTH0_DOMAIN=https://dev-d51eadyaio4on45e.us.auth0.com REACT_APP_AUTH0_CLIENT_ID=24zqSDB29tkRaWa6Rxd97dea8wNljT6T  REACT_APP_AUTH0_CALLBACK_URL=https://dit-exchange.gambyl.com/callback REACT_APP_IS_PROD=false react-scripts build", "build-uat": "NODE_OPTIONS=--openssl-legacy-provider REACT_APP_PUBLIC_TOKEN=https://iutt7576tti4lzy9.daml.app/api/public-party REACT_APP_AUTH0_DOMAIN=https://qa-gambyl.us.auth0.com REACT_APP_AUTH0_CLIENT_ID=RZPDRQpmnq46ZWveFMkMPkQuZysxNyoy  REACT_APP_AUTH0_CALLBACK_URL=https://qa-exchange.gambyl.com/callback REACT_APP_IS_PROD=false react-scripts build", "build-prod": "NODE_OPTIONS=--openssl-legacy-provider REACT_APP_PUBLIC_TOKEN=https://imenoj6hcxkdra2n.daml.app/api/public-party REACT_APP_AUTH0_DOMAIN=https://beta-gambyl.us.auth0.com REACT_APP_AUTH0_CLIENT_ID=gfAMvMnP3leZIpdHcLQiLX0EzUKRI7Q0  REACT_APP_AUTH0_CALLBACK_URL=https://beta.gambyl.com/callback REACT_APP_IS_PROD=true react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "https://jsonapi-dev.gambyl.bc.intellecteu.com/", "devDependencies": {"@lingui/cli": "^3.12.1", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/numeral": "^2.0.2", "@types/papaparse": "^5.3.14", "@types/react": "^17.0.0", "@types/react-datepicker": "^4.1.7", "@types/react-dom": "^17.0.0", "@types/react-gtm-module": "^2.0.1", "@types/react-modal": "^3.13.1", "@types/react-router-dom": "^5.3.1", "@types/react-router-hash-link": "^2.4.5", "@types/uuid": "^8.3.3", "@types/yup": "^0.29.13", "cross-env": "^7.0.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "jscodeshift": "^17.3.0", "prettier": "^2.2.1"}}