stages:          # List of stages for jobs, and their order of execution
  - build

build-image:
  image: docker:dind
  stage: build
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_BUILDKIT: 1
  needs:
    - project: intellecteu/blockchain/clients/gambyl/gambyl-daml
      ref: working
      job: artifacts
      artifacts: true
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA -t $CI_REGISTRY_IMAGE:latest .
    - docker push $CI_REGISTRY_IMAGE --all-tags
    - echo "${CI_PIPELINE_SOURCE}"
    - echo "${CI_PIPELINE_TRIGGERED}"
  only:
    - pipelines


docker-compose-recerate:
  stage: build
  trigger:
    project: intellecteu/blockchain/clients/gambyl/gambyl-daml
    # branch: dev
    branch: working
    strategy: depend
  except:
    - pipelines
