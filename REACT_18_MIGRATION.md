# React 17 to 18 Migration Guide

This guide provides step-by-step instructions for migrating your React application from version 17 to 18.

## Prerequisites

Before running the migration script, ensure you have the following tools installed:

1. **Node.js** (version 14 or higher)
2. **npm** or **yarn**
3. **jscodeshift** (for running the transform)

## Step 1: Install jscodeshift

If you don't have jscodeshift installed globally, install it:

```bash
npm install -g jscodeshift
```

## Step 2: Update Dependencies

Update your package.json dependencies to React 18. Run these commands:

```bash
# Update React and React-DOM to version 18
npm install react@^18.0.0 react-dom@^18.0.0

# Update React types for TypeScript
npm install --save-dev @types/react@^18.0.0 @types/react-dom@^18.0.0

# Update React Scripts to version 5+ (for React 18 support)
npm install --save-dev react-scripts@^5.0.0
```

## Step 3: Run the JSCodeshift Transform

Run the provided transform to automatically update your code:

```bash
# Run the transform on your src directory
jscodeshift -t react-17-to-18-transform.js src/ --extensions=tsx,ts,jsx,js --parser=tsx
```

### What the transform does:

1. **Updates ReactDOM.render() calls**: Converts `ReactDOM.render(component, container)` to use the new `createRoot` API
2. **Updates ReactDOM.hydrate() calls**: Converts to `hydrateRoot` API
3. **Adds necessary imports**: Imports `createRoot` and `hydrateRoot` from `react-dom/client`
4. **Marks unmount calls**: Adds TODO comments for `ReactDOM.unmountComponentAtNode()` calls that need manual review

## Step 4: Manual Review and Updates

After running the transform, review the following:

### 1. Check the main entry point (src/index.tsx)

The transform should have updated your `src/index.tsx` from:

```tsx
ReactDOM.render(
    <React.Suspense fallback={<LoaderSpinner />}>
        {/* Your app components */}
    </React.Suspense>,
    document.getElementById('root')
)
```

To:

```tsx
const root = createRoot(document.getElementById('root'))
root.render(
    <React.Suspense fallback={<LoaderSpinner />}>
        {/* Your app components */}
    </React.Suspense>
)
```

### 2. Handle any unmount calls

If you have any `ReactDOM.unmountComponentAtNode()` calls, they need to be manually updated:

**Before (React 17):**
```tsx
ReactDOM.unmountComponentAtNode(container)
```

**After (React 18):**
```tsx
// You need to keep a reference to the root
const root = createRoot(container)
// Later when you want to unmount:
root.unmount()
```

### 3. Update test files

If you have test files that use `ReactDOM.render()`, update them as well:

```tsx
// Before
import { render } from '@testing-library/react'
// or
import ReactDOM from 'react-dom'

// After - no changes needed for @testing-library/react
// It handles React 18 compatibility automatically
```

## Step 5: Test Your Application

1. **Start the development server**:
   ```bash
   npm start
   ```

2. **Run tests**:
   ```bash
   npm test
   ```

3. **Build the application**:
   ```bash
   npm run build
   ```

## Step 6: Review React 18 Features and Breaking Changes

### New Features You Can Use:
- **Automatic Batching**: Multiple state updates are automatically batched
- **Concurrent Features**: `useTransition`, `useDeferredValue`, `Suspense` improvements
- **Strict Mode**: Enhanced to help detect side effects

### Potential Breaking Changes:
- **Stricter StrictMode**: May reveal previously hidden issues
- **Consistent useEffect timing**: Effects are now consistently fired after paint
- **Internet Explorer support dropped**: React 18 doesn't support IE

## Step 7: Optional Optimizations

Consider adopting new React 18 features:

1. **Use Concurrent Features**:
   ```tsx
   import { useTransition, useDeferredValue } from 'react'
   
   function MyComponent() {
     const [isPending, startTransition] = useTransition()
     // Use for non-urgent updates
   }
   ```

2. **Enhanced Suspense**:
   ```tsx
   <Suspense fallback={<Loading />}>
     <LazyComponent />
   </Suspense>
   ```

## Troubleshooting

### Common Issues:

1. **TypeScript errors**: Make sure you've updated `@types/react` and `@types/react-dom`
2. **Build errors**: Update `react-scripts` to version 5+
3. **Test failures**: Update testing libraries if needed

### If you encounter issues:

1. Check the console for specific error messages
2. Ensure all dependencies are compatible with React 18
3. Review the [React 18 upgrade guide](https://react.dev/blog/2022/03/08/react-18-upgrade-guide)

## Rollback Plan

If you need to rollback:

1. Revert package.json changes:
   ```bash
   npm install react@^17.0.0 react-dom@^17.0.0 @types/react@^17.0.0 @types/react-dom@^17.0.0
   ```

2. Revert code changes using git:
   ```bash
   git checkout -- src/
   ```

## Additional Resources

- [React 18 Release Notes](https://react.dev/blog/2022/03/29/react-v18)
- [React 18 Upgrade Guide](https://react.dev/blog/2022/03/08/react-18-upgrade-guide)
- [Concurrent Features](https://react.dev/reference/react)
