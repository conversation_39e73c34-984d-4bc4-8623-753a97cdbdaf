# React Router v5 to v6 Migration Guide

This guide provides step-by-step instructions for migrating your React Router from version 5 to 6.

## Prerequisites

Before running the migration script, ensure you have:

1. **Node.js** (version 14 or higher)
2. **jscodeshift** installed globally
3. **React Router v6** installed

## Step 1: Install jscodeshift (if not already installed)

```bash
npm install -g jscodeshift
```

## Step 2: Update React Router Dependencies

Update your package.json dependencies to React Router v6:

```bash
# Update React Router to version 6
npm install react-router-dom@^6.0.0

# Update React Router types for TypeScript
npm install --save-dev @types/react-router-dom@^5.3.0
```

## Step 3: Run the JSCodeshift Transform

Run the provided transform to automatically update your code:

```bash
# Run the transform on your src directory
jscodeshift -t react-router-v5-to-v6-transform.js src/ --extensions=tsx,ts,jsx,js --parser=tsx
```

### What the transform does:

1. **Updates useHistory to useNavigate**: Converts `useHistory()` hook usage
2. **Separates location from useHistory**: Extracts `location` to use `useLocation()` hook
3. **Updates navigation calls**: Converts `history.push()` and `push()` to `navigate()`
4. **Updates Redirect to Navigate**: Converts `<Redirect>` components to `<Navigate>`
5. **Updates Switch to Routes**: Converts `<Switch>` components to `<Routes>`
6. **Updates imports**: Adds necessary new imports and removes old ones

## Step 4: Manual Review and Updates

After running the transform, you'll need to manually review and update several patterns:

### 1. Route Components and Render Props

**Before (React Router v5):**
```tsx
<Route path="/admin" component={Admin} />
<Route path="/admin" render={() => <Admin />} />
<Route path="/admin">
  <Admin />
</Route>
```

**After (React Router v6):**
```tsx
<Route path="/admin" element={<Admin />} />
<Route path="/admin" element={<Admin />} />
<Route path="/admin" element={<Admin />} />
```

### 2. Conditional Routing

**Before (React Router v5):**
```tsx
<Route
  render={() =>
    isAuthenticated ? (
      <Admin />
    ) : (
      <Redirect to="/unauthorized" />
    )
  }
  path="/admin"
/>
```

**After (React Router v6):**
```tsx
<Route
  path="/admin"
  element={
    isAuthenticated ? (
      <Admin />
    ) : (
      <Navigate to="/unauthorized" replace />
    )
  }
/>
```

### 3. Nested Routes

**Before (React Router v5):**
```tsx
function App() {
  return (
    <Switch>
      <Route path="/events" component={EventRoutes} />
    </Switch>
  );
}

function EventRoutes() {
  const { path } = useRouteMatch();
  return (
    <Switch>
      <Route path={`${path}/:id`} component={Event} />
    </Switch>
  );
}
```

**After (React Router v6):**
```tsx
function App() {
  return (
    <Routes>
      <Route path="/events/*" element={<EventRoutes />} />
    </Routes>
  );
}

function EventRoutes() {
  return (
    <Routes>
      <Route path=":id" element={<Event />} />
    </Routes>
  );
}
```

### 4. useHistory Patterns

**Before (React Router v5):**
```tsx
import { useHistory } from 'react-router-dom';

function MyComponent() {
  const history = useHistory();
  const { push, location } = useHistory();
  
  const handleClick = () => {
    history.push('/new-path');
    push('/another-path');
  };
  
  console.log(location.pathname);
}
```

**After (React Router v6):**
```tsx
import { useNavigate, useLocation } from 'react-router-dom';

function MyComponent() {
  const navigate = useNavigate();
  const location = useLocation();
  
  const handleClick = () => {
    navigate('/new-path');
    navigate('/another-path');
  };
  
  console.log(location.pathname);
}
```

### 5. Replace Navigation

**Before (React Router v5):**
```tsx
history.replace('/new-path');
```

**After (React Router v6):**
```tsx
navigate('/new-path', { replace: true });
```

## Step 5: Update Your Route Structure

Your main routes file will need significant updates:

**Before (src/routes.tsx):**
```tsx
<Switch>
  <Route exact path="/">
    <Homepage />
  </Route>
  <Route path="/admin" component={Admin} />
  <Route
    render={() =>
      isAuthenticated ? <Admin /> : <Redirect to="/unauthorized" />
    }
    path="/admin"
  />
</Switch>
```

**After (src/routes.tsx):**
```tsx
<Routes>
  <Route path="/" element={<Homepage />} />
  <Route path="/admin" element={<Admin />} />
  <Route
    path="/admin"
    element={
      isAuthenticated ? <Admin /> : <Navigate to="/unauthorized" replace />
    }
  />
</Routes>
```

## Step 6: Handle Route Parameters

**Before (React Router v5):**
```tsx
import { useParams, useRouteMatch } from 'react-router-dom';

function Component() {
  const { path, url } = useRouteMatch();
  const { id } = useParams();
}
```

**After (React Router v6):**
```tsx
import { useParams } from 'react-router-dom';

function Component() {
  // useRouteMatch is removed, use relative paths in nested routes
  const { id } = useParams();
}
```

## Step 7: Test Your Application

1. **Start the development server**:
   ```bash
   npm start
   ```

2. **Test all routes**: Navigate through your application to ensure all routes work
3. **Test navigation**: Verify all buttons and links that trigger navigation work correctly
4. **Test conditional routing**: Ensure authentication-based routing works

## Common Issues and Solutions

### 1. Route Order Matters More in v6

In React Router v6, routes are matched by specificity, not order. Make sure your routes are structured correctly:

```tsx
<Routes>
  <Route path="/events/:id" element={<Event />} />
  <Route path="/events" element={<Events />} />
  <Route path="*" element={<NotFound />} />
</Routes>
```

### 2. Exact Prop Removed

The `exact` prop is no longer needed in v6. Routes are exact by default.

### 3. useRouteMatch Removed

Replace `useRouteMatch` with relative paths in nested routes or use `useLocation` and `useParams`.

### 4. History Object Changes

The history object is no longer directly accessible. Use `useNavigate` for navigation and `useLocation` for location information.

## Rollback Plan

If you need to rollback:

1. **Revert package.json changes**:
   ```bash
   npm install react-router-dom@^5.3.0
   ```

2. **Revert code changes using git**:
   ```bash
   git checkout -- src/
   ```

## Additional Resources

- [React Router v6 Migration Guide](https://reactrouter.com/en/main/upgrading/v5)
- [React Router v6 Documentation](https://reactrouter.com/en/main)
- [What's New in React Router v6](https://reactrouter.com/en/main/upgrading/v5#what-changed)
