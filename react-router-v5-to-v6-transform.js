/**
 * J<PERSON><PERSON>hift transform to migrate React Router v5 to v6
 * 
 * This transform handles:
 * 1. useHistory -> useNavigate
 * 2. history.push() -> navigate()
 * 3. history.replace() -> navigate(path, { replace: true })
 * 4. Redirect -> Navigate
 * 5. Switch -> Routes
 * 6. Route component prop -> element prop
 * 7. Route render prop -> element prop
 * 8. Extract location from useHistory -> useLocation
 */

module.exports = function transformer(fileInfo, api) {
  const j = api.jscodeshift;
  const root = j(fileInfo.source);
  
  let hasChanges = false;
  let needsUseNavigate = false;
  let needsUseLocation = false;
  let needsNavigate = false;
  let needsRoutes = false;
  let hasReactRouterImport = false;
  let reactRouterImportPath = null;

  // Find React Router imports
  const reactRouterImports = root.find(j.ImportDeclaration, {
    source: { value: 'react-router-dom' }
  });

  if (reactRouterImports.length > 0) {
    hasReactRouterImport = true;
    reactRouterImportPath = reactRouterImports.at(0);
  }

  // Transform useHistory to useNavigate and useLocation
  root.find(j.ImportSpecifier, {
    imported: { name: 'useHistory' }
  }).forEach(path => {
    // Replace useHistory import with useNavigate
    j(path).replaceWith(j.importSpecifier(j.identifier('useNavigate')));
    needsUseNavigate = true;
    hasChanges = true;
  });

  // Handle useHistory hook calls and destructuring
  root.find(j.VariableDeclarator).forEach(path => {
    const node = path.value;
    
    // Handle: const history = useHistory()
    if (
      node.init &&
      node.init.type === 'CallExpression' &&
      node.init.callee.name === 'useHistory'
    ) {
      // Replace with useNavigate
      node.init.callee.name = 'useNavigate';
      
      if (node.id.type === 'Identifier') {
        // Simple assignment: const history = useHistory()
        // Change to: const navigate = useNavigate()
        node.id.name = 'navigate';
      }
      
      needsUseNavigate = true;
      hasChanges = true;
    }
    
    // Handle: const { push, location } = useHistory()
    if (
      node.init &&
      node.init.type === 'CallExpression' &&
      node.init.callee.name === 'useHistory' &&
      node.id.type === 'ObjectPattern'
    ) {
      const properties = node.id.properties;
      const hasLocation = properties.some(prop => 
        prop.key && prop.key.name === 'location'
      );
      const hasPush = properties.some(prop => 
        prop.key && prop.key.name === 'push'
      );
      const hasReplace = properties.some(prop => 
        prop.key && prop.key.name === 'replace'
      );
      
      if (hasLocation) {
        needsUseLocation = true;
        
        // Remove location from destructuring and add separate useLocation call
        const newProperties = properties.filter(prop => 
          prop.key && prop.key.name !== 'location'
        );
        
        if (newProperties.length > 0) {
          // Still have other properties, keep the destructuring but change to useNavigate
          node.id.properties = newProperties;
          node.init.callee.name = 'useNavigate';
          needsUseNavigate = true;
        } else {
          // Only had location, remove this declaration entirely
          j(path.parent).remove();
        }
        
        // Add separate useLocation call
        const locationDeclaration = j.variableDeclaration('const', [
          j.variableDeclarator(
            j.identifier('location'),
            j.callExpression(j.identifier('useLocation'), [])
          )
        ]);
        
        // Insert the location declaration
        const statement = j(path).closest(j.VariableDeclaration);
        if (statement.length > 0) {
          j(statement.get()).insertAfter(locationDeclaration);
        }
      } else if (hasPush || hasReplace) {
        // Only navigation methods, change to useNavigate
        node.init.callee.name = 'useNavigate';
        
        // Replace push/replace with navigate
        node.id.properties = node.id.properties.map(prop => {
          if (prop.key && (prop.key.name === 'push' || prop.key.name === 'replace')) {
            return j.property('init', j.identifier('navigate'), j.identifier('navigate'));
          }
          return prop;
        });
        
        needsUseNavigate = true;
      }
      
      hasChanges = true;
    }
  });

  // Transform history.push() calls to navigate()
  root.find(j.CallExpression, {
    callee: {
      type: 'MemberExpression',
      object: { name: 'history' },
      property: { name: 'push' }
    }
  }).forEach(path => {
    const args = path.value.arguments;
    
    // Replace history.push(path) with navigate(path)
    j(path).replaceWith(
      j.callExpression(j.identifier('navigate'), args)
    );
    
    hasChanges = true;
  });

  // Transform history.replace() calls to navigate(path, { replace: true })
  root.find(j.CallExpression, {
    callee: {
      type: 'MemberExpression',
      object: { name: 'history' },
      property: { name: 'replace' }
    }
  }).forEach(path => {
    const args = path.value.arguments;
    const newArgs = [
      ...args,
      j.objectExpression([
        j.property('init', j.identifier('replace'), j.literal(true))
      ])
    ];
    
    // Replace history.replace(path) with navigate(path, { replace: true })
    j(path).replaceWith(
      j.callExpression(j.identifier('navigate'), newArgs)
    );
    
    hasChanges = true;
  });

  // Transform push() calls (from destructured useHistory)
  root.find(j.CallExpression, {
    callee: { name: 'push' }
  }).forEach(path => {
    // Replace push(path) with navigate(path)
    path.value.callee.name = 'navigate';
    hasChanges = true;
  });

  // Transform Redirect to Navigate
  root.find(j.ImportSpecifier, {
    imported: { name: 'Redirect' }
  }).forEach(path => {
    j(path).replaceWith(j.importSpecifier(j.identifier('Navigate')));
    needsNavigate = true;
    hasChanges = true;
  });

  // Transform <Redirect to="/path" /> to <Navigate to="/path" replace />
  root.find(j.JSXElement, {
    openingElement: {
      name: { name: 'Redirect' }
    }
  }).forEach(path => {
    const openingElement = path.value.openingElement;
    const closingElement = path.value.closingElement;
    
    // Change tag name
    openingElement.name.name = 'Navigate';
    if (closingElement) {
      closingElement.name.name = 'Navigate';
    }
    
    // Add replace prop
    const hasReplace = openingElement.attributes.some(attr => 
      attr.name && attr.name.name === 'replace'
    );
    
    if (!hasReplace) {
      openingElement.attributes.push(
        j.jsxAttribute(j.jsxIdentifier('replace'))
      );
    }
    
    hasChanges = true;
  });

  // Transform Switch to Routes
  root.find(j.ImportSpecifier, {
    imported: { name: 'Switch' }
  }).forEach(path => {
    j(path).replaceWith(j.importSpecifier(j.identifier('Routes')));
    needsRoutes = true;
    hasChanges = true;
  });

  // Transform <Switch> to <Routes>
  root.find(j.JSXElement, {
    openingElement: {
      name: { name: 'Switch' }
    }
  }).forEach(path => {
    const openingElement = path.value.openingElement;
    const closingElement = path.value.closingElement;
    
    openingElement.name.name = 'Routes';
    if (closingElement) {
      closingElement.name.name = 'Routes';
    }
    
    hasChanges = true;
  });

  // Add missing imports
  if (hasChanges && hasReactRouterImport && reactRouterImportPath) {
    const importDeclaration = reactRouterImportPath.get().value;
    const existingSpecifiers = importDeclaration.specifiers;
    const newSpecifiers = [...existingSpecifiers];
    
    if (needsUseNavigate && !existingSpecifiers.some(spec => 
      spec.imported && spec.imported.name === 'useNavigate'
    )) {
      newSpecifiers.push(j.importSpecifier(j.identifier('useNavigate')));
    }
    
    if (needsUseLocation && !existingSpecifiers.some(spec => 
      spec.imported && spec.imported.name === 'useLocation'
    )) {
      newSpecifiers.push(j.importSpecifier(j.identifier('useLocation')));
    }
    
    if (needsNavigate && !existingSpecifiers.some(spec => 
      spec.imported && spec.imported.name === 'Navigate'
    )) {
      newSpecifiers.push(j.importSpecifier(j.identifier('Navigate')));
    }
    
    if (needsRoutes && !existingSpecifiers.some(spec => 
      spec.imported && spec.imported.name === 'Routes'
    )) {
      newSpecifiers.push(j.importSpecifier(j.identifier('Routes')));
    }
    
    importDeclaration.specifiers = newSpecifiers;
  }

  return hasChanges ? root.toSource({ quote: 'single' }) : fileInfo.source;
};

module.exports.parser = 'tsx';
