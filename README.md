# Gambyl (Frontend)

This project holds the UI code for the Gambyl - Sport Betting Exchange.

## Table of contents

-   [1. Project Description](#1-Project-Description)
-   [2. Technologies](#2-technologies)
-   [3. Dependencies](#3-dependencies)
-   [4. Setup](#4-setup)
-   [5. Running the UI locally](#5-Running-the-UI-locally)
-   [6. Getting the codegen from AWS](#6-Getting-the-codegen-from-aws)
-   [7. Scripts overview](#7-Scripts-overview)

## 1. Project Description

This project holds the UI code for the Gambyl - Sport Betting Exchange, builted with React, Typescript and the DAML UI libraries.

## 2. Technologies

The technologies used to develop this system which it depends on were:

-   @daml/hub-react @1.1.5,
-   @daml/ledger @2.4.0,
-   @daml/react @2.4.0,
-   @daml/types @2.4.0,
-   @fortawesome/fontawesome-svg-core @^1.2.36,
-   @fortawesome/free-brands-svg-icons @^5.15.4,
-   @fortawesome/free-solid-svg-icons @^5.15.4,
-   @fortawesome/react-fontawesome @^0.1.16,
-   @types/node @^12.0.0,
-   @types/numeral @^2.0.2,
-   @types/react @^17.0.0,
-   @types/react-datepicker @^4.1.7,
-   @types/react-dom @^17.0.0,
-   @types/react-gtm-module @^2.0.1,
-   @types/react-router-dom @^5.3.1,
-   @types/react-router-hash-link @^2.4.5,
-   @types/uuid @^8.3.3,
-   formik @^2.2.9,
-   i18next @^21.6.14,
-   jwt-decode @^3.1.2,
-   jwt-simple @^0.5.6,
-   mathjs @^10.1.1,
-   moment-timezone @^0.5.40,
-   numeral @^2.0.6,
-   react @^16.12.0,
-   react-currency-input-field @^3.6.0,
-   react-datepicker @^4.3.0,
-   react-dom @^16.12.0,
-   react-flags-select @^2.1.2,
-   react-gtm-module @^2.0.11,
-   react-i18next @^11.15.6,
-   react-idle-timer @^4.6.4,
-   react-loader-spinner @^4.0.0,
-   react-query @^3.27.0,
-   react-responsive-carousel @^3.2.22,
-   react-router-dom @^5.3.0,
-   react-router-hash-link @^2.4.3,
-   react-scripts @4.0.3,
-   react-switch @^7.0.0,
-   react-toastify @^8.1.0,
-   react-tooltip @^4.2.21,
-   sass @^1.43.2,
-   typescript @^4.1.2,
-   uuid @^8.3.2,
-   web-vitals @^1.0.1,
-   yup @^0.32.11

## 3. Dependencies

To run this application you will need to have node.js and yarn or npm installed on your machine. We would recommend to use yarn over npm. You can follow this [Link](https://yarnpkg.com/getting-started/install) to install Yarn on your machine.

## 4. Setup

To setup correctly the UI, you will need to get the UI aligned with the contract templates from the DAML code. For that reason the setup of the UI should be the last step you do when running locally or deploying all the Gambyl DAML code.

In order to successfully build the UI, you will need to generate the codegen on the Gambyl DAML project.

On the Gambyl DAML readme you will be able to find all the needed steps to build, generate the codegen and running the sandbox locally. The codegen will be output on the daml.js directory, and they contain the Typescript bidings for all the needed contract templates. You need to copy the daml.js folder and paste it on the root of the folder containing both of the Gambyl Frontend project. You may also fetch the bindings of the daml code deployed on the dev aws env. You can see the steps to do so on point 6 of this readme.

If it's the first time you are running the project run:

```bash
yarn install or npm install
```

Although, if you have installed a previous version of the daml.js bindings (it's generated everytime you build a new ledger), please run first:

```bash
rm -rf node_modules/
```

or delete the node_modules folder from the folder containing project.

And only then you can run:

```bash
yarn install or npm install
```

## 5. Running the UI locally

After finishing the installation installation process. You can start the UI by running the command:

```bash
yarn start or npm run start
```

Then a page will open on your browser on the path:

    http://localhost:3000.

To stop the application from the terminal where it's running:

```bash
ctrl+c
```

## 6. Getting the codegen from AWS

First you will need to have a access token to the gitlab docker registry. Then you can go to Gambyl Exchange repository and on the container registry copy latest image path. Once you preform that step you wil run the following command:

```bash
docker pull registry.gitlab.com/intellecteu/blockchain/clients/gambyl/gambyl-exchange/gambyl-frontend:latest
```

Then run

```bash
docker  run -d --name frontend registry.gitlab.com/intellecteu/blockchain/clients/gambyl/gambyl-exchange/gambyl-frontend:latest sleep 300
```

Now inside of this docker container you will have access to the bindings on the daml.js folder. You can get them with the command.

```bash
docker cp frontend:/src/gambyl-frontend/daml.js/ ./daml.js/
```

This command will copy the daml.js folder to your local machine, but please remember to move the folder to the write directory to install it correctly on the FE. Now you will just need to stop and remove the container. For that you can run:

```bash
docker stop frontend
```

Followed by:

```bash
docker rm frontend
```

## 7. Scripts Overview

This project includes predefined commands in the `package.json` file to streamline development and production workflows. Below is an explanation of the two key commands:

#### `npm run start`

This command is used to start the development server. It sets various environment variables necessary for the application to function correctly during development. Here's a breakdown:

-   **NODE_OPTIONS=--openssl-legacy-provider**: Ensures compatibility with older OpenSSL providers. Only needed if you use node at version higher than 20.X.X
-   **REACT_APP_PUBLIC_TOKEN**: Specifies the public token for API calls. Replace `someurl` with your actual token.
-   **REACT_APP_AUTH0_DOMAIN**: Sets the Auth0 domain for authentication.
-   **REACT_APP_AUTH0_CLIENT_ID**: Defines the Auth0 client ID used for authentication.
-   **REACT_APP_AUTH0_CALLBACK_URL**: Specifies the callback URL for Auth0 authentication.
-   **REACT_APP_IS_PROD=false**: Indicates the app is running in a development environment.
-   **react-scripts start**: Starts the React development server with hot-reloading enabled.

**Usage**:

```bash
npm run start
```

This launches the app locally for development purposes.

---

#### `npm run build`

This command is used to build the application for production or staging environments. It prepares an optimized bundle of the app, ensuring it's ready for deployment. Environment variables are similarly configured:

-   **REACT_APP_AUTH0_DOMAIN**: Sets the Auth0 domain for authentication.
-   **REACT_APP_AUTH0_CLIENT_ID**: Defines the Auth0 client ID used for authentication.
-   **REACT_APP_AUTH0_CALLBACK_URL**: Specifies the callback URL for Auth0 authentication.
-   **REACT_APP_IS_PROD=false**: While this is set to `false` here, you may want to set it to `true` for production builds.
-   **react-scripts build**: Compiles the React app into static files for production deployment.

**Usage**:

```bash
npm run build
```

This generates the production-ready assets in the `build` directory.

---

### Notes:

-   Replace placeholder values (e.g., `someurl`, `someID`) with the actual values required for your application.
-   Adjust `REACT_APP_IS_PROD` to `true` when building for production to reflect the correct environment configuration.
