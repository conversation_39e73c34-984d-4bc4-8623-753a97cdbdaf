@import 'colors';

.documentContainer {
    border-radius: 6px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
    width: 90%;
    max-width: 1500px;
    margin: 20px auto;
    padding: 20px;
}

.documents {
    color: $darkGrey;

    &__purple {
        color: $purple;
    }

    section {
        margin-top: 40px;
    }

    h1 {
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 1.75rem;
        line-height: 2rem;
        margin-bottom: 30px;
        text-align: center;
    }

    h2 {
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 1.5rem;
        line-height: 1.75rem;
        margin-bottom: 20px;
        text-align: center;

        i {
            font-size: 32px;
        }
    }

    p {
        font-family: OpenSans, sans-serif;
        font-size: 1rem;
        line-height: 1.5rem;
        margin-bottom: 15px;
    }
    ul,
    ol {
        padding-left: 40px;
        font-family: OpenSans, sans-serif;
        font-size: 1rem;
        line-height: 1.25rem;
        margin-bottom: 15px;
    }

    a {
        color: $purple;
    }
    .center {
        text-align: center;
    }

    .btn {
        width: 260px;
        margin: 0 auto;
        display: block;
        padding-left: 15px;
        padding-right: 15px;
    }

    table {
        border: 1px solid $darkGrey;
        margin-bottom: 20px;
        border-spacing: 0;
        font-family: OpenSans, sans-serif;
        font-size: 1rem;
        line-height: 1.25rem;
        th,
        td {
            border: 1px solid $darkGrey;
            padding: 5px;
        }
    }

    // @media (max-width: 426px) {
    //     h1 {
    //         font-size: 30px;
    //         margin-bottom: 20px;
    //     }
    //     h2 {
    //         font-size: 20px;
    //         margin-bottom: 10px;

    //         i {
    //             font-size: 20px;
    //         }
    //     }
    // }
}
