@import './colors';

.btn {
    color: $white;
    cursor: pointer;
    text-decoration: none;
    padding: 8px 5px;
    margin: 1px 5px;
    width: 103px;
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    font-family: 'Montserrat-Bold', sans-serif;

    &:disabled {
        border: 1px solid #d3dce5;
        border-radius: 6px;
        background: #efefef;
        color: #8298ab;
        cursor: not-allowed;
    }

    &:hover {
        opacity: 0.8;
    }
    &__secondary {
        color: $orange;
        background-color: transparent;
        border: 1px solid $orange;
    }
    &__primary {
        background-color: $purple;
        border-color: $purple;
    }
    &__green {
        background: $successgreen;
        border-color: $successgreen;
    }
    &__grey {
        border: 1px solid $grayInpuBorder;
        color: $gray;
    }
    &__danger {
        background: $rederror;
        border-color: $rederror;
    }
}

.btn__a {
    a {
        color: white;
        text-decoration: none;

        &:visited {
            color: white;
            text-decoration: none;
        }
    }
}

.registerbtn {
    &__nextStep {
        background: $purple;
        border: none;
        border-radius: 6px;
        color: $white;
        cursor: pointer;
        //font-family: Montserrat;
        font-size: 0.813rem;
        font-weight: bold;
        height: 50px;
        line-height: 1.563rem;
        margin-top: 20px;
        text-align: center;
        width: 100%;
        transition: filter 0.2s;

        &:hover {
            filter: brightness(0.9);
        }

        &:disabled {
            border: 1px solid #d3dce5;
            border-radius: 6px;
            background: #efefef;
            color: #8298ab;
            cursor: not-allowed;
        }
    }

    &__previousStep {
        border: 1px solid #d3dce5;
        border-radius: 6px;
        color: #8298ab;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 30px;
        padding: 15px 20px;
        //font-family: Montserrat;
        font-size: 0.813rem;
        font-weight: bold;
        height: 50px;
        line-height: 1.563rem;
        text-align: center;
    }

    &__finalStep {
        background: $successgreen;
        border: none;
        border-radius: 6px;
        color: $white;
        cursor: pointer;
        //font-family: Montserrat;
        font-size: 0.813rem;
        font-weight: bold;
        height: 50px;
        line-height: 1.563rem;
        margin-top: 20px;
        text-align: center;
        width: 100%;
        transition: filter 0.2s;

        &:hover {
            filter: brightness(0.9);
        }

        &:disabled {
            border: 1px solid #d3dce5;
            border-radius: 6px;
            background: #efefef;
            color: #8298ab;
            cursor: not-allowed;
        }
    }
}
