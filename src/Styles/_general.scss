@import 'buttons';

.App {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main__container {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    overflow-y: scroll;

    @media (max-width: 892px) {
        /* Hide scrollbar for Chrome, Safari and Opera */
        &::-webkit-scrollbar {
            display: none;
        }

        /* Hide scrollbar for IE, Edge and Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
}

.overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2;

    @media (min-width: 930px) {
        display: none;
    }
}

.app__container {
    display: flex;
    max-height: calc(100vh - 70px);
    min-height: calc(100vh - 70px);
}

.content__container {
    flex: 1 1 100%;
}

.marginTop60 {
    margin-top: 3.75rem;
}

.marginTop20 {
    margin-top: 20px;
}

.marginSportCard {
    margin: 15px 0;
}

.pagePadding {
    padding: 29px 16px;
}

.width100 {
    width: 100%;
}

.tooltipMaxWidthMobile {
    @media (max-width: 930px) {
        max-width: 170px;
    }
}

.tooltipTableHeader {
    font-size: 0.8rem !important;
    text-transform: none !important;
    font-weight: normal !important;
    max-width: 220px;
    @media (max-width: 930px) {
        max-width: 170px;
    }
}

.reactTable__pagination {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;

    &--available {
        color: $purple;
        cursor: pointer;
        font-weight: bold;
        font-size: 1.5rem;
        border: none;
        outline: none;
        background-color: transparent;
    }

    &--disabled {
        color: #8298ab;
        cursor: not-allowed;
        font-weight: bold;
        font-size: 1.5rem;
        border: none;
        outline: none;
        background-color: transparent;
    }
}

.marginTop0 {
    margin-top: 0px !important;
}
