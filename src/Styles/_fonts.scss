@font-face {

  font-family: 'OpenSansLight';

  src: url('../fonts/OpenSans-Light-webfont.eot');

  src: url('../fonts/OpenSans-Light-webfont.eot?#iefix') format('embedded-opentype'),
  url('../fonts/OpenSans-Light-webfont.woff') format('woff'),
  url('../fonts/OpenSans-Light-webfont.ttf') format('truetype'),
  url('../fonts/OpenSans-Light-webfont.svg#OpenSansLight') format('svg');

  font-weight: normal;
  font-style: normal;
}


@font-face {
  font-family: 'Montserrat-Bold';

  src: url('../fonts/Montserrat-Bold.eot');

  src: url('../fonts/Montserrat-Bold.eot?#iefix') format('embedded-opentype'),
  url('../fonts/Montserrat-Bold.woff') format('woff'),
  url('../fonts/Montserrat-Bold.ttf') format('truetype'),
  url('../fonts/Montserrat-Bold.svg#Montserrat-Bold') format('svg');

  font-weight: normal;
  font-style: normal;
}


@font-face {
  font-family: 'OpenSansLightItalic';

  src: url('../fonts/OpenSans-LightItalic-webfont.eot');

  src: url('../fonts/OpenSans-LightItalic-webfont.eot?#iefix') format('embedded-opentype'),
  url('../fonts/OpenSans-LightItalic-webfont.woff') format('woff'),
  url('../fonts/OpenSans-LightItalic-webfont.ttf') format('truetype'),
  url('../fonts/OpenSans-LightItalic-webfont.svg#OpenSansLightItalic') format('svg');

  font-weight: normal;
  font-style: normal;

}


@font-face {
  font-family: 'Montserrat-Medium';

  src: url('../fonts/MontserratMedium.eot');

  src: url('../fonts/MontserratMedium.eot?#iefix') format('embedded-opentype'),
  url('../fonts/MontserratMedium.woff') format('woff'),
  url('../fonts/MontserratMedium.ttf') format('truetype'),
  url('../fonts/MontserratMedium.svg#Montserrat-Medium') format('svg');

  font-weight: normal;
  font-style: normal;
}


@font-face {
  font-family: 'OpenSansRegular';

  src: url('../fonts/OpenSans-Regular-webfont.eot');

  src: url('../fonts/OpenSans-Regular-webfont.eot?#iefix') format('embedded-opentype'),
  url('../fonts/OpenSans-Regular-webfont.woff') format('woff'),
  url('../fonts/OpenSans-Regular-webfont.ttf') format('truetype'),
  url('../fonts/OpenSans-Regular-webfont.svg#OpenSansRegular') format('svg');

  font-weight: normal;
  font-style: normal;
}


@font-face {
  font-family: 'OpenSansBold';

  src: url('../fonts/OpenSans-Bold-webfont.eot');

  src: url('../fonts/OpenSans-Bold-webfont.eot?#iefix') format('embedded-opentype'),
  url('../fonts/OpenSans-Bold-webfont.woff') format('woff'),
  url('../fonts/OpenSans-Bold-webfont.ttf') format('truetype'),
  url('../fonts/OpenSans-Bold-webfont.svg#OpenSansRegular') format('svg');

  font-weight: normal;
  font-style: normal;
}


@font-face {
  font-family: 'OpenSansSemiBold';

  src: url('../fonts/OpenSans-Semibold-webfont.eot');

  src: url('../fonts/OpenSans-Semibold-webfont.eot?#iefix') format('embedded-opentype'),
  url('../fonts/OpenSans-Semibold-webfont.woff') format('woff'),
  url('../fonts/OpenSans-Semibold-webfont.ttf') format('truetype'),
  url('../fonts/OpenSans-Semibold-webfont.svg#OpenSansRegular') format('svg');

  font-weight: normal;
  font-style: normal;
}


@font-face {
  font-family: 'Montserrat-Regular';

  src: url('../fonts/MontserratRegular.eot');

  src: url('../fonts/MontserratRegular.eot?#iefix') format('embedded-opentype'),
  url('../fonts/MontserratRegular.woff') format('woff'),
  url('../fonts/MontserratRegular.ttf') format('truetype'),
  url('../fonts/MontserratRegular.svg#Montserrat-Regular') format('svg');

  font-weight: normal;
  font-style: normal;
}


@font-face {
  font-family: 'Montserrat-SemiBold';

  src: url('../fonts/Montserrat-SemiBold.eot');

  src: url('../fonts/Montserrat-SemiBold.eot?#iefix') format('embedded-opentype'),
  url('../fonts/Montserrat-SemiBold.woff') format('woff'),
  url('../fonts/Montserrat-SemiBold.ttf') format('truetype'),
  url('../fonts/Montserrat-SemiBold.svg#Montserrat-SemiBold') format('svg');

  font-weight: normal;
  font-style: normal;
}