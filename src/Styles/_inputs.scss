@import './colors';

.signup {
    &__container {
        background-attachment: fixed;
        background-color: $black;
        background-image: url('../Assets/coverBGRegister.png');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        font-family: 'Montserrat-Bold', sans-serif;
        gap: 100px;
        min-height: 100vh;
        align-items: center;
        display: flex;
        justify-content: center;
        width: 100%;
    }
}

.form {
    &__container {
        background: $white;
        border-radius: 5px;
        padding: 60px;
        max-width: 640px;
        max-height: 832px;

        h2 {
            font-size: 2.2rem;
            line-height: 2.75rem;
        }
    }
}

.step {
    &__text {
        font-weight: bold;
        line-height: 1.25rem;
        padding: 20px 0;
    }
}

.register {
    &__wrapper {
        position: relative;

        svg {
            position: absolute;
            font-size: 18px;
            top: 35px;
            right: 20px;
        }
    }
    &__input {
        border: 1px solid $grayInpuBorder;
        border-radius: 6px;
        color: $black;
        font-family: 'Open Sans', sans-serif;
        font-size: 1rem;
        font-style: normal;
        font-weight: normal;
        height: 50px;
        line-height: 1.25rem;
        margin-top: 20px;
        padding: 0 20px;
        width: 100%;
        transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

        &:focus {
            border-color: $blueShaddow;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
        }

        &::placeholder {
            color: $black;
            opacity: 1;
        }

        &[type='checkbox'] {
            accent-color: $purple;
            cursor: pointer;
            height: 15px;
            padding: 0;
            margin-top: 0;
            vertical-align: middle;
            width: 25px;
            &:focus {
                outline: 0;
                box-shadow: none;
            }
        }
    }

    &__checkboxContainer {
        display: flex;
        gap: 10px;
        margin-top: 20px;

        label {
            font-family: 'Open Sans', sans-serif;
            font-style: normal;
            font-weight: normal;
            font-size: 0.85rem;
            line-height: 1.5rem;
            display: inline-block;
            vertical-align: middle;
        }
    }

    &__error {
        color: $rederror;
        font-size: 0.725rem;
        padding: 5px 0 0 0;
    }

    &__btnContainer {
        display: flex;
        flex-direction: column;
        gap: 20px;
        align-items: center;
        justify-content: center;
    }
}

.benefits {
    &__container {
        color: $white;
        line-height: 1.563rem;
        font-family: 'Open Sans', sans-serif;

        h4 {
            font-size: 1.25rem;
            margin-bottom: 30px;
        }

        p {
            font-size: 1.125rem;
            font-weight: 300;
            margin-bottom: 19px;
        }
    }
}

.react-datepicker__day--selected {
    background-color: $purple;
}

@media (max-width: 1020px) {
    .signup {
        &__container {
            min-height: 100vh;
            align-items: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 50px;
        }
    }

    .form {
        &__container {
            padding: 40px;
            max-width: 470px;
        }
    }
}

@media (max-width: 500px) {
    .signup {
        &__container {
            min-height: 100vh;
            align-items: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
    }

    .form {
        &__container {
            padding: 30px;
            max-width: 370px;
        }
    }

    .benefits {
        &__container {
            display: none;
        }
    }
}
