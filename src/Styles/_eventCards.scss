@import './colors';
/*DEPRECATED OLD STYLES*/
.dashboard__card {
    .scrollUpBtn {
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
        color: $white;
        background-color: $purple;
        color: $white;
        cursor: pointer;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 50%;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
            border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        font-family: 'Montserrat-Bold', sans-serif;
        border: none;
    }

    .scrollToBottom {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        &__btn {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            color: $white;
            background-color: $purple;
            color: $white;
            cursor: pointer;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 50%;
            transition: color 0.15s ease-in-out,
                background-color 0.15s ease-in-out,
                border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            font-family: 'Montserrat-Bold', sans-serif;
            border: none;
        }
    }

    &__title {
        display: flex;
        align-items: center;
        margin-bottom: 7px;
        @media (max-width: 1014px) {
            padding-left: 2rem;
            padding-right: 2rem;
        }
        &__icon {
            display: flex;
            flex-direction: column;
            color: $purple;
            font-size: 1.8rem;
            margin-right: 19px;
            svg {
                fill: $purple;
                width: 1em;
                path {
                    fill: $purple;
                }
            }
            span {
                color: $darkGrey;
                font-family: Open Sans, sans-serif;
                font-style: normal;
                font-weight: normal;
                font-size: 0.8rem;
                line-height: 31px;
            }
        }
        h3 {
            font-family: Montserrat, sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 1.8rem;
            line-height: 3rem;
            color: $darkGrey;
            word-break: break-word !important;
        }
    }
    h5 {
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 0.8rem;
        line-height: 20px;
        letter-spacing: 1px;
        text-transform: uppercase;
        color: $darkGrey;
        margin-top: 27px;
        @media (max-width: 1014px) {
            display: none;
        }
    }

    &__typeDiviser {
        margin: 15px 0 0 0;
    }

    &__betType {
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 0.9rem;
        text-transform: uppercase;
        color: $darkGrey;
        padding: 15px 0;
    }

    &__betWrapper {
        display: grid;
        grid-template-columns: repeat(3, 200px);
        justify-items: start;
        align-items: self-end;
        gap: 20px;

        @media (max-width: 1014px) {
            grid-template-columns: repeat(1, 1fr);
        }
    }
    &__betWrapper2 {
        display: grid;
        grid-template-columns: repeat(2, 200px);
        align-items: self-end;
        gap: 20px;

        @media (max-width: 1014px) {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    &__row {
        display: flex;
        flex-direction: column;
        border-radius: 6px;
        width: 100%;
        padding: 16px;
        flex-wrap: wrap;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);

        @media (max-width: 1014px) {
            padding-bottom: 0;
            padding-left: 32px;
            padding-right: 32px;
        }

        &.green {
            background: $lightGreen;
        }
        &__left {
            @media (max-width: 1014px) {
                width: 100%;
                padding-bottom: 15px;
            }

            &__linkPage {
                font-family: Montserrat, sans-serif;
                font-style: normal;
                font-weight: bold;
                font-size: 1rem;
                line-height: 25px;
                color: $purple;
                text-decoration: none;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 10px;
                cursor: pointer;
            }

            h4 {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-family: Montserrat, sans-serif;
                font-style: normal;
                font-weight: bold;
                font-size: 1.25rem;
                line-height: 25px;
                color: $darkGrey;
                @media (max-width: 1014px) {
                    //font-size: 15px;
                    display: flex;
                    align-items: center;
                }
                svg {
                    margin-right: 8px;
                }

                .plus-icon {
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    background: $lightPurple;
                    position: relative;
                    display: none;
                    margin-left: auto;
                    cursor: pointer;
                    @media (max-width: 1014px) {
                        display: block;
                    }
                    span {
                        width: 14px;
                        height: 2px;
                        background: $purple;
                        display: block;
                        position: absolute;
                        left: calc(50% - 7px);
                        top: calc(50% - 1px);
                        transition: transform 0.3s;
                        &:last-of-type {
                            transform: rotate(-90deg);
                            &.open {
                                transform: rotate(0deg);
                            }
                        }
                    }
                }

                a {
                    font-family: Montserrat, sans-serif;
                    font-style: normal;
                    font-weight: bold;
                    font-size: 1.25rem;
                    line-height: 25px;
                    color: $darkGrey;
                    text-decoration: none;
                }
            }
            p {
                font-family: Open Sans, sans-serif;
                font-style: normal;
                font-weight: normal;
                font-size: 0.93rem;
                line-height: 20px;
                color: $darkGrey;
                margin-top: 8px;
            }
            &__bottom {
                font-family: Montserrat, sans-serif;
                font-style: normal;
                font-weight: bold;
                font-size: 0.3rem;
                line-height: 20px;
                letter-spacing: 1px;
                text-transform: uppercase;
                margin-top: 6px;
                min-width: 370px;
                @media (max-width: 1014px) {
                    justify-content: space-between;
                    display: flex;
                    min-width: auto;
                }
                strong {
                    color: $darkGrey;
                    width: 50%;
                    display: inline-block;
                }
                span {
                    color: $purple;
                }
            }
        }
        //HERE CHANGED FOR NEW ALIGNMENT
        &__right {
            @media (max-width: 1014px) {
                flex-direction: column;
                height: 0;
                margin-top: 0;
                margin-bottom: 0;
                justify-content: center;
                background: $white;
                overflow: hidden;
                transition: height 0.3s;
                //HERE CHANGED FOR NEW AIGNMENT
                &.open {
                    height: auto;
                }
            }
            &__bet {
                cursor: pointer;
                display: flex;
                flex-direction: column;
                align-items: center;
                //margin-right: 16px;
                @media (max-width: 1014px) {
                    flex-direction: row;
                    justify-content: space-between;
                    width: 100%;
                    margin-bottom: 8px;
                }
                &:last-child {
                    margin-right: 0;
                }
                &__title {
                    font-family: Open Sans, sans-serif;
                    font-style: normal;
                    font-weight: bold;
                    font-size: 0.8rem;
                    line-height: 20px;
                    text-align: center;
                    color: $darkGrey;
                    margin-bottom: 8px;
                    @media (max-width: 1014px) {
                        font-weight: bold;
                        font-size: 13px;
                        //HERE CHANGED FOR NEW AIGNMENT
                        text-align: left;
                    }
                }
                &__wrapper {
                    display: flex;

                    &__itemMakeOffer {
                        display: flex;
                        flex-direction: column;
                        border-radius: 6px;
                        width: 100px;
                        height: 50px;
                        gap: 8px;
                        justify-content: center;
                        align-items: center;
                        font-family: Open Sans, sans-serif;
                        font-style: normal;
                        font-weight: normal;
                        text-align: center;
                        transition: filter 0.2s;

                        &:hover {
                            filter: brightness(110%);
                        }

                        @media (max-width: 1014px) {
                            width: 53px;
                        }
                        strong {
                            font-size: 13px;
                        }

                        &.purple {
                            background: $purple;
                            margin-right: 8px;
                            color: $white;
                        }
                        &.orange {
                            background: $orange;
                            color: $darkGrey;
                        }
                    }

                    &__item {
                        display: flex;
                        flex-direction: column;
                        border-radius: 6px;
                        width: 100px;
                        height: 50px;
                        justify-content: flex-end;
                        align-items: center;
                        font-family: Open Sans, sans-serif;
                        font-style: normal;
                        font-weight: normal;
                        font-size: 0.87rem;
                        line-height: 24px;
                        text-align: center;
                        transition: filter 0.2s;

                        &:hover {
                            filter: brightness(110%);
                        }

                        @media (max-width: 1014px) {
                            width: 53px;
                        }
                        strong {
                            font-size: 1rem;
                            line-height: 10px;
                        }

                        &.purple {
                            background: $purple;
                            margin-right: 8px;
                            color: $white;
                        }
                        &.orange {
                            background: $orange;
                            color: $darkGrey;
                        }

                        &.disabled {
                            background: gray;
                            color: $grayInpuBorder;
                            cursor: not-allowed;
                            margin-right: 8px;
                        }
                    }
                }
            }
        }
    }
}
