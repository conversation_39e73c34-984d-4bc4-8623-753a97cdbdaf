import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import { resources } from './translationResources'

const languange = localStorage.getItem('AppLang')?.length
    ? localStorage.getItem('AppLang')?.toString()
    : 'US'

i18n.use(initReactI18next).init({
    resources,
    lng: languange,
    fallbackLng: 'US',
    interpolation: {
        escapeValue: false
    }
})

export default i18n
