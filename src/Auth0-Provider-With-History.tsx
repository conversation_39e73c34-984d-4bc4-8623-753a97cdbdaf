import { AppState, Auth0Provider } from '@auth0/auth0-react'
import { Auth0EnvVariables } from 'config'
import React, { PropsWithChildren } from 'react'
import { useHistory } from 'react-router-dom'

interface Auth0ProviderWithConfigProps {
    children: React.ReactNode
}

export const Auth0ProviderWithHistory = ({
    children
}: PropsWithChildren<Auth0ProviderWithConfigProps>): JSX.Element | null => {
    const history = useHistory()
    const { domain, clientId, redirectUri } = Auth0EnvVariables

    const onRedirectCallback = (appState?: AppState) => {
        history.push(appState?.returnTo || window.location.pathname)
    }

    if (!(domain && clientId && redirectUri)) {
        return null
    }

    return (
        <Auth0Provider
            domain={domain}
            clientId={clientId}
            authorizationParams={{
                redirect_uri: redirectUri,
                prompt: 'select_account'
            }}
            onRedirectCallback={onRedirectCallback}
        >
            {children}
        </Auth0Provider>
    )
}
