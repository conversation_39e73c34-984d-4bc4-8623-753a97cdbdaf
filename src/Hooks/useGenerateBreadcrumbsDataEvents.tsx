import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { CreateEvent } from '@daml/ledger'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import { useI18LanguageContext } from 'State/LanguageState'
import { baseName, checkIfIsCustom } from 'Containers/MarketPages/Utils'
import { useTranslation } from 'react-i18next'
import { checkIfNonSportEvent } from 'Utils/checkIfNonSportEvent'
import { getGeoPath, getSubmarketPath } from 'Utils/getPathFromSubmarket'
import { getLanguage } from 'Utils/getLanguage'
import BreadcrumbElipsis from 'Components/Breadcrumbs/BreadcrumbElipsis'

export interface IGenerateBreadcrumbsData {
    event: readonly CreateEvent<EventInstrument, EventInstrument.Key, string>[]
    levelOfBreadcrumbs: 'market' | 'geography' | 'tournament' | 'game'
}

const useGenerateBreadcrumbsDataEvents = ({
    event,
    levelOfBreadcrumbs
}: IGenerateBreadcrumbsData) => {
    //HOOKS
    const { BR, MX, US } = useSportTranslationsContext()
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()

    //EARLY SAFETY RETURN CONDITION
    if (!event.length) {
        return []
    }

    //EVENT DATA
    const {
        details: { eventTitle, submarkets, geography, market }
    } = event[0]?.payload
    const language = getLanguage({ lang, BR, MX, US })
    //BASE FOR BREADCRUMB
    let baseBreadcrumb = [
        {
            pageName: t('HExchange'),
            path: '/',
            isActive: false
        }
    ]

    //ROUTE PATH LOGIC
    //MARKET
    const marketTag = checkIfNonSportEvent(market)
    const routeToMarket = `/events/${marketTag}`
    const marketCodeToTranslate = checkIfNonSportEvent(market)
    const translatedMarketName = baseName(language, marketCodeToTranslate)
    const translatedIfCustom = checkIfIsCustom(
        marketCodeToTranslate,
        translatedMarketName
    )
    const marketDisplayName = isNaN(Number(marketCodeToTranslate))
        ? t(translatedIfCustom)
        : (translatedIfCustom as string)

    //GEOGRAPHY
    let geo = geography.value
    const routeToGeography = getGeoPath(geo, marketTag)
    //TOURNAMENT
    const tournament = submarkets.filter(s => s.tag === 'Tournament')[0].value
    const routeToTournament = getSubmarketPath(tournament, marketTag, geo)
    //EVENT
    const eventTitleTranslated = getEventTitleFromLang(eventTitle, lang)
    // BREADCRUMB LOGIC
    const breadcrumbLevels = {
        market: [
            {
                pageName: marketDisplayName,
                path: routeToMarket,
                isActive: true
            }
        ],
        geography: [
            {
                pageName: <BreadcrumbElipsis content={marketDisplayName} />,
                path: routeToMarket,
                isActive: false
            },
            { pageName: geo, path: routeToGeography, isActive: true }
        ],
        tournament: [
            {
                pageName: <BreadcrumbElipsis content={marketDisplayName} />,
                path: routeToMarket,
                isActive: false
            },
            {
                pageName: <BreadcrumbElipsis content={geo} />,
                path: routeToGeography,
                isActive: false
            },
            {
                pageName: tournament,
                path: routeToTournament,
                isActive: true
            }
        ],
        game: [
            {
                pageName: <BreadcrumbElipsis content={marketDisplayName} />,
                path: routeToMarket,
                isActive: false
            },
            {
                pageName: <BreadcrumbElipsis content={geo} />,
                path: routeToGeography,
                isActive: false
            },
            {
                pageName: <BreadcrumbElipsis content={tournament} />,
                path: routeToTournament,
                isActive: false
            },
            {
                pageName: eventTitleTranslated,
                path: '/',
                isActive: true
            }
        ]
    }

    return [...baseBreadcrumb, ...(breadcrumbLevels[levelOfBreadcrumbs] || [])]
}

export { useGenerateBreadcrumbsDataEvents }
