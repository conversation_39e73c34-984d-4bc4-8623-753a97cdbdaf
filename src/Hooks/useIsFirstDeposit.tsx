import React from 'react'
import { useQuery } from '@daml/react'
import { Actionable } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { TransactionHistory } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'

export default function useIsFirstDeposit(partyLedger: string) {
    const [isFirstDeposit, setIsFirstDeposit] = React.useState(false)
    const {
        loading: isLoadingFirstDeposit,
        contracts: transactionHistoryContracts
    } = useQuery(
        TransactionHistory,
        () => {
            return {
                customer: partyLedger,
                transactionType: Actionable.Deposit
            }
        },
        [partyLedger]
    )

    React.useEffect(() => {
        if (
            !isLoadingFirstDeposit &&
            transactionHistoryContracts?.length === 0
        ) {
            setIsFirstDeposit(true)
        } else {
            setIsFirstDeposit(false)
        }
    }, [isLoadingFirstDeposit, transactionHistoryContracts])

    return {
        isFirstDeposit,
        isLoadingFirstDeposit,
        transactionHistoryContracts
    }
}
