import { useLedger, useParty } from '@daml/react'
import React from 'react'

import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import useIsAuthenticated from './useAuth'
import { CreateEvent } from '@daml/ledger'

type HookState = {
    isAdmin: boolean
    loading: boolean
    EventManagerServiceContract: CreateEvent<
        EventManagerService,
        EventManagerService.Key,
        string
    >[]
    MarketingManagerServiceContract: CreateEvent<
        MarketingManagerService,
        MarketingManagerService.Key,
        string
    >[]
}

type Action = {
    type: 'UPDATEDATA'
    payload: HookState
}

const initState = {
    isAdmin: false,
    loading: true,
    EventManagerServiceContract: [],
    MarketingManagerServiceContract: []
}

const emptyState = {
    isAdmin: false,
    loading: false,
    EventManagerServiceContract: [],
    MarketingManagerServiceContract: []
}


function reducer(state: HookState, action: Action) {
    switch (action.type) {
        case 'UPDATEDATA':
            return {
                isAdmin: action.payload.isAdmin,
                loading: false,
                EventManagerServiceContract: action.payload.EventManagerServiceContract,
                MarketingManagerServiceContract: action.payload.MarketingManagerServiceContract
            }
        default:
            throw new Error('Error on reducer')
    }
}

export default function useIsAllowed() {
    const isAuthenticated = useIsAuthenticated()
    const [{ loading, isAdmin, EventManagerServiceContract, MarketingManagerServiceContract }, dispatch] = React.useReducer(
        reducer,
        initState
    )

    const ledger = useLedger()
    const party = useParty()
    React.useEffect(() => {
        if (isAuthenticated) {
            Promise.all([
                ledger.query(MarketingManagerService, {
                    customer: party
                }),
                ledger.query(EventManagerService, {
                    customer: party
                })
            ])
                .then(([marketingManagerQuery, eventManagerQuery]) => {
                    if (
                        marketingManagerQuery.length > 0
                    ) {
                        dispatch({
                            type: 'UPDATEDATA',
                            payload: {
                                loading: false,
                                isAdmin: true,
                                EventManagerServiceContract: [],
                                MarketingManagerServiceContract: marketingManagerQuery
                            }
                        })
                        return () => { }
                    }
                    if (
                        eventManagerQuery.length > 0
                    ) {
                        dispatch({
                            type: 'UPDATEDATA',
                            payload: {
                                loading: false,
                                isAdmin: true,
                                EventManagerServiceContract: eventManagerQuery,
                                MarketingManagerServiceContract: []
                            }
                        })
                        return () => { }
                    }
                    dispatch({
                        type: 'UPDATEDATA',
                        payload: emptyState
                    })
                    return () => { }
                })
                .catch(() =>
                    dispatch({
                        type: 'UPDATEDATA',
                        payload: emptyState
                    })
                )
        }
        return () => { }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAuthenticated])

    return { loading, isAdmin, EventManagerServiceContract, MarketingManagerServiceContract }
}
