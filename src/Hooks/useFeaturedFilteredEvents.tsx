import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { filterEventsByFeaturedFlag } from 'Utils/filterEventsByFeaturedFlag'

/**
 * 
 * @param eventContracts readonly CreateEvent<
        EventInstrument,
        EventInstrument.Key,
        string
    >[]
 * @param isFeatured boolean
 * @returns memoized event contracts filtered by featured
 */
const useFeaturedFilteredEvents = (
    eventContracts: readonly CreateEvent<
        EventInstrument,
        EventInstrument.Key,
        string
    >[],
    isFeatured: boolean
) => {
    const filteredContracts = React.useMemo(
        () => filterEventsByFeaturedFlag(isFeatured, eventContracts),
        [isFeatured, eventContracts]
    )

    return filteredContracts
}

export default useFeaturedFilteredEvents
