import React from 'react'

export default function useMediaQuery(viewport: string) {
    const [matches, setMatches] = React.useState(false)
    React.useEffect(() => {
        const media = window.matchMedia(viewport)
        if (media.matches !== matches) {
            setMatches(media.matches)
        }
        const listener = () => {
            setMatches(media.matches)
        }
        media.addListener(listener)
        return () => media.removeListener(listener)
    }, [matches, viewport])

    return matches
}
