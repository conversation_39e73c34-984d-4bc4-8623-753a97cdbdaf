import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

import { useUserState, signOut, useUserDispatch } from 'State/UserContext'

import { isLocalDev } from 'config'
import { isTokenExpired } from 'Utils/isLocalTokenExpired'
import { cache } from 'Utils/cache'
import { isAdminTokenExpired } from 'Utils/validateGambylAdminToken'

const { remove } = cache()

/**
 * Daml Hub access tokens expire every 24 hours.
 */
export default function useIsUserTokenValid() {
    const location = useLocation()
    const navigate = useNavigate()
    const { isAuthenticated, token } = useUserState()
    const userDispatch = useUserDispatch()

    function logout() {
        remove('bets')
        signOut(userDispatch, history)
    }

    React.useEffect(() => {
        if (isAuthenticated) {
            if (!isLocalDev) {
                return isAdminTokenExpired(token) ? logout() : () => {}
            }
            return isTokenExpired(token) ? logout() : () => {}
        }
        return () => {}
        //eslint-disable-next-line
    }, [location, isAuthenticated])
}
