import React from 'react'
import { TemplateOrInterface } from '@daml/types'
import { useLedger } from '@daml/react'
import { CreateEvent, Stream, Query, QueryResult } from '@daml/ledger'

function useConditionalStreamQueries<T extends object, K, I extends string>(
    template: TemplateOrInterface<T, K, I>,
    queryParams: Query<T>[],
    condition: boolean
): QueryResult<T, K, I> {
    const ledger = useLedger()
    const [results, setResults] = React.useState<QueryResult<T, K, I>>({
        loading: true,
        contracts: []
    })

    if (!ledger) {
        throw new Error(
            'useConditionalStreamQuery must be used within a DamlLedgerProvider.'
        )
    }

    // Event handler for 'live' event
    const onLive = (contracts: readonly CreateEvent<T, K, I>[]) => {
        console.log(`live stream ${template.templateId}`, contracts)
        setResults(prevResult => {
            // Ensure we're maintaining the full structure of the result
            if (!prevResult.loading) return prevResult // Avoid unnecessary state updates
            return { ...prevResult, contracts, loading: false }
        })
    }

    // Event handler for 'change' event
    const onChange = (contracts: readonly CreateEvent<T, K, I>[]) => {
        setResults(prevResult => {
            // Ensure we're maintaining the full structure of the result
            return {
                ...prevResult,
                contracts,
                loading: false // Set loading to false after receiving contracts
            }
        })
    }

    // Event handler for 'close' event
    const onClose = (closeEvent: { code: number }) => {
        //console.log(`closed stream ${template.templateId}`)
        if (closeEvent.code !== 4000) {
            console.error(
                `WebSocket connection failed - ${template.templateId}.`
            )
            setResults(prevResult => {
                // Reset loading state when closing stream due to failure
                return { ...prevResult, loading: true }
            })
        }
    }

    React.useEffect(() => {
        let stream: Stream<T, K, I, readonly CreateEvent<T, K, I>[]> | null =
            null
        if (condition) {
            stream = ledger.streamQueries(template, queryParams)
            stream.on('live', data => onLive(data))
            stream.on('change', data => onChange(data))
            stream.on('close', onClose)
        }
        if (!condition && stream) {
            stream.close()
            stream = null
        }
        return () => {
            if (stream) {
                stream.close()
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [condition, ledger])

    return results
}

export default useConditionalStreamQueries
