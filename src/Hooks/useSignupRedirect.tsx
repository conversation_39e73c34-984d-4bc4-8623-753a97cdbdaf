import { useHistory } from 'react-router-dom'
import { isLocalDev } from 'config'
import { useAuth0 } from '@auth0/auth0-react'
import { signUpParams } from 'Constants/Auth0Constants'

export default function useSignupRedirection() {
    const { push } = useHistory()
    const { loginWithRedirect } = useAuth0()

    const handleLogin = async () => {
        await loginWithRedirect(signUpParams)
    }

    return isLocalDev ? () => push('/signin') : () => handleLogin()
}
