import React, { useEffect } from 'react'
import { toast } from 'react-toastify'
import { Link } from 'react-router-dom'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'
import useIsAuthenticated from 'Hooks/useAuth'
import usePrevious from 'Hooks/usePrevious'

import 'react-toastify/dist/ReactToastify.css'
import './style.scss'
import { useTranslation } from 'react-i18next'

export default function useStatusBanner() {
    const { status } = useVerificationStatusContext()
    const isAuthenticated = useIsAuthenticated()
    const previousStatus = usePrevious(status)
    const { t } = useTranslation()

    const bannerText = () => {
        if (status === 'pending') {
            toast.info(
                <span>
                    Your KYC verification is in progress.You will successfully
                    be able to access the platform once you are compliant.{' '}
                    <Link to="/account">Click here</Link> to check you status.
                </span>,
                {
                    className: `statusBanner--purple`,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    progress: undefined,
                    theme: 'colored'
                }
            )
        }
        if (status === 'failed') {
            toast.info(
                <span>
                    Your KYC verification has been rejected.{' '}
                    <Link to="/account">Click here</Link> for more details.
                </span>,
                {
                    className: `statusBanner--purple`,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    progress: undefined,
                    theme: 'colored'
                }
            )
        }
        if (status === 'success') {
            toast.info(
                <span>
                    'Congratulations! Your KYC verification is successfully
                    completed.'
                </span>,
                {
                    className: `statusBanner--purple`,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    progress: undefined,
                    theme: 'colored'
                }
            )
        }
        if (status === 'notStarted') {
            toast.info(
                <span>
                    {t('UnverifiedToastMessage1')}{' '}
                    <Link to="/account">{t('UnverifiedToastMessage2')}</Link>{' '}
                    {t('UnverifiedToastMessage3')}
                </span>,
                {
                    className: `statusBanner--purple`,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    progress: undefined,
                    theme: 'colored'
                }
            )
        }
    }
    return useEffect(() => {
        if (status !== 'success') {
            if (isAuthenticated) {
                bannerText()
            }
        } else if (previousStatus === 'pending' && status === 'success') {
            if (isAuthenticated) {
                bannerText()
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [status, isAuthenticated])
}
