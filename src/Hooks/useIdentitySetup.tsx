import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { useParty, useQuery } from '@daml/react'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'

export default function useIdentitySetup() {
    const navigate = useNavigate()
    const { status } = useVerificationStatusContext()
    const user = useParty()
    const { contracts: serviceContracts, loading: loadingServiceContracts } =
        useQuery(
            Service,
            () => {
                return { customer: user }
            },
            [user]
        )

    React.useEffect(() => {
        if (
            status === 'notStarted' &&
            serviceContracts?.length === 0 &&
            !loadingServiceContracts
        ) {
            return navigate('/identity_setup');
        }
        return () => {}
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [serviceContracts, loadingServiceContracts, status])
}
