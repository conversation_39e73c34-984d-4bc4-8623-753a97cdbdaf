import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import useIsAuthenticated from './useAuth'
import useIsAllowed from './useIsAdminAccount'

export default function useAdminPath() {
    const { pathname } = useLocation()
    const isAuthenticated = useIsAuthenticated()
    const { isAdmin, loading } = useIsAllowed()
    const { push } = useNavigate()

    React.useEffect(() => {
        if (!loading && isAuthenticated && isAdmin && pathname !== '/admin') {
            return navigate('/admin');
        }
        return () => { }
    }, [isAdmin, loading, pathname])
}
