import React from 'react'
import { useHistory, useLocation } from 'react-router-dom'
import useIsAuthenticated from './useAuth'
import useIsAllowed from './useIsAdminAccount'

export default function useAdminPath() {
    const { pathname } = useLocation()
    const isAuthenticated = useIsAuthenticated()
    const { isAdmin, loading } = useIsAllowed()
    const { push } = useHistory()

    React.useEffect(() => {
        if (!loading && isAuthenticated && isAdmin && pathname !== '/admin') {
            return push('/admin')
        }
        return () => { }
    }, [isAdmin, loading, pathname])
}
