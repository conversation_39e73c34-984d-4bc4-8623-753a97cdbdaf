import { useNavigate } from 'react-router-dom'
import { isLocalDev } from 'config'
import { useAuth0 } from '@auth0/auth0-react'
import { loginParams } from 'Constants/Auth0Constants'

export default function useLoginRedirection() {
    const { push } = useNavigate()
    const { loginWithRedirect } = useAuth0();

    const handleLogin = async () => {
        await loginWithRedirect(loginParams);
    };


    return isLocalDev ? () => navigate('/signin') : () => handleLogin();
}
