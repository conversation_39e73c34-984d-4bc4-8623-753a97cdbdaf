import { useState, useEffect, useRef, useCallback } from 'react'

export default function useInfiniteScroll(incrementStep = 5) {
    const [visibleAmount, setVisibleAmount] = useState(incrementStep)
    const [element, setElement] = useState<HTMLSpanElement | null>(null)
    const showMore = useCallback(
        () => setVisibleAmount(prevState => prevState + incrementStep),
        [incrementStep]
    )

    const showMoreRef = useRef(showMore)

    const observer = useRef(
        new IntersectionObserver(
            entries => {
                const firstEntry = entries[0]
                if (firstEntry.isIntersecting) {
                    showMoreRef.current()
                }
            },
            { threshold: 1 }
        )
    )

    useEffect(() => {
        const currentElement = element
        const currentObserver = observer.current

        if (currentElement) {
            currentObserver.observe(currentElement)
        }

        return () => {
            if (currentElement) {
                currentObserver.unobserve(currentElement)
            }
        }
    }, [element])

    useEffect(() => {
        showMoreRef.current = showMore
    }, [showMore])

    return { visibleAmount, setVisibleAmount, setElement, showMore }
}
