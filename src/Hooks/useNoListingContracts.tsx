import React from 'react'
import { publicContext } from 'Containers/App';
import { EventInstrument } from "@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model";
import { useGamblingListingContext } from 'State/GamblingListingContext';

/**
 * Returns a list of Event contracts without bets placed
 * @returns a isLoading boolean and and a list of contracts with the type CreateEvent<EventInstrument, EventInstrument.Key, string>[]
 */
const useNoListingContracts = () => {
    const { contracts, loading } = publicContext.useStreamQueries(EventInstrument, () => [
        {
            status: "Active",
            details: {
                eventStatus: 'NotStarted',
            },
        } as EventInstrument
    ], [])
    const { gamblingListingContracts, gamblingListingLoading } =
        useGamblingListingContext()

    const isLoading = loading || gamblingListingLoading

    const memoizedContracts = React.useMemo(() => contracts.filter(data =>
        !gamblingListingContracts.find(e => e.payload.eventLabel === data.payload.eventId.label)
    ), [gamblingListingContracts, contracts])

    return { contracts: memoizedContracts, isLoading }
}

export default useNoListingContracts