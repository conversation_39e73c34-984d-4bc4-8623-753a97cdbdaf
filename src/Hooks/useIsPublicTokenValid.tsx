import React from 'react'
import { useLocation } from 'react-router-dom'

import { isLocalDev } from 'config'
import { isTokenExpired } from 'Utils/isLocalTokenExpired'
import { isAdminTokenExpired } from 'Utils/validateGambylAdminToken'

function refreshPublicToken() {
    //for now DA doesn't provide a method
    //to refresh tokens so the only way
    //is to refresh the page and fetch a new one
    window.location.reload()
}

export default function useIsPublicTokenValid(token: string | undefined) {
    const location = useLocation()
    React.useEffect(() => {
        if (token) {
            if (!isLocalDev) {
                return isAdminTokenExpired(token)
                    ? refreshPublicToken()
                    : () => {}
            }
            return isTokenExpired(token) ? refreshPublicToken() : () => {}
        }
        return () => {}
    }, [token, location])
}
