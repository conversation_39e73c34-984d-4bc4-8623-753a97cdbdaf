import React from 'react'
import {
    Account,
    TimedLimit,
    AccruedLimit
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import { CreateEvent } from '@daml/ledger'

type TLimit = [TimedLimit, AccruedLimit]
type TLimitsMap = TLimit[]

export default function useDepositLimits(
    accountLoading: boolean,
    accountContracts: CreateEvent<Account, Account.Key, string>
) {
    const [depositLimits, setDepositLimits] = React.useState<any>([])

    React.useEffect(() => {
        if (!accountLoading && accountContracts) {
            const sortDepositLimits = (limits: TLimitsMap | undefined) => {
                if (limits?.length) {
                    const dailyLimit = limits.find(
                        (limit: TLimit) => limit[0] === 'Daily'
                    )
                    const weeklyLimit = limits.find(
                        (limit: TLimit) => limit[0] === 'Weekly'
                    )
                    const monthlyLimit = limits.find(
                        (limit: TLimit) => limit[0] === 'Monthly'
                    )
                    const otherLimits = limits.filter(
                        limit =>
                            !['Daily', 'Weekly', 'Monthly'].includes(limit[0])
                    )
                    return [
                        dailyLimit,
                        weeklyLimit,
                        monthlyLimit,
                        ...otherLimits
                    ].filter(limit => limit !== undefined)
                }
                return []
            }
            const sortedLimits = sortDepositLimits(
                accountContracts?.payload?.depositLimit.entriesArray()
            )

            setDepositLimits(sortedLimits)
        }
    }, [accountLoading, accountContracts])

    return depositLimits
}
