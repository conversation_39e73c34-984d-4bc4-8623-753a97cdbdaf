import React from 'react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { publicContext } from 'Containers/App'
import { checkIfNonSportEvent } from 'Utils/checkIfNonSportEvent'
import { prepareSubmarketPath } from 'Utils/getPathFromSubmarket'

const useFeaturedEvents = () => {
    const { contracts: eventContracts, loading: eventLoading } =
        publicContext.useStreamQueries(
            EventInstrument,
            () => {
                return [
                    {
                        status: 'Active',
                        details: {
                            eventStatus: 'NotStarted'
                        },
                        featured: true
                    } as EventInstrument
                ]
            },
            []
        )

    const { featuredEvents, isLoading } = React.useMemo(() => ({
        featuredEvents: eventContracts,
        isLoading: eventLoading,
    }),
        [eventContracts, eventLoading]
    );

    const memoizedRoutes = React.useMemo(() => {
        const featuredQueries = featuredEvents.filter(
            (event, index, self) =>
                index ===
                self.findIndex(
                    t =>
                        checkIfNonSportEvent(t.payload.details.market) ===
                        checkIfNonSportEvent(event.payload.details.market) &&
                        t.payload.details.market.tag ===
                        event.payload.details.market.tag &&
                        event.payload.details.submarkets[0].value === t.payload.details.submarkets[0].value
                )
        ).map(event => ({
            tournament: event?.payload.details.submarkets[0].value,
            market: event?.payload?.details?.market,
            route:
                `/popular/${event?.payload?.details?.market?.tag}/${checkIfNonSportEvent(event?.payload.details.market)}/${prepareSubmarketPath(event?.payload.details.submarkets[0].value)}`
        }))
        return featuredQueries
    }, [featuredEvents])


    return { routes: memoizedRoutes, isLoading }
}

export default useFeaturedEvents