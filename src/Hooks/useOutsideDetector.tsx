import React,{ <PERSON><PERSON>RefObject } from 'react'

/**
 * Hook that detects clicks outside of the passed ref
 */
export default function useOutsideDetector(
    ref: MutableRefObject<HTMLDivElement | HTMLButtonElement | null> | any,
    eventHandler: () => void,
    exclude: MutableRefObject<HTMLDivElement | HTMLButtonElement | null>
) {
    React.useEffect(() => {
        /**
         * callback if clicked on outside of element
         */
        function handleClickOutside(this: Document, event: { target: any }) {
            if (
                ref.current &&
                !ref.current.contains(event?.target) &&
                !event.target.isSameNode(exclude.current)
            ) {
                eventHandler()
            }
        }

        // Bind the event listener
        document.addEventListener('mousedown', handleClickOutside)
        return () => {
            // Unbind the event listener on clean up
            document.removeEventListener('mousedown', handleClickOutside)
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [ref])
}
