import React from 'react'
import useIsAuthenticated from './useAuth'

import { toast } from 'react-toastify'

export default function usePromotionsBanner() {
    const isAuthenticated = useIsAuthenticated()
    const Banner = () => {
        toast.info(
            'Promotions will only be applied according to the settlement time.',
            {
                position: 'top-right',
                autoClose: false,
                hideProgressBar: true,
                closeOnClick: true,
                pauseOnHover: false,
                draggable: false,
                progress: undefined
            }
        )
    }

    return React.useEffect(() => {
        if (isAuthenticated) {
            return Banner()
        }
        return () => { }
    }, [isAuthenticated])
}
