import { useAuth0 } from '@auth0/auth0-react'
import { useManagerLoginState } from 'State/ManagerLoginContext'
import { useUserState } from 'State/UserContext'
import { isLocalDev } from 'config'

export default function useIsAuthenticated() {
    const { isAuthenticated: localIsAuthenticated } = useUserState()
    const { isAuthenticated: managerIsAuthenticated } = useManagerLoginState()
    const { isAuthenticated: auth0IsAuthenticated } = useAuth0()
    //CHANGE HERE AUTH0
    function getIsAuthenticated() {
        if (isLocalDev) {
            return localIsAuthenticated ? localIsAuthenticated : managerIsAuthenticated
        }
        return auth0IsAuthenticated ? auth0IsAuthenticated : managerIsAuthenticated
    }
    const isAuthenticated = getIsAuthenticated()
    return isAuthenticated
}
