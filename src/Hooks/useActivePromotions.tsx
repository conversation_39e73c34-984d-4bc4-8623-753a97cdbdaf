import { publicContext } from 'Containers/App'
import {
    Promotion,
    Status
} from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'

const useActivePromotions = () => {
    const { contracts: promotionsContracts, loading: promotionsLoader } =
        publicContext.useQuery(
            Promotion,
            () => {
                return {
                    status: 'Active' as Status
                }
            },
            []
        )
    return { promotionsContracts, promotionsLoader }
}

export default useActivePromotions
