import React from 'react'

const useScript = (url: string, id?: string, async?: boolean) => {
    React.useEffect(() => {
        const script = document.createElement('script')
        script.id = id ? id : ''
        script.src = url
        script.async = async ? async : true
        script.type = 'text/javascript'

        document.body.appendChild(script)

        return () => {
            document.body.removeChild(script)
        }
    }, [url])
}

export default useScript
