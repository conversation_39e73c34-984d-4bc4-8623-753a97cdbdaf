import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { Account } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import { useHistory } from 'react-router-dom'

export default function useRedirectToFirstDeposit(
    accountLoading: boolean,
    accountContracts: CreateEvent<Account, Account.Key, string>
) {
    const { push, location } = useHistory()
    React.useEffect(() => {
        if (!accountLoading && location.pathname === '/first_deposit') {
            return accountContracts?.payload?.transactionHistory
                .entriesArray()
                .flat()
                .some(
                    (transaction: any) => transaction === 'DepositTransaction'
                )
                ? push('/deposit')
                : () => {}
        } else if (!accountLoading && location.pathname === '/deposit') {
            return accountContracts?.payload?.transactionHistory
                .entriesArray()
                .flat().length <= 0
                ? push('/first_deposit')
                : () => {}
        }
        return () => {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [accountContracts, accountLoading])
}
