import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

import {
    signOutManagerLogin,
    useManagerLoginDispatch,
    useManagerLoginState
} from 'State/ManagerLoginContext'
import { isTokenExpired } from 'Utils/isLocalTokenExpired'

import { isLocalDev } from 'config'
import { cache } from 'Utils/cache'
import { isAdminTokenExpired } from 'Utils/validateGambylAdminToken'

const { remove } = cache()
export default function useIsManagerTokenValid() {
    const location = useLocation()
    const navigate = useNavigate()
    const { isAuthenticated: managerIsAuthenticated, token } =
        useManagerLoginState()
    const managerDispatch = useManagerLoginDispatch()

    function logout() {
        remove('bets')
        signOutManagerLogin(managerDispatch, history)
    }

    React.useEffect(() => {
        if (managerIsAuthenticated) {
            if (!isLocalDev) {
                return isAdminTokenExpired(token) ? logout() : () => {}
            }
            return isTokenExpired(token) ? logout() : () => {}
        }
        return () => {}
        //eslint-disable-next-line
    }, [location, managerIsAuthenticated])
}
