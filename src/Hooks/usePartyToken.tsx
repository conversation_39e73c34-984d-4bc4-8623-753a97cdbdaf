import { useUserState } from 'State/UserContext'
import { useAuth0 } from '@auth0/auth0-react'
import { isLocalDev } from 'config'
import { useManagerLoginState } from 'State/ManagerLoginContext'

export default function usePartyToken() {
    const { party: localParty, token: localToken } = useUserState()
    const { party: managerParty, token: managerToken } = useManagerLoginState()
    const { user } = useAuth0()

    //CHANGE HERE AUTH0
    function getPartyToDAMLProvider() {
        if (isLocalDev) {
            return localParty ? localParty : managerParty
        }
        return user?.daml_party ? user?.daml_party : managerParty
    }
    function getTokenToDAMLProvider() {
        if (isLocalDev) {
            return localToken ? localToken : managerToken
        }
        return user?.daml_access_token ? user?.daml_access_token : managerToken
    }

    const loginCount = user?.login_count;

    const partyToDAMLProvider = getPartyToDAMLProvider()
    const tokenToDAMLProvider = getTokenToDAMLProvider()

    return { partyToDAMLProvider, tokenToDAMLProvider, loginCount }
}
