import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { langConverter } from 'Utils/getEventTitleFromLang'

/**
 * 
 * @param eventContracts readonly CreateEvent<
        EventInstrument,
        EventInstrument.Key,
        string
    >[]
 * @param eventTitle string
 * @param lang string with current active language on the App
 * @returns memoized event contracts filtered by event title string
 */
const useFilterEventByEventTitle = (
    eventContracts: readonly CreateEvent<
        EventInstrument,
        EventInstrument.Key,
        string
    >[],
    eventTitle: string,
    lang: string
) => {
    const filterEventsByEventTitle = React.useMemo(() => {
        const eventTitleToFilter = eventTitle.toLocaleLowerCase()
        const userActiveLangKey = langConverter(lang)
        const includesInTitle = (title: string) =>
            title.toLowerCase().includes(eventTitleToFilter)

        return eventContracts.filter(d => {
            const eventTitleMap = d.payload.details.eventTitle.entriesArray()
            if (eventTitleMap.length > 1) {
                const filteredByLang = eventTitleMap.filter(
                    titleMap => titleMap[0] === userActiveLangKey
                )[0]
                return includesInTitle(filteredByLang[1])
            }
            return includesInTitle(eventTitleMap[0][1])
        })
    }, [eventContracts, eventTitle, lang])

    return filterEventsByEventTitle
}

export default useFilterEventByEventTitle
