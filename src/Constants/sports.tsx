import React, { ReactElement } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

import {
    faBaseballBall,
    faBiking,
    faFootballBall,
    faFutbol,
    faGamepad,
    faGlobe,
    faGolfBall,
    faHockeyPuck,
    faHorseHead,
    faLandmark,
    faMotorcycle,
    faTableTennis,
    faVolleyballBall,
    faBasketballBall
} from '@fortawesome/free-solid-svg-icons'
import Cricket from 'Assets/SportsIcons/cricket'
import AustralianFootball from 'Assets/SportsIcons/australianFootball'
import Boxing from 'Assets/SportsIcons/boxing'
import Darts from 'Assets/SportsIcons/darts'
import Olympics from 'Assets/SportsIcons/olympics'
import Tennis from 'Assets/SportsIcons/tennis'
import Fight from 'Assets/SportsIcons/fight'
import Wrestling from 'Assets/SportsIcons/wrestling'

export interface Sport {
    title: string
    icon: ReactElement
    link: string
}

export const sports: { [key: string]: Sport } = {
    Handball: {
        title: 'Handball',
        icon: <FontAwesomeIcon icon={faVolleyballBall} />,
        link: '/handball'
    },
    Basketball: {
        title: 'Basketball',
        icon: <FontAwesomeIcon icon={faBasketballBall} />,
        link: '/Basketball'
    },
    Football: {
        title: 'Soccer',
        icon: <FontAwesomeIcon icon={faFutbol} />,
        link: '/soccer'
    },
    'Am. Football': {
        title: 'Am. Football',
        icon: <FontAwesomeIcon icon={faFootballBall} />,
        link: '/football'
    },
    Politics: {
        title: 'Politics',
        icon: <FontAwesomeIcon icon={faLandmark} />,
        link: '/politics'
    },
    'League of Legends': {
        title: 'League of Legends',
        icon: <FontAwesomeIcon icon={faGamepad} />,
        link: '/esports'
    },
    'Dota 2': {
        title: 'Dota 2',
        icon: <FontAwesomeIcon icon={faGamepad} />,
        link: '/esports'
    },
    'Counter-Strike': {
        title: 'Counter-Strike',
        icon: <FontAwesomeIcon icon={faGamepad} />,
        link: '/esports'
    },
    FIFA: {
        title: 'FIFA',
        icon: <FontAwesomeIcon icon={faGamepad} />,
        link: '/horses'
    },
    'In Play': {
        title: 'In Play',
        icon: <FontAwesomeIcon icon={faGlobe} />,
        link: '/inplay'
    },
    'Horse Racing': {
        title: 'Horse Racing',
        icon: <FontAwesomeIcon icon={faHorseHead} />,
        link: '/horses'
    },
    Cricket: {
        title: 'Cricket',
        icon: <Cricket />,
        link: '/cricket'
    },
    'Australian Rules Football': {
        title: 'Australian Rules Football',
        icon: <AustralianFootball />,
        link: 'australian_rules_football'
    },
    Baseball: {
        title: 'Baseball',
        icon: <FontAwesomeIcon icon={faBaseballBall} />,
        link: '/baseball'
    },
    Boxing: {
        title: 'Boxing',
        icon: <Boxing />,
        link: '/boxing'
    },
    Cycling: {
        title: 'Cycling',
        icon: <FontAwesomeIcon icon={faBiking} />,
        link: '/cycling'
    },
    Darts: {
        title: 'Darts',
        icon: <Darts />,
        link: '/darts'
    },
    Golf: {
        title: 'Golf',
        icon: <FontAwesomeIcon icon={faGolfBall} />,
        link: '/golf'
    },
    'Ice Hockey': {
        title: 'Ice Hockey',
        icon: <FontAwesomeIcon icon={faHockeyPuck} />,
        link: '/hockey'
    },
    Motosport: {
        title: 'Motosport',
        icon: <FontAwesomeIcon icon={faMotorcycle} />,
        link: '/motosport'
    },
    Olympics: {
        title: 'Olympics',
        icon: <Olympics />,
        link: '/olympics'
    },
    Tennis: {
        title: 'Tennis',
        icon: <Tennis />,
        link: '/tennis'
    },
    'Table Tennis': {
        title: 'Table tennis',
        icon: <FontAwesomeIcon icon={faTableTennis} />,
        link: '/table_tennis'
    },
    MMA: {
        title: 'MMA / UFC',
        icon: <Fight />,
        link: '/mma'
    },
    Volleyball: {
        title: 'Volleyball',
        icon: <FontAwesomeIcon icon={faVolleyballBall} />,
        link: '/volleyball'
    },
    Wrestling: {
        title: 'Wrestling',
        icon: <Wrestling />,
        link: '/wrestling'
    }
}

export const markets = [
    {
        title: 'top markets',
        sports: ['soccer', 'football', 'politics', 'eSports', 'inPlay']
    },
    { title: 'favorites', sports: ['horses', 'cricket'] },
    {
        title: 'all other markets',
        sports: [
            'australianRulesFootball',
            'baseball',
            'boxing',
            'cycling',
            'darts',
            'golfs',
            'hockey',
            'motosport',
            'olympics',
            'tennis',
            'tableTennis',
            'mma',
            'volleyball',
            'wrestling'
        ]
    }
]
