import React, { ReactElement } from 'react'

import Account from 'Assets/HelpIcons/account'
import CheckMark from 'Assets/HelpIcons/checkMark'
import Money from 'Assets/HelpIcons/money'
import QuestionMark from 'Assets/HelpIcons/questionMark'
import Security from 'Assets/HelpIcons/security'
import ShoppingCart from 'Assets/HelpIcons/shoppingCart'
import Wallet from 'Assets/HelpIcons/wallet'

export const helpCards: {
    title: string
    icon: ReactElement
    subtitle: string
}[] = [
    {
        icon: <Account />,
        title: 'Guide for new users',
        subtitle: 'Donec facilisis tortor ut augue lacinia'
    },
    {
        icon: <QuestionMark />,
        title: 'How to place bets',
        subtitle: 'Donec facilisis tortor ut augue lacinia'
    },
    {
        icon: <Wallet />,
        title: 'Depositing money',
        subtitle: 'Donec facilisis tortor ut augue lacinia'
    },
    {
        icon: <Money />,
        title: 'Getting Paid',
        subtitle: 'Donec facilisis tortor ut augue lacinia'
    },
    {
        icon: <CheckMark />,
        title: 'Verification Policies',
        subtitle: 'Donec facilisis tortor ut augue lacinia'
    },
    {
        icon: <Account />,
        title: 'Your Account',
        subtitle: 'Donec facilisis tortor ut augue lacinia'
    },
    {
        icon: <Security />,
        title: 'Security',
        subtitle: 'Donec facilisis tortor ut augue lacinia'
    },
    {
        icon: <ShoppingCart />,
        title: 'About our Fees',
        subtitle: 'Donec facilisis tortor ut augue lacinia'
    }
]
