import React from 'react'
import ReactDOM from 'react-dom'
import './index.scss'
import App from './Containers/App'
import reportWebVitals from './reportWebVitals'
import { UserProvider } from './State/UserContext'
import DamlHub from '@daml/hub-react'
import './i18n'
import LoaderSpinner from 'Components/Loader'
import TagManager from 'react-gtm-module'
import { BrowserRouter } from 'react-router-dom'
import { Auth0ProviderWithHistory } from 'Auth0-Provider-With-History'
import { ManagerLoginProvider } from 'State/ManagerLoginContext'

const tagManagerArgs = {
    gtmId: process.env.REACT_APP_GTM ?? ''
}

TagManager.initialize(tagManagerArgs)

ReactDOM.render(
    <React.Suspense fallback={<LoaderSpinner />}>
        <BrowserRouter>
            <Auth0ProviderWithHistory>
                <DamlHub>
                    <UserProvider>
                        <ManagerLoginProvider>
                            <App />
                        </ManagerLoginProvider>
                    </UserProvider>
                </DamlHub>
            </Auth0ProviderWithHistory>
        </BrowserRouter>
    </React.Suspense>,
    document.getElementById('root')
)

reportWebVitals()
