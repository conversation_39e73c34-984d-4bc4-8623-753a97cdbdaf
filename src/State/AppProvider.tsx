import React, {
    createContext,
    useState,
    FC,
    Dispatch,
    SetStateAction
} from 'react'

type ContextProps = {
    showSideMenu: boolean
    setShowSideMenu: Dispatch<SetStateAction<boolean>>
}

export const AppContext = createContext<Partial<ContextProps>>({})

export const AppProvider: FC = ({ children }) => {
    const [showSideMenu, setShowSideMenu] = useState<boolean>(true)

    const contextValue = {
        showSideMenu,
        setShowSideMenu
    }
    return (
        <AppContext.Provider value={contextValue}>
            {children}
        </AppContext.Provider>
    )
}
