import React from 'react'
import i18n from 'i18next'

const I18LanguageContext = React.createContext<
    { lang: string; handleLang: () => void } | any
>({})

const localStorageLanguageKey = 'AppLang'

function I18LanguageProvider({ children }: { children: React.ReactNode }) {
    const [lang, setLang] = React.useState(() =>
        localStorage.getItem(localStorageLanguageKey)
            ? localStorage.getItem(localStorageLanguageKey)?.toString()
            : 'US'
    )

    const handleLang = (value: string) => {
        setLang(value)
        i18n.changeLanguage(value)
        localStorage.setItem(localStorageLanguageKey, value)
    }

    return (
        <I18LanguageContext.Provider value={{ lang, handleLang }}>
            {children}
        </I18LanguageContext.Provider>
    )
}

function useI18LanguageContext() {
    const context = React.useContext(I18LanguageContext)
    if (context === undefined) {
        throw new Error(
            'useI18LanguageContext must be used within a I18LanguageProvider'
        )
    }
    return context
}

export { useI18LanguageContext, I18LanguageProvider }
