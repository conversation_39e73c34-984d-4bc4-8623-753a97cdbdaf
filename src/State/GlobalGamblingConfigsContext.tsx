import React from 'react'
import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { publicContext } from 'Containers/App'
import { CreateEvent } from '@daml/ledger'

const GlobalGamblingConfigContext = React.createContext<{
    GlobalGamblingConfigurationContract: readonly CreateEvent<
        GlobalGamblingConfiguration,
        GlobalGamblingConfiguration.Key,
        string
    >[]
    GlobalGamblingConfigurationLoader: boolean
}>({
    GlobalGamblingConfigurationContract: [],
    GlobalGamblingConfigurationLoader: false
})

function GlobalGamblingConfigProvider({
    children
}: {
    children: React.ReactNode
}) {
    const {
        contracts: GlobalGamblingConfigurationContract,
        loading: GlobalGamblingConfigurationLoader
    } = publicContext.useQuery(GlobalGamblingConfiguration)

    return (
        <GlobalGamblingConfigContext.Provider
            value={{
                GlobalGamblingConfigurationContract,
                GlobalGamblingConfigurationLoader
            }}
        >
            {children}
        </GlobalGamblingConfigContext.Provider>
    )
}

function useGlobalGamblingConfigContext() {
    const context = React.useContext(GlobalGamblingConfigContext)
    if (context === undefined) {
        throw new Error(
            'useFavoriteMarketContext must be used within a FavoriteMarketsProvider'
        )
    }
    return context
}

export { useGlobalGamblingConfigContext, GlobalGamblingConfigProvider }
