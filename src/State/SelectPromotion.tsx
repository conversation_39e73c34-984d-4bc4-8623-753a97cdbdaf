import React from 'react'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'

const SelectedPromotionContext = React.createContext<any>({})

function SelectedPromotionProvider({
    children
}: {
    children: React.ReactNode
}) {
    const [selectedPromotion, setSelectedPromotion] =
        React.useState<Promotion.CreateEvent | null>(null)

    return (
        <SelectedPromotionContext.Provider
            value={[selectedPromotion, setSelectedPromotion]}
        >
            {children}
        </SelectedPromotionContext.Provider>
    )
}

function useSelectedPromotionContext() {
    const context = React.useContext(SelectedPromotionContext)
    if (context === undefined) {
        throw new Error(
            'useSelectedPromotionContext must be used within a SelectedPromotionProvider'
        )
    }
    return context
}

export { useSelectedPromotionContext, SelectedPromotionProvider }
