import React, { useContext, createContext, useState, useEffect } from 'react'
import { cache } from 'Utils/cache'
import { useAccountContex } from './AccountContext'

const { load, save } = cache()

type OddTypeProviderProps = { children: React.ReactNode }

const oddTypeContextInitialValue = {
    oddState: 'Moneyline',
    updateOddState: (value: string) => {}
}

const oddTypeContext = createContext(oddTypeContextInitialValue)

function OddTypeProvider({ children }: OddTypeProviderProps) {
    const stateFromStorage = load('oddType')
    const [oddState, setOddState] = useState(
        stateFromStorage ? stateFromStorage : 'Moneyline'
    )
    const { accountContracts } = useAccountContex()
    const preferredOdd = React.useMemo(() => {
        if (accountContracts.length > 0) {
            return accountContracts[0].payload.preferences.entriesArray()[1][1]
        }
        return stateFromStorage ? stateFromStorage : 'Moneyline'
    }, [accountContracts, stateFromStorage])

    const updateOddState = (val: string) => {
        if (val !== oddState) {
            save('oddType', val)
            setOddState(val)
        }
    }

    useEffect(() => {
        if (preferredOdd) {
            updateOddState(preferredOdd)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [preferredOdd])

    return (
        <oddTypeContext.Provider value={{ oddState, updateOddState }}>
            {children}
        </oddTypeContext.Provider>
    )
}

function useOddType() {
    const context = useContext(oddTypeContext)
    if (context === undefined) {
        throw new Error('useOddType must be used within a OddTypeProvider')
    }
    return context
}

export { useOddType, OddTypeProvider }
