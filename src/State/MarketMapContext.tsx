import React from 'react'
import { publicContext } from 'Containers/App'
import { MarketMap } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { CreateEvent } from '@daml/ledger'

const MarketMapContext = React.createContext<{
    marketContracts: readonly CreateEvent<MarketMap, MarketMap.Key, string>[]
    loadingMarkets: boolean
}>({ marketContracts: [], loadingMarkets: false })

function MarketMapProvider({ children }: { children: React.ReactNode }) {
    const { contracts: marketContracts, loading: loadingMarkets } =
        publicContext.useStreamQueries(MarketMap)

    return (
        <MarketMapContext.Provider
            value={{
                marketContracts,
                loadingMarkets
            }}
        >
            {children}
        </MarketMapContext.Provider>
    )
}

function useMarketMapContext() {
    const context = React.useContext(MarketMapContext)
    if (context === undefined) {
        throw new Error(
            'useMarketMapContext must be used within a MarketMapProvider'
        )
    }
    return context
}

export { useMarketMapContext, MarketMapProvider }
