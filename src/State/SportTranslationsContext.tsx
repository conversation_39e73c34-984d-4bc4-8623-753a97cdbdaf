import React from 'react'
import { SportTranslations } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Static'
import { publicContext } from 'Containers/App'
import { CreateEvent } from '@daml/ledger'
import { getTextByLang } from 'Components/Event/EventCard/SportTranslations.helper'

export type langArray = {
    [key: string]: string
}

const SportTranslationsContext = React.createContext<{
    sportTranslationsContracts: readonly CreateEvent<
        SportTranslations,
        any,
        string
    >[]
    loadingSportTranslations: boolean
    BR: langArray[]
    US: langArray[]
    MX: langArray[]
}>({
    sportTranslationsContracts: [],
    loadingSportTranslations: false,
    BR: [],
    US: [],
    MX: []
})

function SportTranslationsProvider({
    children
}: {
    children: React.ReactNode
}) {
    const {
        contracts: sportTranslationsContracts,
        loading: loadingSportTranslations
    } = publicContext.useStreamQueries(SportTranslations)

    const US =
        !loadingSportTranslations &&
        sportTranslationsContracts.length > 0 &&
        sportTranslationsContracts[0]?.payload?.sportsMap
            ? getTextByLang(
                  'en_uk',
                  sportTranslationsContracts[0]?.payload?.sportsMap
              )
            : []

    const MX =
        !loadingSportTranslations &&
        sportTranslationsContracts?.length > 0 &&
        sportTranslationsContracts[0]?.payload?.sportsMap
            ? getTextByLang(
                  'es',
                  sportTranslationsContracts[0]?.payload?.sportsMap
              )
            : []

    const BR =
        !loadingSportTranslations &&
        sportTranslationsContracts?.length > 0 &&
        sportTranslationsContracts[0]?.payload?.sportsMap
            ? getTextByLang(
                  'pt',
                  sportTranslationsContracts[0]?.payload?.sportsMap
              )
            : []

    return (
        <SportTranslationsContext.Provider
            value={{
                sportTranslationsContracts,
                loadingSportTranslations,
                US,
                BR,
                MX
            }}
        >
            {children}
        </SportTranslationsContext.Provider>
    )
}

function useSportTranslationsContext() {
    const context = React.useContext(SportTranslationsContext)
    if (context === undefined) {
        throw new Error(
            'useSportTranslationsContext must be used within a SportTranslationsProvider'
        )
    }
    return context
}

export { useSportTranslationsContext, SportTranslationsProvider }
