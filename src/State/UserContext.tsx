import { History } from 'history'
import React from 'react'

import Credentials, {
    clearCredentials,
    retrieveCredentials,
    storeCredentials
} from 'Utils/credentials'

import { cache } from 'Utils/cache'

const { remove } = cache()

type UserState = {
    isAuthenticated: boolean
    party: string
    token: string
}

type UserAction = {
    type: string
    party?: string | undefined
    token?: string | undefined
}

const UserStateContext = React.createContext<UserState>({
    isAuthenticated: false,
    party: '',
    token: ''
})
const UserDispatchContext = React.createContext<React.Dispatch<UserAction>>(
    {} as React.Dispatch<UserAction>
)

function userReducer(state: UserState, action: UserAction): UserState | never {
    switch (action.type) {
        case 'LOGIN_SUCCESS':
            return {
                ...state,
                isAuthenticated: true,
                party: action.party,
                token: action.token
            } as UserState
        case 'LOGIN_FAILURE':
            return { ...state, isAuthenticated: false }
        case 'SIGN_OUT_SUCCESS':
            return { ...state, party: '', token: '', isAuthenticated: false }
        default: {
            throw new Error(`Unhandled action type: ${action.type}`)
        }
    }
}

const UserProvider: React.FC = ({ children }) => {
    const credentials = retrieveCredentials()

    let initialUserState: UserState = {
        isAuthenticated: false,
        party: '',
        token: ''
    }

    if (credentials) {
        initialUserState = {
            isAuthenticated: true,
            party: credentials.party,
            token: credentials.token
        }
    }

    var [state, dispatch] = React.useReducer(userReducer, initialUserState)

    return (
        <UserStateContext.Provider value={state}>
            <UserDispatchContext.Provider value={dispatch}>
                {children}
            </UserDispatchContext.Provider>
        </UserStateContext.Provider>
    )
}

function useUserState() {
    var context = React.useContext<UserState>(UserStateContext)
    if (context === undefined) {
        throw new Error('useUserState must be used within a UserProvider')
    }
    return context
}

function useUserDispatch() {
    var context =
        React.useContext<React.Dispatch<UserAction>>(UserDispatchContext)
    if (context === undefined) {
        throw new Error('useUserDispatch must be used within a UserProvider')
    }
    return context
}

async function loginUser(
    dispatch: React.Dispatch<UserAction>,
    history: History,
    credentials: Credentials
) {
    const { party, token } = credentials
    try {
        storeCredentials(credentials)
        dispatch({ type: 'LOGIN_SUCCESS', party, token })
        navigate('/verification')
    } catch {
        dispatch({ type: 'LOGIN_FAILURE' })
    }
}

function signOut(dispatch: React.Dispatch<UserAction>, history: History) {
    remove('bets')
    clearCredentials()
    retrieveCredentials()
    dispatch({ type: 'SIGN_OUT_SUCCESS' })
    navigate('/')
}

export { UserProvider, useUserState, useUserDispatch, loginUser, signOut }
