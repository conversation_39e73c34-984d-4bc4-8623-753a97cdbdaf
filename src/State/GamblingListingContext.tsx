import React from 'react'
import { publicContext } from 'Containers/App'
import { GamblingListing } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Listing/Model'
import { CreateEvent } from '@daml/ledger'

const GamblingListingContext = React.createContext<{
    gamblingListingContracts: readonly CreateEvent<
        GamblingListing,
        GamblingListing.Key,
        string
    >[]
    gamblingListingLoading: boolean
}>({ gamblingListingContracts: [], gamblingListingLoading: false })

function GamblingListingProvider({ children }: { children: React.ReactNode }) {
    const {
        contracts: gamblingListingContracts,
        loading: gamblingListingLoading
    } = publicContext.useStreamQueries(GamblingListing)

    return (
        <GamblingListingContext.Provider
            value={{
                gamblingListingContracts,
                gamblingListingLoading
            }}
        >
            {children}
        </GamblingListingContext.Provider>
    )
}

function useGamblingListingContext() {
    const context = React.useContext(GamblingListingContext)
    if (context === undefined) {
        throw new Error(
            'useGamblingListingContext must be used within a GamblingListingProvider'
        )
    }
    return context
}

export { useGamblingListingContext, GamblingListingProvider }
