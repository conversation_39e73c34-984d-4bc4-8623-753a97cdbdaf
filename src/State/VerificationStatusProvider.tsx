import React, { createContext, FC, useContext } from 'react'
import {
    GamblerIdentity,
    RejectedIdentity,
    PendingIdentity
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'

import getUserStatus, { UserStatus } from 'Utils/getUserStatus'
import useIsAuthenticated from 'Hooks/useAuth'
import usePartyToken from 'Hooks/usePartyToken'
import useConditionalStreamQueries from 'Hooks/useConditionalStreamQueries'
import { CreateEvent } from '@daml/ledger'

type ContextProps = {
    loading: boolean
    status: string
    gamblerIdentityContracts: readonly CreateEvent<
        GamblerIdentity,
        GamblerIdentity.Key,
        string
    >[]
    rejectedIdentityContracts: readonly CreateEvent<
        RejectedIdentity,
        RejectedIdentity.Key,
        string
    >[]
    pendingIdentityContracts: readonly CreateEvent<
        PendingIdentity,
        PendingIdentity.Key,
        string
    >[]
}

export const VerificationStatusContext = createContext<ContextProps>({
    loading: false,
    status: '',
    gamblerIdentityContracts: [],
    rejectedIdentityContracts: [],
    pendingIdentityContracts: []
})

export const VerificationStatusProvider: FC = ({ children }) => {
    const isAuthenticated = useIsAuthenticated()
    const { partyToDAMLProvider: party } = usePartyToken()

    const gamblerIdentity = useConditionalStreamQueries(
        GamblerIdentity,
        [{ customer: party }],
        isAuthenticated
    )
    const rejectedIdentity = useConditionalStreamQueries(
        RejectedIdentity,
        [{ customer: party }],
        isAuthenticated
    )
    const pendingIdentity = useConditionalStreamQueries(
        PendingIdentity,
        [{ customer: party }],
        isAuthenticated
    )

    const isLoading = React.useMemo(
        () =>
            gamblerIdentity.loading ||
            rejectedIdentity.loading ||
            pendingIdentity.loading,
        [
            gamblerIdentity.loading,
            rejectedIdentity.loading,
            pendingIdentity.loading
        ]
    )

    const status = React.useMemo(() => {
        return getUserStatus({
            gamblerIdentity: gamblerIdentity.contracts,
            rejectedIdentity: rejectedIdentity.contracts,
            pendingIdentity: pendingIdentity.contracts,
            isLoading
        } as UserStatus)
    }, [
        gamblerIdentity.contracts,
        rejectedIdentity.contracts,
        pendingIdentity.contracts,
        isLoading
    ])

    const contextValue = React.useMemo(() => {
        return {
            gamblerIdentityContracts: gamblerIdentity.contracts,
            rejectedIdentityContracts: rejectedIdentity.contracts,
            pendingIdentityContracts: pendingIdentity.contracts,
            loading: isLoading,
            status: status
        }
    }, [
        gamblerIdentity.contracts,
        rejectedIdentity.contracts,
        pendingIdentity.contracts,
        isLoading,
        status
    ])

    return (
        <VerificationStatusContext.Provider value={contextValue}>
            {children}
        </VerificationStatusContext.Provider>
    )
}

export function useVerificationStatusContext() {
    const context = useContext<ContextProps>(VerificationStatusContext)
    if (context === undefined) {
        throw new Error(
            'useVerificationStatusContext must be used within a GamblingRoleContextProvider'
        )
    }
    return context
}
