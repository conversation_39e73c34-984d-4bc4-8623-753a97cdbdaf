import React from 'react'
import { History } from 'history'
import Credentials from 'Utils/credentials'

import { cache } from 'Utils/cache'

const CREDENTIALS_STORAGE_GAMBYL_MANAGER = 'GAMBYL_MANAGER_CRED'

let { load, save, remove } = cache({ permanent: true })

function retrieveCredentials(): Credentials | undefined {
    const credentialsJson = load(CREDENTIALS_STORAGE_GAMBYL_MANAGER)

    if (!credentialsJson) {
        return undefined
    }

    try {
        const credentials = JSON.parse(credentialsJson)
        return credentials
    } catch {
        console.error('Could not parse credentials: ', credentialsJson)
    }

    return undefined
}

function storeCredentials(credentials?: Credentials): void {
    save(CREDENTIALS_STORAGE_GAMBYL_MANAGER, JSON.stringify(credentials))
}

function clearCredentials(): void {
    remove(CREDENTIALS_STORAGE_GAMBYL_MANAGER)
}

type ManagerLoginState = {
    isAuthenticated: boolean
    party: string
    token: string
}

type ManagerLoginAction = {
    type: string
    party?: string | undefined
    token?: string | undefined
}

const ManagerLoginContext = React.createContext<ManagerLoginState>({
    isAuthenticated: false,
    party: '',
    token: ''
})
const ManagerLoginDispatchContext = React.createContext<
    React.Dispatch<ManagerLoginAction>
>({} as React.Dispatch<ManagerLoginAction>)

function userReducer(
    state: ManagerLoginState,
    action: ManagerLoginAction
): ManagerLoginState | never {
    switch (action.type) {
        case 'LOGIN_SUCCESS':
            return {
                ...state,
                isAuthenticated: true,
                party: action.party,
                token: action.token
            } as ManagerLoginState
        case 'LOGIN_FAILURE':
            return { ...state, isAuthenticated: false }
        case 'SIGN_OUT_SUCCESS':
            return { ...state, party: '', token: '', isAuthenticated: false }
        default: {
            throw new Error(`Unhandled action type: ${action.type}`)
        }
    }
}

const ManagerLoginProvider: React.FC = ({ children }) => {
    const credentials = retrieveCredentials()

    let initialUserState: ManagerLoginState = {
        isAuthenticated: false,
        party: '',
        token: ''
    }

    if (credentials) {
        initialUserState = {
            isAuthenticated: true,
            party: credentials.party,
            token: credentials.token
        }
    }

    var [state, dispatch] = React.useReducer(userReducer, initialUserState)

    return (
        <ManagerLoginContext.Provider value={state}>
            <ManagerLoginDispatchContext.Provider value={dispatch}>
                {children}
            </ManagerLoginDispatchContext.Provider>
        </ManagerLoginContext.Provider>
    )
}

function useManagerLoginState() {
    var context = React.useContext<ManagerLoginState>(ManagerLoginContext)
    if (context === undefined) {
        throw new Error(
            'useManagerLoginState must be used within a ManagerLoginProvider'
        )
    }
    return context
}

function useManagerLoginDispatch() {
    var context = React.useContext<React.Dispatch<ManagerLoginAction>>(
        ManagerLoginDispatchContext
    )
    if (context === undefined) {
        throw new Error(
            'useManagerLoginDispatch must be used within a ManagerLoginProvider'
        )
    }
    return context
}

async function loginManager(
    dispatch: React.Dispatch<ManagerLoginAction>,
    history: History,
    credentials: Credentials
) {
    const { party, token } = credentials
    try {
        storeCredentials(credentials)
        dispatch({ type: 'LOGIN_SUCCESS', party, token })
        history.push('/verification')
    } catch {
        dispatch({ type: 'LOGIN_FAILURE' })
    }
}

function signOutManagerLogin(
    dispatch: React.Dispatch<ManagerLoginAction>,
    history: History
) {
    clearCredentials()
    retrieveCredentials()
    dispatch({ type: 'SIGN_OUT_SUCCESS' })
    history.push('/')
}

export {
    ManagerLoginProvider,
    useManagerLoginState,
    useManagerLoginDispatch,
    loginManager,
    signOutManagerLogin
}
