import React, { useEffect } from 'react'
import { cache } from 'Utils/cache'
import { convertOddValue } from '../Components/MyBets/utils'
import roundNumber from '../Utils/roundNumber'
import { useOddType } from './OddTypeContext'
import usePrevious from '../Hooks/usePrevious'

const { save, remove, load } = cache()

const betsFromStorage = load('bets')

const BetsStateContext = React.createContext<BetsState>({
    bets: betsFromStorage ? JSON.parse(betsFromStorage) : [],
    showBets: true,
    showPlacement: false,
    showPlaceAllBets: false,
    activeTab: 'betslip',
    betToPlace: {}
})
const BetsDispatchContext = React.createContext<React.Dispatch<any>>(
    {} as React.Dispatch<any>
)

type BetsState = {
    bets: any | []
    showBets: boolean
    showPlacement: boolean
    betToPlace: any
    showPlaceAllBets: boolean
    activeTab: string
}

function betsReducer(state: BetsState, action: any) {
    switch (action.type) {
        case 'CHANGE ACTIVE TAB':
            return {
                ...state,
                activeTab: action.activeTab,
                showPlaceAllBets: false,
                showPlacement: false
            }
        case 'ADD BET':
            save('bets', JSON.stringify([...state.bets, action.bet]))
            return {
                ...state,
                showPlacement: false,
                showPlaceAllBets: false,
                activeTab: 'betslip',
                bets: [...state.bets, action.bet]
            }
        case 'REMOVE ALL BETS':
            remove('bets')
            return {
                ...state,
                bets: []
            }
        case 'REMOVE BET':
            const filteredBets = state.bets.filter(
                (bet: { betPlacementId: string }) =>
                    bet.betPlacementId !== action.bet.betPlacementId
            )
            save('bets', JSON.stringify(filteredBets))
            return {
                ...state,
                bets: filteredBets
            }
        case 'UPDATE BET':
            const initialBets = [...state.bets]
            const betTopBeUpdated = initialBets.findIndex(
                bet => bet.betPlacementId === action.bet.betPlacementId
            )
            initialBets[betTopBeUpdated] = action.bet
            save('bets', JSON.stringify([...initialBets]))
            return {
                ...state,
                bets: [...initialBets]
            }
        case 'SHOW BETS':
            return {
                ...state,
                showBets: true,
                activeTab: 'betslip'
            }
        case 'HIDE BETS':
            return {
                ...state,
                showBets: false
            }

        case 'SHOW PLACEMENT':
            return {
                ...state,
                betToPlace: action.betToPlace,
                showPlacement: true
            }
        case 'HIDE PLACEMENT':
            return {
                ...state,
                showPlacement: false
            }
        case 'SHOW PLACE ALL BETS':
            return {
                ...state,
                showPlaceAllBets: true
            }
        case 'HIDE PLACE ALL BETS':
            return {
                ...state,
                showPlaceAllBets: false
            }
        default: {
            throw new Error(`Unhandled action type: ${action.type}`)
        }
    }
}

const BetsProvider: React.FC = ({ children }) => {
    let initialBetsState: BetsState = {
        bets: JSON.parse(load('bets') as string) || [],
        showBets: true,
        showPlacement: false,
        betToPlace: {},
        showPlaceAllBets: false,
        activeTab: 'betslip'
    }

    var [state, dispatch] = React.useReducer(betsReducer, initialBetsState)

    const { oddState } = useOddType()
    const previousOddType = usePrevious(oddState)

    useEffect(() => {
        state.bets.forEach((bet: { odd: { value: any } }) => {
            const newOddValue = convertOddValue(
                Number(bet.odd.value),
                oddState,
                previousOddType
            )
            const updatedBet = {
                ...bet,
                odd: { tag: oddState, value: roundNumber(newOddValue, 2) }
            }
            updateBet(dispatch, updatedBet)
        })
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [oddState])

    return (
        <BetsStateContext.Provider value={state}>
            <BetsDispatchContext.Provider value={dispatch}>
                {children}
            </BetsDispatchContext.Provider>
        </BetsStateContext.Provider>
    )
}

function useBetsState() {
    let context = React.useContext<BetsState>(BetsStateContext)
    if (context === undefined) {
        throw new Error('useUserState must be used within a UserProvider')
    }
    return context
}

function useBetsDispatch() {
    var context = React.useContext<React.Dispatch<any>>(BetsDispatchContext)
    if (context === undefined) {
        throw new Error('useUserDispatch must be used within a UserProvider')
    }
    return context
}

async function addBet(dispatch: React.Dispatch<any>, bet: any) {
    dispatch({ type: 'ADD BET', bet })
}

async function removeBet(dispatch: React.Dispatch<any>, bet: any) {
    dispatch({ type: 'REMOVE BET', bet })
}

async function removeAllBets(dispatch: React.Dispatch<any>) {
    dispatch({ type: 'REMOVE ALL BETS' })
}

async function updateBet(dispatch: React.Dispatch<any>, bet: any) {
    dispatch({ type: 'UPDATE BET', bet })
}

async function showBets(dispatch: React.Dispatch<any>) {
    dispatch({ type: 'SHOW BETS' })
}

async function hideBets(dispatch: React.Dispatch<any>) {
    dispatch({ type: 'HIDE BETS' })
}
async function showPlacement(dispatch: React.Dispatch<any>, betToPlace: any) {
    dispatch({ type: 'SHOW PLACEMENT', betToPlace })
}
async function hidePlacement(dispatch: React.Dispatch<any>) {
    dispatch({ type: 'HIDE PLACEMENT' })
}

async function showPlaceAll(dispatch: React.Dispatch<any>) {
    dispatch({ type: 'SHOW PLACE ALL BETS' })
}
async function hidePlaceAll(dispatch: React.Dispatch<any>) {
    dispatch({ type: 'HIDE PLACE ALL BETS' })
}

async function handleActiveTab(
    dispatch: React.Dispatch<any>,
    activeTab: string
) {
    dispatch({ type: 'CHANGE ACTIVE TAB', activeTab })
}

export {
    BetsProvider,
    useBetsState,
    useBetsDispatch,
    addBet,
    removeBet,
    showBets,
    hideBets,
    removeAllBets,
    updateBet,
    showPlacement,
    hidePlacement,
    showPlaceAll,
    hidePlaceAll,
    handleActiveTab
}
