import React from 'react'
import { Account } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import { useLedger, useParty } from '@daml/react'
import { CreateEvent, Stream } from '@daml/ledger'
import useIsAuthenticated from 'Hooks/useAuth'

type TResults = readonly CreateEvent<Account, Account.Key, string>[]

const AccountContext = React.createContext<{
    accountContracts: readonly CreateEvent<Account, Account.Key, string>[]
}>({
    accountContracts: []
})

function AccountContextProvider({ children }: { children: React.ReactNode }) {
    const party = useParty()
    const ledger = useLedger()
    const isAuthenticated = useIsAuthenticated()
    const [results, setResults] = React.useState<TResults>([])
    const memoAccounts = React.useMemo(() => {
        return results
    }, [results])

    function handleChange(contracts: TResults) {
        setResults(contracts)
    }

    React.useEffect(() => {
        let stream: Stream<
            Account,
            Account.Key,
            string,
            readonly CreateEvent<Account, Account.Key, string>[]
        > | null = null
        if (isAuthenticated) {
            stream = ledger.streamQueries(Account, [{ customer: party }])
            stream.on('live', () => console.log('live Account stream'))
            stream.on('change', data => handleChange(data))
            stream.on('close', () => console.log('closed Account stream'))
        }
        if (!isAuthenticated && stream) {
            stream.close()
            stream = null
        }
        return () => {
            if (stream) {
                stream.close()
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAuthenticated, ledger])

    return (
        <AccountContext.Provider value={{ accountContracts: memoAccounts }}>
            {children}
        </AccountContext.Provider>
    )
}

function useAccountContex() {
    const context = React.useContext(AccountContext)
    if (context === undefined) {
        throw new Error(
            'useAccountContex must be used within an AccountContextProvider'
        )
    }
    return context
}

export { useAccountContex, AccountContextProvider }
