import { isLocalDev } from 'config'
import { Navigate, Route, Routes as Routez, with<PERSON>out<PERSON> } from 'react-router-dom'

import AccountClose from 'Components/AccountClose'
import BlockedAccount from 'Components/BlockedAccount'
import ContactUs from 'Components/ContactUs'
import NotFound from 'Components/ErrorPages/NotFound'
import Unauthorized from 'Components/ErrorPages/Unauthorized'
import Security from 'Components/Security'
import AboutUs from 'Containers/AboutUs'
import Account from 'Containers/Account'
import EditProfile from 'Containers/Account/Edit'
import Admin from 'Containers/Admin'
import EditEventPage from 'Containers/Admin/EditEvents/EditPage'
import OddsUpdatePage from 'Containers/Admin/OddsUpdate/OddUpdatePage'
import UpdatePromotionForm from 'Containers/Admin/UpdatePromotions/UpdateForm'
import { CallbackPage } from 'Containers/AuthCallbackPage'
import CheckVerify from 'Containers/CheckVerify'
import Homepage from 'Containers/Dashboard'
import Deposit from 'Containers/Deposit'
import EventMarketingManagerLogin from 'Containers/EventMarketingManagerLogin'
import FeaturedEvent from 'Containers/FeaturedEvent'
import Intro from 'Containers/Intro'
import FailureKyc from 'Containers/kycCallbackPages/failureKyc'
import SuccessKyc from 'Containers/kycCallbackPages/successKyc'
import KYCVerification from 'Containers/KYCVerification'
import LocalCreds from 'Containers/LocalCreds'
import Promotions from 'Containers/Promotions'
import SignIn from 'Containers/SignIn'
import SingleEvent from 'Containers/SingleEvent'
import GamblerUnverifiedIdentity from 'Containers/UnverifiedGamblerIdentity'
import Withdrawal from 'Containers/Withdrawal'
import useIsAuthenticated from 'Hooks/useAuth'
import MarketRoutes from 'MarketRoutes'
import Features from './Containers/Features'

function EventRoutes() {
    return <MarketRoutes />
}

function PromotionRoute() {
    return <Promotions />
}

function Routes() {
    const isAuthenticated = useIsAuthenticated()

    return (
        <Routez>
            <Route exact path="/">
                <Homepage />
            </Route>
            <Route
                render={() =>
                    isAuthenticated ? (
                        <Admin />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
                path="/admin"
            />
            <Route
                render={() =>
                    isAuthenticated ? (
                        <EditEventPage />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
                path="/admin-update-event/:assetLabel"
            />
            <Route
                render={() =>
                    isAuthenticated ? (
                        <UpdatePromotionForm />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
                path="/update-promotion/:promotionId"
            />
            <Route path="/unauthorized" component={Unauthorized} />
            <Route
                render={() =>
                    isLocalDev ? (
                        <SignIn />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
                path="/signin"
            />
            <Route path={'/gambylmanagerlogin'}>
                <EventMarketingManagerLogin />
            </Route>
            <Route path="/verification">
                <CheckVerify />
            </Route>
            <Route
                path="/kycverification"
                render={() =>
                    isAuthenticated ? (
                        <KYCVerification />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route
                path="/identity_setup"
                render={() =>
                    isAuthenticated ? (
                        <GamblerUnverifiedIdentity />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route
                path="/account"
                render={() =>
                    isAuthenticated ? (
                        <Account />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route
                path="/first_deposit"
                render={() =>
                    isAuthenticated ? (
                        <Deposit />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route
                path="/deposit"
                render={() =>
                    isAuthenticated ? (
                        <Deposit />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route
                path="/withdrawal"
                render={() =>
                    isAuthenticated ? (
                        <Withdrawal />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route
                path="/intro"
                render={() =>
                    isAuthenticated ? (
                        <Intro />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route
                path="/edit/profile"
                render={() =>
                    isAuthenticated ? (
                        <EditProfile />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route path="/about">
                <AboutUs />
            </Route>
            <Route path="/security">
                <Security />
            </Route>
            <Route path="/contact">
                <ContactUs />
            </Route>
            <Route path="/promotions">
                <PromotionRoute />
            </Route>
            <Route path="/features">
                <Features />
            </Route>
            <Route
                path="/close-account"
                render={() =>
                    isAuthenticated ? (
                        <AccountClose />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
            />
            <Route path="/popular/:market/:code/:tournament">
                <FeaturedEvent />
            </Route>
            <Route path="/events">
                <EventRoutes />
            </Route>
            <Route path="/:sport/:tournament/:eventTitle/:assetLabel">
                <SingleEvent />
            </Route>
            <Route path="/blocked-account">
                <BlockedAccount />
            </Route>
            <Route path="/edit-odd/:eventId">
                <OddsUpdatePage />
            </Route>
            <Route
                render={() =>
                    isLocalDev ? (
                        <LocalCreds />
                    ) : (
                        <Navigate to="/unauthorized" replace />
                    )
                }
                path="/localcreds"
            />
            {/** CHANGE HERE AUTH0 CALLBACK Local DEV */}
            <Route
                render={() =>
                    isLocalDev ? (
                        <Navigate to="/unauthorized" replace />
                    ) : (
                        <CallbackPage />
                    )
                }
                path="/callback"
            />
            <Route path="/success-kyc">
                <SuccessKyc />
            </Route>
            <Route path="/failed-kyc">
                <FailureKyc />
            </Route>
            <Route path="*">
                <NotFound />
            </Route>
        </Routez>
    )
}

export default withRouter(Routes)
