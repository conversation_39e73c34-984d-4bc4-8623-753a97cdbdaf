import { isLocalDev } from 'config'
import React from 'react'
import { Redirect, Route, Switch, withRouter } from 'react-router-dom'

import AccountClose from 'Components/AccountClose'
import BlockedAccount from 'Components/BlockedAccount'
import ContactUs from 'Components/ContactUs'
import NotFound from 'Components/ErrorPages/NotFound'
import Unauthorized from 'Components/ErrorPages/Unauthorized'
import Security from 'Components/Security'
import AboutUs from 'Containers/AboutUs'
import Account from 'Containers/Account'
import Admin from 'Containers/Admin'
import CheckVerify from 'Containers/CheckVerify'
import Homepage from 'Containers/Dashboard'
import Deposit from 'Containers/Deposit'
import FeaturedEvent from 'Containers/FeaturedEvent'
import KYCVerification from 'Containers/KYCVerification'
import Promotions from 'Containers/Promotions'
import SignIn from 'Containers/SignIn'
import SingleEvent from 'Containers/SingleEvent'
import Withdrawal from 'Containers/Withdrawal'
import MarketRoutes from 'MarketRoutes'
import Features from './Containers/Features'
import GamblerUnverifiedIdentity from 'Containers/UnverifiedGamblerIdentity'
import LocalCreds from 'Containers/LocalCreds'
import { CallbackPage } from 'Containers/AuthCallbackPage'
import useIsAuthenticated from 'Hooks/useAuth'
import EventMarketingManagerLogin from 'Containers/EventMarketingManagerLogin'
import EditEventPage from 'Containers/Admin/EditEvents/EditPage'
import OddsUpdatePage from 'Containers/Admin/OddsUpdate/OddUpdatePage'
import Intro from 'Containers/Intro'
import UpdatePromotionForm from 'Containers/Admin/UpdatePromotions/UpdateForm'
import SuccessKyc from 'Containers/kycCallbackPages/successKyc'
import FailureKyc from 'Containers/kycCallbackPages/failureKyc'
import EditProfile from 'Containers/Account/Edit'

function EventRoutes() {
    return <MarketRoutes />
}

function PromotionRoute() {
    return <Promotions />
}

function Routes() {
    const isAuthenticated = useIsAuthenticated()

    return (
        <Switch>
            <Route exact path="/">
                <Homepage />
            </Route>
            <Route
                render={() =>
                    isAuthenticated ? (
                        <Admin />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
                path="/admin"
            />
            <Route
                render={() =>
                    isAuthenticated ? (
                        <EditEventPage />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
                path="/admin-update-event/:assetLabel"
            />
            <Route
                render={() =>
                    isAuthenticated ? (
                        <UpdatePromotionForm />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
                path="/update-promotion/:promotionId"
            />
            <Route path="/unauthorized" component={Unauthorized} />
            <Route
                render={() =>
                    isLocalDev ? <SignIn /> : <Redirect to="/unauthorized" />
                }
                path="/signin"
            />
            <Route path={'/gambylmanagerlogin'}>
                <EventMarketingManagerLogin />
            </Route>
            <Route path="/verification">
                <CheckVerify />
            </Route>
            <Route
                path="/kycverification"
                render={() =>
                    isAuthenticated ? (
                        <KYCVerification />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route
                path="/identity_setup"
                render={() =>
                    isAuthenticated ? (
                        <GamblerUnverifiedIdentity />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route
                path="/account"
                render={() =>
                    isAuthenticated ? (
                        <Account />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route
                path="/first_deposit"
                render={() =>
                    isAuthenticated ? (
                        <Deposit />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route
                path="/deposit"
                render={() =>
                    isAuthenticated ? (
                        <Deposit />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route
                path="/withdrawal"
                render={() =>
                    isAuthenticated ? (
                        <Withdrawal />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route
                path="/intro"
                render={() =>
                    isAuthenticated ? (
                        <Intro />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route
                path="/edit/profile"
                render={() =>
                    isAuthenticated ? (
                        <EditProfile />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route path="/about">
                <AboutUs />
            </Route>
            <Route path="/security">
                <Security />
            </Route>
            <Route path="/contact">
                <ContactUs />
            </Route>
            <Route path="/promotions">
                <PromotionRoute />
            </Route>
            <Route path="/features">
                <Features />
            </Route>
            <Route
                path="/close-account"
                render={() =>
                    isAuthenticated ? (
                        <AccountClose />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
            />
            <Route path="/popular/:market/:code/:tournament">
                <FeaturedEvent />
            </Route>
            <Route path="/events">
                <EventRoutes />
            </Route>
            <Route path="/:sport/:tournament/:eventTitle/:assetLabel">
                <SingleEvent />
            </Route>
            <Route path="/blocked-account">
                <BlockedAccount />
            </Route>
            <Route path="/edit-odd/:eventId">
                <OddsUpdatePage />
            </Route>
            <Route
                render={() =>
                    isLocalDev ? (
                        <LocalCreds />
                    ) : (
                        <Redirect to="/unauthorized" />
                    )
                }
                path="/localcreds"
            />
            {/** CHANGE HERE AUTH0 CALLBACK Local DEV */}
            <Route
                render={() =>
                    isLocalDev ? (
                        <Redirect to="/unauthorized" />
                    ) : (
                        <CallbackPage />
                    )
                }
                path="/callback"
            />
            <Route path="/success-kyc">
                <SuccessKyc />
            </Route>
            <Route path="/failed-kyc">
                <FailureKyc />
            </Route>
            <Route path="*">
                <NotFound />
            </Route>
        </Switch>
    )
}

export default withRouter(Routes)
