@import '../../Styles/colors';

.header {
    background-color: $black;
    display: grid;
    grid-template-columns: 240px 1fr auto;
    grid-template-areas: 'logo nav actions';
    padding: 0 18px;
    color: $white;
    z-index: 200;
    height: 70px;

    @media (max-width: 800px) {
        grid-template-columns: 200px 1fr 1fr;
        grid-template-areas: 'logo actions actions';
    }

    @media (max-width: 450px) {
        grid-template-columns: 150px 1fr 1fr;
        grid-template-areas: 'logo actions actions';
    }

    @media (max-width: 355px) {
        grid-template-columns: 110px 1fr 1fr;
        grid-template-areas: 'logo actions actions';
    }

    &__left {
        grid-area: logo;
        display: flex;
        align-items: center;

        img {
            width: 100%;
            max-width: 240px;
            height: auto;
            max-height: 50px;
            object-fit: cover;
        }
    }

    &__nav {
        grid-area: nav;
        display: flex;
        align-items: center;

        ul {
            display: flex;
            align-items: center;
            gap: 20px;
            list-style: none;

            li {
                a,
                span {
                    color: $white;
                    text-decoration: none;
                    font-size: 1rem;
                    cursor: pointer;
                }
            }
        }
        //CHANGE HERE
        @media (max-width: 1210px) {
            display: none;
        }
    }

    &__right {
        grid-area: actions;
        display: flex;
        justify-content: flex-end;
        gap: 20px;
        align-items: center;
        @media (max-width: 1000px) {
            gap: 10px;
        }
        @media (max-width: 800px) {
            gap: 7px;
        }
    }

    &__action {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        font-size: 0.8rem;

        @media (max-width: 800px) {
            margin-right: 10px;
        }
    }

    &__btn {
        cursor: pointer;
        padding: 10px 15px;
        border: none;
        background-color: $purple;
        color: $white;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
            border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        font-family: 'Montserrat-Bold', sans-serif;

        &:hover {
            opacity: 0.8;
        }

        @media (max-width: 1000px) {
            font-size: 0.8rem;
            line-height: 1.2;
        }
        @media (max-width: 400px) {
            font-size: 0.6rem;
            line-height: 1.2;
        }
    }

    &__btn--secondary {
        cursor: pointer;
        padding: 8px 15px;
        border-color: $purple;
        background-color: $white;
        color: $purple;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
            border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        font-family: 'Montserrat-Bold', sans-serif;

        &:hover {
            opacity: 0.8;
        }

        @media (max-width: 1000px) {
            font-size: 0.8rem;
            line-height: 1.2;
        }
        @media (max-width: 400px) {
            font-size: 0.6rem;
            line-height: 1.2;
        }
    }
}
