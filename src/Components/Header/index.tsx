import { useAuth0 } from '@auth0/auth0-react'
import { faFileInvoiceDollar } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useContext } from 'react'
import TagManager from 'react-gtm-module'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'

import BurgerBtn from 'Components/BurgerBtn'
import { helpLinks } from 'Components/Footer/links.utils'
import Notifications from 'Components/Notification'
import AccountDropdown from '../AccountDropdown'
import FlagSelect from '../FlagSelect'

import useIsAuthenticated from 'Hooks/useAuth'
import useLoginRedirection from 'Hooks/useLoginRedirection'
import useMediaQuery from 'Hooks/useMediaQuery'
import {
    hideBets,
    showBets,
    useBetsDispatch,
    useBetsState
} from 'State/BetsContext'
import { useI18LanguageContext } from 'State/LanguageState'
import { AppContext } from '../../State/AppProvider'

import { generateLogoWithLang } from 'Utils/generateLogoWithLang'

import { logEvent } from 'Utils/analytics'
import { signUpParams } from '../../Constants/Auth0Constants'
import './style.scss'

const nationLinks = {
    US: 'https://gambyl.com/en/',
    BR: 'https://gambyl.com/pt/',
    MX: 'https://gambyl.com/es/'
}

export default function Header() {
    const { loginWithRedirect } = useAuth0()
    const { showBets: isBetSlipOpen } = useBetsState()
    const betsDispatch = useBetsDispatch()
    const isAuthenticated = useIsAuthenticated()
    const { setShowSideMenu } = useContext(AppContext)
    const { lang } = useI18LanguageContext()
    const { t } = useTranslation()

    //CHANGE HERE
    const displayHamburguer = useMediaQuery('(max-width: 1210px)')
    const displayName = useMediaQuery('(max-width: 1082px)')
    const login = useLoginRedirection()

    const handleLogin = () => {
        login()
        logEvent('login')
        TagManager.dataLayer({
            dataLayer: {
                event: 'sign_in_click'
            }
        })
    }

    const handleSignUp = async () => await loginWithRedirect(signUpParams)

    const handleOpenLinkByLang = ({
        US,
        BR,
        MX
    }: {
        US: string
        BR: string
        MX: string
    }) => {
        let getLinkToOpenByLang = lang === 'MX' ? MX : lang === 'BR' ? BR : US
        window.open(getLinkToOpenByLang)
    }

    async function handleMyBets() {
        if (isBetSlipOpen) {
            return await hideBets(betsDispatch)
        } else {
            if (displayHamburguer) {
                setShowSideMenu && setShowSideMenu(false)
            }
            await showBets(betsDispatch)
        }
    }

    const handleClickLogo = async () => {
        if (displayHamburguer) {
            return await hideBets(betsDispatch)
        }
        return
    }

    return (
        <header className="header">
            <div className="header__left">
                {displayHamburguer && <BurgerBtn />}
                <div className="header__logo">
                    <Link to="/" onClick={() => handleClickLogo()}>
                        <img src={generateLogoWithLang(lang)} alt="Logo" />{' '}
                    </Link>
                </div>
            </div>
            <div className="header__nav">
                <ul>
                    <li>
                        <Link to="/">{t('HSports')}</Link>
                    </li>
                    <li>
                        <span
                            onClick={() =>
                                window.open('https://casino.gambyl.com/')
                            }
                        >
                            {t('HCasino')}
                        </span>
                    </li>
                    <li>
                        <span
                            onClick={() =>
                                window.open(
                                    'https://casino.gambyl.com/casino/categories/136'
                                )
                            }
                        >
                            {t('HLiveCasino')}
                        </span>
                    </li>
                    <li>
                        <Link to="/promotions">{t('HPromotions')}</Link>
                    </li>
                    <li>
                        <span onClick={() => handleOpenLinkByLang(helpLinks)}>
                            {t('HHelp')}
                        </span>
                    </li>
                    <li>
                        <span onClick={() => handleOpenLinkByLang(nationLinks)}>
                            {t('HNews')}
                        </span>
                    </li>
                </ul>
            </div>
            <div className="header__right">
                {isAuthenticated && <Notifications />}
                <span
                    className="header__action"
                    onClick={async () => await handleMyBets()}
                    style={{ color: 'white', cursor: 'pointer' }}
                >
                    <FontAwesomeIcon
                        style={{ fontSize: '1rem' }}
                        icon={faFileInvoiceDollar}
                    />
                    <p>{!displayName && t('HBets')}</p>
                </span>
                {!isAuthenticated && (
                    <>
                        <button
                            className="header__btn--secondary"
                            aria-pressed="true"
                            onClick={handleSignUp}
                            type="button"
                        >
                            {t('SignUpBtn')}
                        </button>

                        <button
                            className="header__btn"
                            aria-pressed="true"
                            onClick={handleLogin}
                            type="button"
                        >
                            {t('SingInBtn')}
                        </button>
                    </>
                )}
                {isAuthenticated && <AccountDropdown />}
                <FlagSelect />
            </div>
        </header>
    )
}
