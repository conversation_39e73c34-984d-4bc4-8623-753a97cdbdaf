import usePrevious from 'Hooks/usePrevious'
import React from 'react'
import TagManager from 'react-gtm-module'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'
import { logEvent } from '../../Utils/analytics'

export default function AppContainer({
    children
}: {
    children: React.ReactNode
}) {
    const { status } = useVerificationStatusContext()

    const previousStatus = usePrevious(status)

    React.useEffect(() => {
        if (previousStatus === 'pending' && status === 'success') {
            logEvent('kyc_verification_complete')

            return TagManager.dataLayer({
                dataLayer: {
                    event: 'kyc_verification_complete'
                }
            })
        }
        return () => {}
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [status])
    return (
        <>
            <div className="app__container">{children}</div>
        </>
    )
}
