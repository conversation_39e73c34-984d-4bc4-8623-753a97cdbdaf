import React, { useContext } from 'react'
import { AppContext } from '../../State/AppProvider'
import { hideBets, useBetsDispatch } from 'State/BetsContext'

import './style.scss'

export default function BurgerBtn() {
    const betsDispatch = useBetsDispatch()
    const { showSideMenu, setShowSideMenu } = useContext(AppContext)

    async function handleBurgerBtn() {
        setShowSideMenu && setShowSideMenu(!showSideMenu)
        await hideBets(betsDispatch)
    }

    return (
        <div
            role="button"
            className="burgerBtn"
            onClick={async () => await handleBurgerBtn()}
        >
            <span
                className={
                    showSideMenu ? 'burgerBtn__item open' : 'burgerBtn__item'
                }
            />
            <span
                className={
                    showSideMenu ? 'burgerBtn__item open' : 'burgerBtn__item'
                }
            />
            <span
                className={
                    showSideMenu ? 'burgerBtn__item open' : 'burgerBtn__item'
                }
            />
        </div>
    )
}
