@import '../../Styles/colors';

.burgerBtn{
  width: 24px;
  height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  cursor: pointer;
  gap: 2px;
  z-index: 3;

  &__item{
    display: block;
    width: 16px;
    height: 2px;
    background-color: $purple;
    transition: all .3s;
    &.open {
      margin-bottom: 0;
      &:first-child {
        transform: rotate(45deg) translate(2px, 3px);
      }
      &:nth-child(2) {
        background-color: transparent;
      }
      &:last-child{
        transform: rotate(-45deg) translate(3px,-4px);
      }
    }
  }
}