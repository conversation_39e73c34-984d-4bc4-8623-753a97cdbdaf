import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

export default function Security() {
    const { t } = useTranslation()
    useEffect(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' })
        return () => {}
    }, [])

    return (
        <div className="content__container documents pagePadding">
            <h1>{t('SecTitle')}</h1>
            <section className="security__container">
                <div className="security__article">
                    <h2>{t('Sec1Title')}</h2>
                    <p>{t('Sec1Text')}</p>
                </div>
                <div className="security__article">
                    <h2>{t('Sec2title')}</h2>
                    <p>{t('Sec2Text')}</p>
                </div>
                <div className="security__article">
                    <h2> {t('Sec3Title')}</h2>
                    <p>{t('Sec3Text')}</p>
                    <h3>{t('SecEncryptionTitle')}</h3>
                    <p>{t('SecEncryptionText')}</p>
                    <h3>{t('SecBrowserTitle')}</h3>
                    <p>{t('SecBrowserText')}</p>
                    <h3>{t('SecIDTitle')}</h3>
                    <p>{t('SecIDText')}</p>
                    <h3>{t('SecSessionTitle')}</h3>
                    <p>{t('SecSessionText')}</p>
                    <h3>{t('SecIdentityTitle')}</h3>
                    <p>{t('SecIdentityText')}</p>
                    <h3>{t('SecSecuredTitle')}</h3>
                    <p>{t('SecSecuredText')} </p>
                </div>
                <div className="security__article">
                    <h2>{t('SecConcernsTitle')}</h2>
                    <p>{t('SecConcernsText')}</p>
                </div>
            </section>
        </div>
    )
}
