import React from 'react'
import './style.scss'

interface ITabsForHistory {
    options: {
        label: string
        value: string
    }[]
    setSelectedValue: (value: any) => void
    selectedOption: string
}

export default function SelectFilter({
    options,
    selectedOption,
    setSelectedValue
}: ITabsForHistory) {
    const handleChange = (event: React.FormEvent<HTMLSelectElement>) => {
        setSelectedValue(event.currentTarget.value)
    }
    return (
        <select
            onChange={(event: React.FormEvent<HTMLSelectElement>) =>
                handleChange(event)
            }
            value={selectedOption}
            className="select__selectFilter"
        >
            {React.Children.toArray(
                options.map(option => (
                    <option value={option.value}>{option.label}</option>
                ))
            )}
        </select>
    )
}
