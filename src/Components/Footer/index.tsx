import React from 'react'
import { Link } from 'react-router-dom'

import over18icon from 'Assets/18plus.png'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
    faFacebookSquare,
    faTwitterSquare,
    faInstagram
} from '@fortawesome/free-brands-svg-icons'
import { useTranslation } from 'react-i18next'
import { useI18LanguageContext } from 'State/LanguageState'
import {
    helpLinks,
    PrivacyPolicyLinks,
    ResponsibleGamingLinks,
    howtodepositfundsLinks,
    TermsandConditionsLinks,
    AntiMoneyLaunderingPolicyLinks,
    accountVerificationLnks,
    howtomakeawithdrawalLinks,
    DataSafetyandSecurityLinks,
    sportsBettingExchangeLinks,
    bettingFAQSLinks,
    CookiePolicyLinks
} from './links.utils'

import './style.scss'
import { generateLogoWithLang } from 'Utils/generateLogoWithLang'
import { isProd } from 'config'

export default function Footer() {
    const { t } = useTranslation()

    const { lang } = useI18LanguageContext()

    const handleLangLink = ({
        US,
        BR,
        MX
    }: {
        US: string
        BR: string
        MX: string
    }) => (lang === 'MX' ? MX : lang === 'BR' ? BR : US)

    React.useEffect(() => {
        if (isProd) {
            // Create the first script element
            const script1 = document.createElement('script')
            script1.type = 'text/javascript'
            script1.id = 'pap_x2s6df8d'
            script1.src =
                'https://thesportsmarket.postaffiliatepro.com/scripts/ezgvmnjf9'
            document.body.appendChild(script1)

            // Create the second script element
            const script2 = document.createElement('script')
            script2.type = 'text/javascript'
            script2.innerHTML = `
   window?.PostAffTracker?.setAccountId('default1');
   try {
       window?.PostAffTracker?.track();
   } catch (err) {}
 `
            document.body.appendChild(script2)
            // Cleanup function to remove scripts on component unmount
            return () => {
                document.body.removeChild(script1)
                document.body.removeChild(script2)
            }
        }
    }, [])

    return (
        <footer className="footer">
            <div className="footer__container">
                <div className="footer__cards">
                    <div className="footer__card general">
                        <img
                            src={generateLogoWithLang(lang)}
                            alt="Gambyl Logo"
                        />
                        <div className="footer__socialmedia">
                            <ul>
                                <li>
                                    <FontAwesomeIcon
                                        icon={faFacebookSquare}
                                        onClick={() =>
                                            window.open(
                                                'https://www.facebook.com/realgambyl/',
                                                '_blank'
                                            )
                                        }
                                    />
                                </li>
                                <li>
                                    <FontAwesomeIcon
                                        icon={faTwitterSquare}
                                        onClick={() =>
                                            window.open(
                                                'https://twitter.com/realgambyl',
                                                '_blank'
                                            )
                                        }
                                    />
                                </li>
                                <li>
                                    <FontAwesomeIcon
                                        icon={faInstagram}
                                        onClick={() =>
                                            window.open(
                                                'https://www.instagram.com/realgambyl_es/',
                                                '_blank'
                                            )
                                        }
                                    />
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div className="footer__card  about">
                        <h4>{t('FAbout')} Gambyl</h4>
                        <ul>
                            <li>
                                <Link to="/about">{t('FAboutUs')}</Link>
                            </li>
                            <li>
                                <Link to="/contact">{t('FContactUs')}</Link>
                            </li>
                        </ul>
                    </div>
                    <div className="footer__card helpArea">
                        <h4>{t('FFAQS')}</h4>
                        <ul>
                            <li>
                                <a
                                    href={handleLangLink(helpLinks)}
                                    target="_blank"
                                    rel="noreferrer"
                                >
                                    {t('FHelp')}
                                </a>
                            </li>
                            <li>
                                <a
                                    href={handleLangLink(
                                        accountVerificationLnks
                                    )}
                                    target="_blank"
                                    rel="noreferrer"
                                >
                                    {t('FAccountVerification')}
                                </a>
                            </li>
                            <li>
                                <a
                                    href={handleLangLink(
                                        howtodepositfundsLinks
                                    )}
                                    target="_blank"
                                    rel="noreferrer"
                                >
                                    {t('FHowtodepositfunds')}
                                </a>
                            </li>
                            <li>
                                <a
                                    href={handleLangLink(
                                        howtomakeawithdrawalLinks
                                    )}
                                    target="_blank"
                                    rel="noreferrer"
                                >
                                    {t('FHowtomakeawithdrawal')}
                                </a>
                            </li>
                            <li>
                                <a
                                    href={handleLangLink(bettingFAQSLinks)}
                                    target="_blank"
                                    rel="noreferrer"
                                >
                                    {t('FBettingFAQS')}
                                </a>
                            </li>
                            <li>
                                <a
                                    target="_blank"
                                    rel="noreferrer"
                                    href={handleLangLink(
                                        sportsBettingExchangeLinks
                                    )}
                                >
                                    {t('WhatisASportsBettingExchange')}
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div className="footer__card privacy">
                        <h4>{t('FRulesandRegulations')}</h4>
                        <ul>
                            <li>
                                <a
                                    target="_blank"
                                    rel="noreferrer"
                                    href={handleLangLink(
                                        TermsandConditionsLinks
                                    )}
                                >
                                    {t('FTermsandConditions')}
                                </a>
                            </li>
                            <li>
                                <a
                                    target="_blank"
                                    rel="noreferrer"
                                    href={handleLangLink(PrivacyPolicyLinks)}
                                >
                                    {t('FPrivacyPolicy')}
                                </a>
                            </li>
                            <li>
                                <a
                                    target="_blank"
                                    rel="noreferrer"
                                    href={handleLangLink(CookiePolicyLinks)}
                                >
                                    {t('FCookiePolicy')}
                                </a>
                            </li>
                            <li>
                                <a
                                    target="_blank"
                                    rel="noreferrer"
                                    href={handleLangLink(
                                        DataSafetyandSecurityLinks
                                    )}
                                >
                                    {t('FDataSafetyandSecurity')}
                                </a>
                            </li>
                            <li>
                                <a
                                    target="_blank"
                                    rel="noreferrer"
                                    href={handleLangLink(
                                        ResponsibleGamingLinks
                                    )}
                                >
                                    {t('FResponsibleGaming')}
                                </a>
                            </li>
                            <li>
                                <a
                                    target="_blank"
                                    rel="noreferrer"
                                    href={handleLangLink(
                                        AntiMoneyLaunderingPolicyLinks
                                    )}
                                >
                                    {t('FAntiMoneyLaunderingPolicy')}
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div className="footer__card footer__licenses mobile">
                        <h4>{t('FLicenses')}</h4>
                        <div className="footer__images">
                            {/* eslint-disable-next-line */}
                            <a
                                /* eslint-disable-next-line */
                                onClick={() =>
                                    window.open(
                                        'https://certificates.gamingcommission.ca/KGCCertificates.aspx?id=fd48c951-8894-4d91-8f00-f3dc97a0280a',
                                        '27BrandNameCertificates',
                                        'location=0,status=0,scrollbars=1'
                                    )
                                }
                            >
                                <img
                                    className="footer__images--kahnawake"
                                    style={{ border: 'none' }}
                                    src="https://certificates.gamingcommission.ca/Members/Pages/Certificates/GeneratedCertificates/logo.gif"
                                    alt="certificate kahnawake comission"
                                />
                            </a>

                            <img
                                src={over18icon}
                                className="footer__images--over18"
                                alt="over 18 logo"
                            />
                        </div>
                    </div>
                </div>
                <div className="footer__disclaimer">
                    <p>
                        Gambyl {t('FGambyl')} Sports Market Group Inc., a BVI
                        company.
                    </p>
                    <p>
                        {t('FDisclaimerTitle')}: {t('FDisclaimer')}
                    </p>
                </div>
            </div>
        </footer>
    )
}
