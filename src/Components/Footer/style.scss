@import '../../Styles/colors';

.footer {
    background: $black;
    color: $white;
    font-size: 1.125rem;

    &__container {
        margin: auto;
        padding: 50px 0;
        // width: 90%;
    }

    &__cards {
        display: grid;
        gap: 18px;
        grid-template-columns: repeat(auto-fit, minmax(216px, 1fr));
    }

    &__card {
        padding: 0 15px;

        h4 {
            padding-bottom: 20px;
        }

        p {
            color: $gray;
            font-size: 0.875rem;
            line-height: 1.5rem;
            max-width: 200px;
        }

        p + p {
            padding-top: 10px;
        }

        img {
            max-width: 100%;
            padding-bottom: 10px;
        }

        ul {
            color: $gray;
            display: flex;
            flex-direction: column;

            li {
                list-style: none;
                a {
                    color: inherit;
                    font-size: 0.875rem;
                    line-height: 1.5rem;
                    text-decoration: none;
                }
            }
        }
    }

    &__socialmedia {
        ul {
            color: $purple;
            display: flex;
            flex-direction: row;
            font-size: 1.25rem;
            gap: 15px;
            padding-top: 30px;

            li {
                cursor: pointer;
                list-style: none;
            }
        }
    }

    &__disclaimer {
        color: $gray;
        font-size: 0.875rem;
        line-height: 1.5rem;
        padding: 30px 15px 0;
    }

    &__licenses {
        display: flex;
        flex-direction: column;
    }

    &__images {
        display: flex;
        align-items: center;
        gap: 5px;
        max-width: 200px;

        &--kahnawake {
            width: 150px;
        }

        &--over18 {
            width: 30px;
            height: 40px;
        }
    }
}

@media (max-width: 765px) {
    .general {
        grid-area: general;
    }
    .about {
        grid-area: about;
    }
    .helpArea {
        grid-area: helpArea;
    }
    .privacy {
        grid-area: privacy;
    }
    .mobile {
        grid-area: mobile;
    }

    .footer {
        &__images {
            &--kahnawake {
                width: 90px;
            }

            &--over18 {
                width: 30px;
                height: 40px;
            }
        }
        &__cards {
            grid-template-columns: repeat(2, 1fr);
            grid-template-areas:
                'about helpArea'
                'privacy .'
                'general mobile';
        }
    }
}
