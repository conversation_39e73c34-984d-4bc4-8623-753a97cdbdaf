@import '../../Styles/colors';

.errorPage {
    padding: 50px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    line-height: 4rem;
    height: 100vh;

    &__title {
        font-size: 3rem;
    }

    &__subtitle {
        font-size: 2rem;
    }

    &__text {
        font-size: 1.2rem;
    }

    &__btn {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        max-width: 250px;
        width: 100%;
        max-height: 50px;
        height: 100%;
        border: none;
        border-radius: 6px;
        font-size: 1rem;
        padding: 5px 15px;
        color: $white;
        background-color: $purple;
        cursor: pointer;
        transition: filter 0.2s;

        &:hover {
            filter: brightness(115%);
        }
    }
}
