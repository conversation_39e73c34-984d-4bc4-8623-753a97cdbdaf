import React from 'react'
import { useSelectedPromotionContext } from 'State/SelectPromotion'

const useCleanUpPromotionStateOnClose = () => {
    const [, setSelectedPromotion] = useSelectedPromotionContext()
    React.useEffect(() => {
        return () => {
            setSelectedPromotion(null)
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
}

export default useCleanUpPromotionStateOnClose
