import React from 'react'
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useNavigate } from 'react-router-dom'
import ReactTooltip from 'react-tooltip'
import { useTranslation } from 'react-i18next'

export const TooltipPromoDropdown = () => {
    const { push } = useNavigate()
    const { t } = useTranslation()
    return (
        <>
            <span data-tip="" data-for="test" data-event="click">
                {' '}
                <FontAwesomeIcon icon={faInfoCircle} />
            </span>
            <ReactTooltip
                clickable
                class="tooltipMaxWidthMobile"
                id="test"
                globalEventOff="click"
                getContent={() => {
                    return (
                        <span>
                            {t('PromotionIcon1')}{' '}
                            <span
                                style={{
                                    cursor: 'pointer',
                                    textDecoration: 'underline'
                                }}
                                onClick={() => navigate('/promotions')}
                            >
                                {t('PromotionIcon2')}
                            </span>{' '}
                            {t('PromotionIcon3')}
                        </span>
                    );
                }}
            />
        </>
    );
}
