import React, { memo } from 'react'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import {
    Promotion,
    PromotionWallet
} from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { useSelectedPromotionContext } from 'State/SelectPromotion'
import { getPropByKeyAndLanguage } from 'Containers/Promotions/Promotion.helper'
import { CreateEvent } from '@daml/ledger'
import { useTranslation } from 'react-i18next'
import {
    haveIUsedPromo,
    isActivePromotion,
    calculateNumberSinceSignup
} from './promotion.helper'
import { TooltipPromoDropdown } from './TooltipPromoDropdown'
import useCleanUpPromotionStateOnClose from 'Components/PromotionsDropdown/useCleanUpPromotionStateOnClose'
import useActivePromotions from 'Hooks/useActivePromotions'

const PromotionDropdown = memo(function PromotionsDropdown({
    promoFilter = 'Deposit',
    styleClass = '',
    defaultSelected,
    showInfoIcon = false,
    onValueChange,
    firstTime = false,
    serviceContracts,
    serviceLoading,
    promotionWalletContracts,
    isLoadingPromoWallet
}: {
    promoFilter?: 'Deposit' | 'Withdrawal' | 'Bet'
    styleClass?: string
    defaultSelected?: CreateEvent<Promotion, Promotion.Key, string>
    showInfoIcon?: Boolean
    onValueChange?: (value: any) => void
    firstTime?: boolean
    serviceContracts: readonly CreateEvent<Service, Service.Key, string>[]
    serviceLoading: boolean
    promotionWalletContracts: readonly CreateEvent<
        PromotionWallet,
        PromotionWallet.Key,
        string
    >[]
    isLoadingPromoWallet: boolean
}) {
    const [selectedValue, setSelectedValue] = React.useState('none')
    const [, setSelectedPromotion] = useSelectedPromotionContext()

    const { promotionsContracts, promotionsLoader } = useActivePromotions()
    const { t } = useTranslation()

    const today = new Date()
    const todayTime = today.getTime()
    const daysSinceSignup = calculateNumberSinceSignup(
        serviceContracts[0]?.payload.createdAt
    )

    const promotionFilteredData = promotionsContracts.filter(promotion => {
        const { promoType } = promotion.payload.config
        const isFirstTimePromotion = promoType.tag === 'FirstTime'

        if (firstTime) {
            if (
                !serviceLoading &&
                serviceContracts.length > 0 &&
                isFirstTimePromotion
            ) {
                const promoValue = Number(promoType.value)
                return (
                    isActivePromotion(promotion, today, todayTime) &&
                    (!promoType.value || promoValue >= daysSinceSignup)
                )
            }
            return false
        } else {
            return (
                !isFirstTimePromotion &&
                isActivePromotion(promotion, today, todayTime)
            )
        }
    })

    useCleanUpPromotionStateOnClose()

    React.useEffect(() => {
        if (defaultSelected && promotionFilteredData.length) {
            // Find the first matching promotion only once
            const matchingPromotion = promotionFilteredData.find(
                (promotion: CreateEvent<Promotion, Promotion.Key, string>) =>
                    promotion.contractId === defaultSelected.contractId &&
                    promotion.payload.config.action.tag === promoFilter
            )
            if (matchingPromotion) {
                setSelectedValue(matchingPromotion?.contractId)
                if (onValueChange) {
                    onValueChange(matchingPromotion)
                }
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedValue, promotionFilteredData])

    const handleChange = (event: React.FormEvent<HTMLSelectElement>) => {
        const selectedContractId = event.currentTarget.value
        setSelectedValue(selectedContractId)
        // Find the selected promotion only once
        const selectedPromotion = promotionFilteredData.find(
            (promotion: CreateEvent<Promotion, Promotion.Key, string>) =>
                promotion.contractId === selectedContractId
        )
        setSelectedPromotion(selectedPromotion)
        if (onValueChange) {
            onValueChange(selectedPromotion)
        }
    }

    const removedUsedPromotions = promotionFilteredData.filter(
        promo => !haveIUsedPromo(promotionWalletContracts[0], promo)
    )

    const shouldRender =
        !promotionsLoader &&
        !isLoadingPromoWallet &&
        removedUsedPromotions.some(
            promotion => promotion.payload.config.action.tag === promoFilter
        ) &&
        serviceContracts.length > 0 &&
        !serviceLoading

    return shouldRender ? (
        <div className={styleClass}>
            <label htmlFor="promotion">
                {t('PromotionDropdown')}{' '}
                {showInfoIcon ? <TooltipPromoDropdown /> : null}
            </label>
            <select
                value={selectedValue}
                onChange={event => handleChange(event)}
            >
                <option value={'none'}>
                    {t('PromotionDropdownPlaceHolder')}
                </option>
                {removedUsedPromotions.map(promotion =>
                    promotion.payload.config.action.tag === promoFilter &&
                    promotion.payload.status === 'Active' ? (
                        <option
                            key={promotion.contractId}
                            value={promotion.contractId}
                        >
                            {getPropByKeyAndLanguage(
                                promotion,
                                'en-US',
                                'title'
                            )}
                        </option>
                    ) : null
                )}
            </select>
        </div>
    ) : null
})

export default PromotionDropdown
