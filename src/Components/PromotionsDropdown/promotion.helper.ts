import { CreateEvent } from '@daml/ledger'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { PromotionWallet } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'

export const calculateNumberSinceSignup = (signupDate: string) => {
    let today = new Date()
    let daySignup = new Date(signupDate)
    let differenceInTime = today.getTime() - daySignup.getTime()
    return Math.ceil(differenceInTime / (1000 * 3600 * 24))
}

export const isActivePromotion = (
    promotion: CreateEvent<Promotion, Promotion.Key, string>,
    today: Date,
    todayTime: number
) => {
    const startDate = new Date(promotion.payload.startDate)
    const endDate = promotion.payload.config.endDate
        ? new Date(promotion.payload.config.endDate).getTime()
        : Infinity

    return startDate <= today && endDate >= todayTime
}

export const haveIUsedPromo = (
    promotionWalletContracts: CreateEvent<
        PromotionWallet,
        PromotionWallet.Key,
        string
    >,
    selectedPromotion: CreateEvent<Promotion, Promotion.Key, string>
) => {
    if (!selectedPromotion || !promotionWalletContracts) {
        return false
    }
    if (selectedPromotion?.payload?.config.promoType.tag === 'FirstTime') {
        return (
            promotionWalletContracts?.payload.promotionMap.entriesArray()
                .length > 0
        )
    }
    return (
        promotionWalletContracts?.payload?.promotionMap
            .entriesArray()
            .filter(
                element =>
                    element[0]._5 === selectedPromotion?.payload?.promotionId &&
                    element[1].usageCounter ===
                        selectedPromotion?.payload?.config?.limitedPromotion
            )?.length > 0
    )
}
