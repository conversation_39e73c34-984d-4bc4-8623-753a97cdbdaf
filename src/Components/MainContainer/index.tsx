import React, { useContext } from 'react'
import { AppContext } from 'State/AppProvider'
import useStatusBanner from 'Hooks/useStatusBanner'
import ActionFailureAlert from 'Components/ActionFailure'
import useIsAuthenticated from 'Hooks/useAuth'

export default function MainContainer({
    children
}: {
    children: React.ReactNode
}) {
    const { showSideMenu } = useContext(AppContext)
    useStatusBanner()
    const isAuthenticated = useIsAuthenticated()
    return (
        <main className={'main__container'}>
            <>
                {isAuthenticated ? <ActionFailureAlert /> : null}
                {showSideMenu && <div className="overlay" />}
                {children}
            </>
        </main>
    )
}
