@import '../../Styles/colors';

.contactus {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGRegister.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    color: $white;

    h1 {
        font-family: 'Montserrat', sans-serif;
        font-size: 2.5rem;
        font-style: normal;
        font-weight: bold;
        line-height: 3.125rem;
        text-align: center;
    }

    &__header {
        max-width: 1383px;
        padding: 30px 0;

        h3 {
            font-family: 'Montserrat', sans-serif;
            max-width: 1328px;
            text-align: center;
            font-size: 1.2rem;
        }
    }

    &__cards {
        margin-top: 50px;
        display: grid;
        grid-gap: 40px;
        grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
        align-items: center;
        color: $darkGrey;
    }

    &__card {
        background-color: $white;
        align-items: center;
        border-radius: 6px;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 200px;
        padding: 20px 30px;
        border-top: 5px solid transparent;
        transition: transform ease 600ms;

        &:hover {
            transform: translate(0, -10px);
            border-top: 5px solid $purple;
        }

        h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.6rem;
            font-style: normal;
            line-height: 2.5rem;
            text-align: center;
        }

        p {
            font-family: 'Open Sans', sans-serif;
            font-size: 0.75rem;
            line-height: 1.2rem;
            letter-spacing: 0.7px;
            padding-top: 25px;
            text-align: center;
            text-transform: uppercase;
        }

        ul {
            display: flex;
            list-style: none;
            justify-content: center;
            align-items: center;
            gap: 30px;
            padding-top: 25px;
            color: $purple;

            li {
                cursor: pointer;
            }
        }

        span {
            background-color: $purple;
            color: $white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: filter 0.2s;
            margin-top: 20px;

            &:hover {
                filter: brightness(115%);
            }
        }
    }
}

.messageus {
    margin: 60px auto;

    &__form {
        align-items: center;
        display: flex;
        justify-content: center;
    }

    &__container {
        border-radius: 6px;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
        padding: 25px 20px;
        max-width: 650px;

        p {
            font-size: 0.75rem;
            padding-top: 20px;
            text-align: center;
            max-width: 400px;
        }
    }
}

@media (max-width: 1014px) {
    .contactus {
        padding: 50px 27px 15px 27px;
    }
}

@media (max-width: 600px) {
    .contactus {
        padding: 15px 27px 15px 27px;
    }
}
