import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
    faFacebookSquare,
    faTwitterSquare,
    faInstagram
} from '@fortawesome/free-brands-svg-icons'

import useScrollToTop from 'Hooks/useScrollToTop'
import { useTranslation } from 'react-i18next'
import { useI18LanguageContext } from 'State/LanguageState'
import { helpLinks } from 'Components/Footer/links.utils'

import './style.scss'

export default function ContactUs() {
    useScrollToTop()

    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()

    const handleHelp = ({
        US,
        BR,
        MX
    }: {
        US: string
        BR: string
        MX: string
    }) => {
        const link = lang === 'MX' ? MX : lang === 'BR' ? BR : US
        window.open(link)
    }

    return (
        <div className="content__container contactus pagePadding">
            <h1>{t('ContactTitle')}</h1>
            <section className="contactus__header">
                <h3>{t('ContactMain')}</h3>
            </section>
            <section className="contactus__cards">
                <div className="contactus__card">
                    <h3>{t('ContactMessageUs')}</h3>
                    <p>{t('ContatctCard1')}</p>
                    <span onClick={() => handleHelp(helpLinks)}>
                        {t('ContactButton')}
                    </span>
                </div>
                <div className="contactus__card">
                    <h3> {t('ContactCard2')}</h3>
                    <ul>
                        <li>
                            <FontAwesomeIcon
                                size="2x"
                                icon={faFacebookSquare}
                                onClick={() =>
                                    window.open(
                                        'https://www.facebook.com/realgambyl/',
                                        '_blank'
                                    )
                                }
                            />
                        </li>
                        <li>
                            <FontAwesomeIcon
                                size="2x"
                                icon={faTwitterSquare}
                                onClick={() =>
                                    window.open(
                                        'https://twitter.com/realgambyl',
                                        '_blank'
                                    )
                                }
                            />
                        </li>
                        <li>
                            <FontAwesomeIcon
                                size="2x"
                                icon={faInstagram}
                                onClick={() =>
                                    window.open(
                                        'https://www.instagram.com/realgambyl_es/',
                                        '_blank'
                                    )
                                }
                            />
                        </li>
                    </ul>
                </div>
            </section>
        </div>
    )
}
