import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faCaretUp,
  faCaretDown,
} from '@fortawesome/free-solid-svg-icons'

export default function ItemWithCaret(
  {
    path,
    handleClick,
    isOpen,
    handleIsOpen,
    content,
    styleClass
  }: {
    path: string,
    handleClick: () => void,
    isOpen: boolean,
    handleIsOpen: () => void,
    content: React.ReactNode,
    styleClass?: string
  }) {
  return (
    <li className={styleClass}>
      <Link
        to={path}
        onClick={() => handleClick()}
      >
        {content}
      </Link>
      <button
        type="button"
        onClick={() => handleIsOpen()}
      >
        {isOpen ? (
          <FontAwesomeIcon icon={faCaretUp} />
        ) : (
          <FontAwesomeIcon icon={faCaretDown} />
        )}
      </button>
    </li>
  )
}
