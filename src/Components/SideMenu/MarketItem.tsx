import React, { useState } from 'react'
import { Market, Submarket } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { Map } from "@daml/types"
import { useTranslation } from 'react-i18next'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTrophy } from '@fortawesome/free-solid-svg-icons'

import { getGeoPath, getSubmarketPath } from 'Utils/getPathFromSubmarket'
import { checkIfNonSportEvent } from 'Utils/checkIfNonSportEvent'

import { sports } from 'Constants/sports'

import ItemWithCaret from './ItemWithCaret'
import SimpleItem from './SimpleItem'

type setValue = { marketId: string, geoValue: string }

export default function MarketItem(
    { market, submarkets, handleLinkClick, language, baseLang }: {
        market: [
            Market, Map<{
                tag: "Geography";
                value: string;
            }, Submarket[]>]
        submarkets: Map<{
            tag: "Geography";
            value: string;
        }, Submarket[]>
        handleLinkClick: () => void
        language: any
        baseLang: any
    }) {
    const [showGeography, setShowGeography] = useState(false)
    const [showLeaguesSet, setShowLeaguesSet] = useState(new Set())
    const hasLeagueOpen = (val: setValue) => {
        let stringifiedValue = JSON.stringify(val)
        return showLeaguesSet.has(stringifiedValue)
    }


    const handleOpenLeage = (val: setValue) => {
        let stringifiedValue = JSON.stringify(val)
        const updatedShowLeaguesSet = new Set(showLeaguesSet);
        if (updatedShowLeaguesSet.has(stringifiedValue)) {
            updatedShowLeaguesSet.delete(stringifiedValue)
        } else {
            updatedShowLeaguesSet.add(stringifiedValue)
        }
        setShowLeaguesSet(updatedShowLeaguesSet)
    }

    const name =
        language && language?.filter((a: any) => {
            if (typeof market[0].value === "string") {
                return a[market[0].value]
            }
            return undefined
        }
        )[0]

    const baseName =
        baseLang && baseLang?.filter((a: any) => {
            if (typeof market[0].value === "string") {
                return a[market[0].value]
            }
            return undefined
        })[0]

    const { t } = useTranslation()

    const checkIfIsCustom = isNaN(Number(checkIfNonSportEvent(market[0])))
        ? checkIfNonSportEvent(market[0])
        : name && Object.values(name)

    return (
        <>
            {/* Item for Market */}
            <ItemWithCaret
                styleClass={"sideMenu__item sideMenu__item--bolder"}
                path={`/events/${checkIfNonSportEvent(market[0])}`}
                handleClick={handleLinkClick}
                isOpen={showGeography}
                handleIsOpen={() => setShowGeography(!showGeography)}
                content={
                    <>
                        {(baseName &&
                            sports[baseName && Object.values(baseName)[0]]
                                ?.icon) || <FontAwesomeIcon icon={faTrophy} />}
                        <span>
                            {checkIfIsCustom === 'Entertainment' ||
                                checkIfIsCustom === 'Politics'
                                ? t(checkIfIsCustom)
                                : checkIfIsCustom}
                        </span>
                    </>
                }
            />
            {showGeography ?
                React
                    .Children
                    .toArray(submarkets.entriesArray().map(([geography, leagues]) => (
                        <>
                            {/* Item for Geography */}
                            <ItemWithCaret
                                styleClass='sideMenu__geography'
                                path={getGeoPath(geography.value, checkIfNonSportEvent(market[0]))}
                                handleClick={handleLinkClick}
                                isOpen={hasLeagueOpen({ marketId: checkIfNonSportEvent(market[0]), geoValue: geography.value })}
                                handleIsOpen={() => handleOpenLeage({ marketId: checkIfNonSportEvent(market[0]), geoValue: geography.value })}
                                content={
                                    <>
                                        {geography.value}
                                    </>
                                }
                            />
                            {hasLeagueOpen({ marketId: checkIfNonSportEvent(market[0]), geoValue: geography.value }) ?
                                <ul className="sideMenu__league">
                                    {React.Children.toArray(leagues.map(league => {
                                        if (league.tag !== "Tournament") return null
                                        return <SimpleItem
                                            path={getSubmarketPath(league.value, checkIfNonSportEvent(market[0]), geography.value.toLowerCase())}
                                            handleClick={handleLinkClick}
                                            content={league.value} />
                                    }))}
                                </ul>
                                : null
                            }
                        </>
                    )))
                : null}
        </>
    )
}
