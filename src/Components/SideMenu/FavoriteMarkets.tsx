import React from 'react'
import { sports } from 'Constants/sports'
import { Link } from 'react-router-dom'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTrophy } from '@fortawesome/free-solid-svg-icons'
import { useAccountContex } from 'State/AccountContext'
import { useTranslation } from 'react-i18next'
import { checkIfNonSportEvent } from 'Utils/checkIfNonSportEvent'
import { flatten } from 'Utils/flatten'

import './style.scss'

export default function FavoriteMarkets({
    handleLinkClick,
    language,
    baseLanguage
}: {
    handleLinkClick: () => void
    language: any
    baseLanguage: any
}) {
    const { accountContracts } = useAccountContex()
    const marketQueries = React.useMemo(() => {
        return accountContracts.length > 0
            ? flatten(
                  accountContracts[0]?.payload?.favouriteMarkets?.map?.entriesArray()
              ).filter((element: any) => {
                  return Object.keys(element).length !== 0
              })
            : []
    }, [accountContracts])

    const name = (b: any) => language && language?.filter((a: any) => a[b])[0]
    const englishName = (b: any) =>
        baseLanguage && baseLanguage?.filter((a: any) => a[b])[0]

    const sportIcon = (value: any) =>
        sports[
            englishName(checkIfNonSportEvent(value)) &&
                Object.values(englishName(checkIfNonSportEvent(value)))[0]
        ]?.icon

    const checkIfIsCustom = (val: string | number) =>
        isNaN(Number(val)) ? val : name(val) && Object.values(name(val))[0]

    const { t } = useTranslation()

    return marketQueries?.length ? (
        <>
            <h3>FAVORITES</h3>
            {marketQueries.map((favorite: any) => (
                <li
                    className="sideMenu__item"
                    key={`sideMenu__item-${checkIfNonSportEvent(favorite)}`}
                >
                    <Link
                        to={`/events/${checkIfNonSportEvent(favorite)}`}
                        onClick={() => handleLinkClick()}
                    >
                        {sportIcon(favorite) || (
                            <FontAwesomeIcon icon={faTrophy} />
                        )}
                        {checkIfIsCustom(checkIfNonSportEvent(favorite)) ===
                            'Politics' ||
                        checkIfIsCustom(checkIfNonSportEvent(favorite)) ===
                            'Entertainment'
                            ? t(checkIfIsCustom(checkIfNonSportEvent(favorite)))
                            : checkIfIsCustom(checkIfNonSportEvent(favorite))}
                    </Link>
                </li>
            ))}
            <hr />
        </>
    ) : null
}
