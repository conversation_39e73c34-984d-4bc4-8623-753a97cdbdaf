import React from 'react'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
    faInfoCircle,
    faTachometerAlt,
    faUsers,
    faDice,
    faNewspaper,
    faUser
} from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

import { useI18LanguageContext } from 'State/LanguageState'

import { helpLinks } from 'Components/Footer/links.utils'

const nationLinks = {
    US: 'https://gambyl.com/en/',
    BR: 'https://gambyl.com/pt/',
    MX: 'https://gambyl.com/es/'
}

export default function BusinessItems({
    handleClick
}: {
    handleClick: () => void
}) {
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()

    const helpLink = ({ US, BR, MX }: { US: string; BR: string; MX: string }) =>
        lang === 'MX' ? MX : lang === 'BR' ? BR : US

    const nationLink = ({
        US,
        BR,
        MX
    }: {
        US: string
        BR: string
        MX: string
    }) => (lang === 'MX' ? MX : lang === 'BR' ? BR : US)

    return (
        <>
            <li className="sideMenu__item">
                <Link to="/" onClick={() => handleClick()}>
                    <FontAwesomeIcon icon={faTachometerAlt} />
                    <span>{t('HSports')}</span>
                </Link>
            </li>
            <li className="sideMenu__item">
                <Link
                    to="/"
                    onClick={() => window.open('https://casino.gambyl.com/')}
                >
                    <FontAwesomeIcon icon={faDice} />
                    <span> {t('HCasino')}</span>
                </Link>
            </li>
            <li className="sideMenu__item">
                <Link
                    to="/"
                    onClick={() =>
                        window.open(
                            'https://casino.gambyl.com/casino/categories/136'
                        )
                    }
                >
                    <FontAwesomeIcon icon={faUser} />
                    <span>{t('HLiveCasino')}</span>
                </Link>
            </li>
            <li className="sideMenu__item">
                <a
                    href={helpLink(helpLinks)}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <FontAwesomeIcon icon={faInfoCircle} />
                    <span>{t('HHelp')}</span>
                </a>
            </li>
            <li className="sideMenu__item">
                <Link to="/promotions" onClick={() => handleClick()}>
                    <FontAwesomeIcon icon={faUsers} />
                    <span>{t('HPromotions')}</span>
                </Link>
            </li>
            <li className="sideMenu__item">
                <a
                    href={nationLink(nationLinks)}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <FontAwesomeIcon icon={faNewspaper} />
                    <span>{t('HNews')}</span>
                </a>
            </li>
            <hr />
        </>
    )
}
