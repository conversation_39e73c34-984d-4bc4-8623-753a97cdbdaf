import React, { useContext, useEffect } from 'react'

import useMediaQuery from 'Hooks/useMediaQuery'
import { AppContext } from 'State/AppProvider'
import { useI18LanguageContext } from 'State/LanguageState'
import { useMarketMapContext } from 'State/MarketMapContext'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import useIsAuthenticated from 'Hooks/useAuth'

import FavoriteMarkets from './FavoriteMarkets'
import FeaturedMarkets from './FeaturedMarkets'
import BusinessItems from './BusinessItems'
import MarketItem from './MarketItem'

import './style.scss'

export default function SideMenu() {
    const { showSideMenu, setShowSideMenu } = useContext(AppContext)
    const { BR, MX, US } = useSportTranslationsContext()
    const { lang } = useI18LanguageContext()
    const isAuthenticated = useIsAuthenticated()
    const { marketContracts, loadingMarkets } = useMarketMapContext()
    let marketsQuery = marketContracts[0]?.payload?.map?.entriesArray()

    //CHANGE HERE
    const isSideMenuVisibleByDefault = useMediaQuery('(max-width: 1210px)')

    const setLanguage = () =>
        lang === 'US' ? US : lang === 'BR' ? BR : lang === 'MX' ? MX : US

    useEffect(() => {
        if (isSideMenuVisibleByDefault) {
            return setShowSideMenu && setShowSideMenu(false)
        }
        return setShowSideMenu && setShowSideMenu(true)
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isSideMenuVisibleByDefault])

    const closeSideMenu = () => setShowSideMenu && setShowSideMenu(false)

    const handleLinkClick = () =>
        isSideMenuVisibleByDefault ? closeSideMenu() : () => {}

    return (
        <div className={showSideMenu ? 'sideMenu open' : 'sideMenu'}>
            <ul>
                {isSideMenuVisibleByDefault ? (
                    <BusinessItems handleClick={handleLinkClick} />
                ) : null}
                <FeaturedMarkets
                    handleLinkClick={handleLinkClick}
                    baseLanguage={US}
                />
                {isAuthenticated ? (
                    <FavoriteMarkets
                        language={setLanguage()}
                        handleLinkClick={handleLinkClick}
                        baseLanguage={US}
                    />
                ) : null}
                {!loadingMarkets && marketsQuery?.length
                    ? React.Children.toArray(
                          marketsQuery.map(market => (
                              <MarketItem
                                  market={market}
                                  submarkets={market[1]}
                                  handleLinkClick={handleLinkClick}
                                  language={setLanguage()}
                                  baseLang={US}
                              />
                          ))
                      )
                    : null}
            </ul>
        </div>
    )
}
