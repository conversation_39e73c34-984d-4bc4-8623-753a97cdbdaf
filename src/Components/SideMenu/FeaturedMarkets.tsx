import React from 'react'
import { sports } from 'Constants/sports'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTrophy } from '@fortawesome/free-solid-svg-icons'
import { Link } from 'react-router-dom'
import { checkIfNonSportEvent } from 'Utils/checkIfNonSportEvent'
import useFeaturedEvents from 'Hooks/useFeaturedEvents'
import { langArray } from 'State/SportTranslationsContext'

export default function FeaturedMarkets({
    handleLinkClick,
    baseLanguage
}: {
    handleLinkClick: () => void
    baseLanguage: langArray[]
}) {
    const { routes, isLoading } = useFeaturedEvents()

    const englishName = (b: string) => baseLanguage?.filter((a: langArray) => a[b])[0]

    const sportIcon = (value: string) =>
        sports[englishName(value) && Object.values(englishName(value))[0]]?.icon

    return !isLoading && routes?.length > 0 ? (
        <>
            <h3>Popular</h3>
            {React.Children.toArray(routes.map((favorite) => (
                <li className="sideMenu__item">
                    <Link
                        to={favorite.route}
                        onClick={() => handleLinkClick()}
                    >
                        <div className='sideMenu__featured--sideLeft'>
                            {sportIcon(
                                checkIfNonSportEvent(
                                    favorite?.market
                                )
                            ) || <FontAwesomeIcon icon={faTrophy} />}
                        </div>
                        <div className='sideMenu__featured--sideRight'>{favorite.tournament}</div>
                    </Link>
                </li>
            )))}
            <hr />
        </>
    ) : null
}
