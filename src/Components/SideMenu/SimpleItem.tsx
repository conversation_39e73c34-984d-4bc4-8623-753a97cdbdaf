import React from 'react'
import { <PERSON> } from 'react-router-dom'

export default function SimpleItem(
    {
        path,
        handleClick,
        content
    }: {
        path: string,
        handleClick: () => void,
        content: React.ReactNode
    }) {
    return (
        <li>
            <Link
                to={path}
                onClick={() => handleClick()}
            >
                {content}
            </Link>
        </li>

    )
}
