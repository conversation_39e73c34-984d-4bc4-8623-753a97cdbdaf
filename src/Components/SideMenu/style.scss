@import '../../Styles/colors';

.sideMenu {
    background: $black;
    width: 0;
    max-height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;

    svg {
        max-width: 16px !important;
        max-height: 16px !important;

        @media (max-width: 450px) {
            max-width: 12px !important;
            max-height: 12px !important;
        }
    }

    ul {
        list-style-type: none;
        margin: 0;
        padding: 0 0 0 20px;
        
        li {
            cursor: pointer;
            font-family: 'Open Sans', sans-serif;
            font-size: 1rem;
            line-height: 1.5rem;
            margin-bottom: 8px;
            align-items: center;
            a {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                flex-wrap: wrap;
                gap: 0 10px;
                color: $white;
                text-decoration: none;
                text-transform: capitalize;
                &:hover {
                    text-decoration: underline;
                }
            }
            svg {
                fill: $white;
                width: 16px !important;
                height: 16px !important;
                max-width: 16px !important;
                max-height: 16px !important;

                @media (max-width: 450px) {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }
            }
        }
        // ul {
        //     padding-left: 35px;
        //     text-transform: capitalize;
        //     li {
        //         width: 100%;
        //         font-size: 0.86rem;
        //         display: inline-block;
        //         word-break: break-word;
        //     }
        // }
    }

    &__item {
        display: flex;
        gap: 0;
        color: $white;

        button {
            flex: 1 1 auto;
            background: none;
            border: none;
            color: $white;
            margin-left: auto;
            outline: none;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 5px 0 0;
            cursor: pointer;
        }

        &--bolder {
            font-weight: bold;
        }
    }

    &__geography {
        display: flex;
        gap: 0;
        color: $white;
        margin-left: 26px !important;
        @media (max-width: 450px) {
            margin-left: 26px !important;
        }

        button {
            flex: 1 1 auto;
            background: none;
            border: none;
            color: $white;
            margin-left: auto;
            outline: none;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 5px 0 0;
            cursor: pointer;
        }
    }

    &__league {
        padding-left: 35px !important;
        text-transform: capitalize !important;
            li {
                width: 90% !important;
                font-size: 0.87rem  !important;
                display: inline-block !important;
                word-break: break-word !important;
            }
    }

    &__featured {
     &--sideLeft {
        flex: 0;
     }
     &--sideRight {
        flex: 1;
     }
    }

    h3 {
        color: $purple;
        font-size: 0.8rem;
        font-family: 'Montserrat-Bold', sans-serif;
        margin-bottom: 15px;
        padding-top: 15px;
        text-transform: uppercase;
    }

    &__shadow {
        width: 0;
        height: 100%;
        background: $black;
        opacity: 0.4;
        display: inline-block;
        position: absolute;
        top: 0;
        &.open {
            width: 100vw;
        }
    }

    &.open {
        width: 245px;

        hr {
            margin: 20px 0;
            width: 90%;
        }

        @media (max-width: 930px) {
            position: absolute;
            z-index: 20;
            left: 0;
            max-height: calc(100vh - 70px);
            height: 100%;
        }

        @media (max-width: 426px) {
            width: 100%;
        }
    }
}
