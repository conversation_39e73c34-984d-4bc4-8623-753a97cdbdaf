import React from 'react'
import { Table } from '@tanstack/react-table'
import '../style.scss'

const RECORDS_PER_PAGE = [10, 20, 50]

interface IPagination<T> {
    table: Table<T>
}

interface IPaginationBtn {
    isDisabled: boolean
    handleClick: () => void
    children: React.ReactNode
}

interface IPaginationSelect {
    pageSize: number
    handlePageSize: (val: string) => void
}

const PaginationBtn = ({
    isDisabled,
    handleClick,
    children
}: IPaginationBtn) => {
    return (
        <button
            className={`table__paginationBtn ${
                isDisabled
                    ? 'table__paginationBtn--disabled'
                    : 'table__paginationBtn--available'
            }`}
            onClick={handleClick}
            disabled={isDisabled}
        >
            {children}
        </button>
    )
}

const PaginationSelect = ({ pageSize, handlePageSize }: IPaginationSelect) => {
    return (
        <div className="table__paginationSelect">
            <select
                value={pageSize}
                onChange={e => {
                    handlePageSize(e.target.value)
                }}
                className="register__input marginTop0"
            >
                {RECORDS_PER_PAGE.map(pageSize => (
                    <option key={pageSize} value={pageSize}>
                        {pageSize} records
                    </option>
                ))}
            </select>
        </div>
    )
}

const Pagination = <T,>({ table }: IPagination<T>) => {
    const canGetPreviousPage = table.getCanPreviousPage()
    const canGetNextPage = table.getCanNextPage()
    const currentPage = table.getState().pagination.pageIndex + 1
    const totalOfPages = table.getPageCount()
    const pageSize = table.getState().pagination.pageSize

    const goToFirstPage = () => {
        table.setPageIndex(0)
    }
    const goToPreviousPage = () => {
        table.previousPage()
    }
    const goToNextPage = () => {
        table.nextPage()
    }
    const goToLastPage = () => {
        table.setPageIndex(totalOfPages - 1)
    }

    const handlePageSize = (value: string) => {
        table.setPageSize(Number(value))
    }

    return (
        <div className="table__paginationContainer">
            <div className="table__paginationActions">
                <PaginationBtn
                    isDisabled={!canGetPreviousPage}
                    handleClick={goToFirstPage}
                >
                    {'<<'}
                </PaginationBtn>
                <PaginationBtn
                    isDisabled={!canGetPreviousPage}
                    handleClick={goToPreviousPage}
                >
                    {'<'}
                </PaginationBtn>
                <strong>
                    {currentPage} of {totalOfPages}
                </strong>
                <PaginationBtn
                    isDisabled={!canGetNextPage}
                    handleClick={goToNextPage}
                >
                    {'>'}
                </PaginationBtn>
                <PaginationBtn
                    isDisabled={!canGetNextPage}
                    handleClick={goToLastPage}
                >
                    {'>>'}
                </PaginationBtn>
            </div>
            <PaginationSelect
                pageSize={pageSize}
                handlePageSize={handlePageSize}
            />
        </div>
    )
}

export default Pagination
