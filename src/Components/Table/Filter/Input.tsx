import React from 'react'

import '../style.scss'

interface InputFilterProps extends React.InputHTMLAttributes<HTMLInputElement> {
    label?: string
}

const FilterInput = ({ label, ...props }: InputFilterProps) => {
    return (
        <div className="table__filterInputContainer">
            {label ? <label htmlFor={label}>{label}</label> : null}
            <input className="register__input" {...props} />
        </div>
    )
}

export default FilterInput
