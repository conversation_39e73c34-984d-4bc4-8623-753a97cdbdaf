import { Table } from '@tanstack/react-table'

export const handleSearch = <T>(table: Table<T>, value: string, id: string) => {
    table.setColumnFilters([
        // Remove existing filter for the column
        ...table.getState().columnFilters.filter(filter => filter.id !== id),
        // Add new filter if value is not empty
        ...(value ? [{ id, value }] : [{ id, value: '' }])
    ])
    table.setPageIndex(0)
}

export const getSearchState = <T>(table: Table<T>, id: string) => {
    return table.getState().columnFilters.find(col => col.id === id)?.value
}
