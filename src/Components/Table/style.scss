@import '../../Styles/colors';

.table__wrapper {
    display: inline-block;
    overflow-x: auto;
    width: 100%;

    table {
        display: table;
        width: 100%;
        td,
        th {
            padding: 15px;
        }

        th {
            text-align: left;
            border-bottom: 1px solid rgba(130, 152, 171, 0.2);
            border-top: 1px solid rgba(130, 152, 171, 0.2);
            text-transform: uppercase;
            padding-top: 1rem;
            padding-bottom: 1rem;
            font-family: 'OpenSans', sans-serif;
            font-size: 1.063rem;
            line-height: 1.563rem;
        }

        td {
            white-space: nowrap;
            text-align: left;
            font-family: 'OpenSans', sans-serif;
            font-size: 0.938rem;
            line-height: 1.25rem;
            border-bottom: none;
            color: rgb(20, 20, 20);
        }

        .success {
            color: $tableSuccessGreen;
        }

        .fail {
            color: $tableFailRed;
        }

        .smaller-font {
            font-size: 0.7rem;
        }
    }
}

.table {
    &__paginationContainer {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    &__paginationActions {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 10px;
    }

    &__paginationBtn {
        font-weight: bold;
        font-size: 1.5rem;
        border: none;
        outline: none;
        background-color: transparent;

        &--available {
            color: $purple;
            cursor: pointer;
        }

        &--disabled {
            color: #8298ab;
            cursor: not-allowed;
        }
    }

    &__paginationSelect {
        width: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &__filterContainer {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px 0;
    }

    &__filterInputContainer {
        flex: 0 1 250px;

        label {
            font-weight: bold;
        }
    }
}
