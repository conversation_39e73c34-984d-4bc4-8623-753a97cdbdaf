import React from 'react'

import '../style.scss'

type TypeTRElement =
    React.ReactElement<HTMLTableRowElement> |
    React.ReactElement<HTMLTableRowElement>[] |
    React.ReactChild |
    React.ReactFragment |
    React.ReactPortal

interface IGenericTableData {
    tableBodyRow: TypeTRElement
    tableHeader: TypeTRElement
}

export default function GenericTable({
    tableBodyRow,
    tableHeader
}: IGenericTableData) {

    return (
        <div className="table__wrapper">
            <table>
                <thead>
                    {tableHeader}
                </thead>
                <tbody>
                    {tableBodyRow}
                </tbody>
            </table>
        </div>
    )
}
