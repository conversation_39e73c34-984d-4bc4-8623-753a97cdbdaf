import React from 'react'

import './style.scss'
import { useTranslation } from 'react-i18next'

export const EventFilterTitleInput = ({
    search,
    searchCallback
}: {
    search: string
    searchCallback: (val: string) => void
}) => {
    const { t } = useTranslation()

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        searchCallback(e.target.value)
    }

    const placeholderText = t('inputFilterEventTiltePlaceholder')

    return (
        <input
            className="eventFilterInputTitle"
            value={search}
            onChange={handleChange}
            placeholder={placeholderText}
        />
    )
}
