@import '../../Styles/colors';

.eventFilterTabsLayout {
    display: grid;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
    // Default: Tablet/Desktop Layout
    grid-template-columns: auto 1fr;
    grid-template-areas:
        'filters search'
        'children children';

    @media (min-width: 768px) {
        // Tablet & Up
        &.has-children {
            grid-template-columns: auto 1fr auto; // Three columns
            grid-template-areas: 'filters search children';
        }

        &.no-children {
            grid-template-columns: auto 1fr; // Two columns
            grid-template-areas: 'filters search';
        }
    }

    @media (max-width: 767px) {
        // Mobile Layout
        &.has-children {
            grid-template-columns: 1fr 50px; // Two columns
            grid-template-areas:
                'filters children'
                'search search';
        }

        &.no-children {
            grid-template-columns: 1fr; // One column
            grid-template-areas:
                'filters'
                'search';
        }
    }
}

.filters {
    grid-area: filters;
}
.search {
    grid-area: search;
    width: 100%;
}

.children {
    grid-area: children;
}

.margin0 {
    margin: 0 !important;
    padding: 0 !important;
}

.eventFilterInputTitle {
    border: 1px solid $grayInpuBorder;
    border-radius: 6px;
    color: $black;
    font-family: 'Open Sans', sans-serif;
    font-size: 0.813rem;
    font-style: normal;
    font-weight: normal;
    height: 40px;
    line-height: 1.25rem;
    padding: 0 20px;
    flex: 1 1 200px;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    width: 100%;

    &:focus {
        border-color: $blueShaddow;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
    }

    &::placeholder {
        color: $black;
        opacity: 1;
    }
}
