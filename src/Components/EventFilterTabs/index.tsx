import React from 'react'
import TabsWithoutLink from 'Components/TabsWithoutLink'
import { useTranslation } from 'react-i18next'
import { EventFilterTitleInput } from './EventFilterTitleInput'

import './style.scss'

const Layout = ({
    children,
    ...props
}: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => {
    return <div {...props}>{children}</div>
}

const EventFilterTabs = ({
    isFeatured,
    handleFeatured,
    children,
    search,
    searchCallback
}: {
    isFeatured: boolean
    handleFeatured: (val: boolean) => void
    search: string
    searchCallback: (val: string) => void
    children?: React.ReactNode
}) => {
    const { t } = useTranslation()
    const tabs = [
        {
            label: t('allEvents'),
            value: 'all',
            onclick: () => {
                handleFeatured(false)
            }
        },
        {
            label: t('popularEvents'),
            value: 'featured',
            onclick: () => {
                handleFeatured(true)
            }
        }
    ]

    return (
        <>
            <Layout
                className={`eventFilterTabsLayout ${
                    children ? 'has-children' : 'no-children'
                }`}
            >
                {/* tabs section */}
                <div className="filters">
                    <TabsWithoutLink
                        classStyle="margin0"
                        tabs={tabs}
                        selectedTab={isFeatured ? 'featured' : 'all'}
                    />
                </div>
                {/* filter input section  */}
                {search !== undefined && searchCallback !== undefined ? (
                    <div className="search">
                        <EventFilterTitleInput
                            search={search}
                            searchCallback={searchCallback}
                        />
                    </div>
                ) : null}
                {/* children section */}
                {children ? <div className="children">{children}</div> : null}
            </Layout>
        </>
    )
}

export default EventFilterTabs
