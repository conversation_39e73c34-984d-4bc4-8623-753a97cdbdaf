import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'

const GoBackNavigation = ({ to, children }: { to: string, children: React.ReactNode }) => {
    return (
        <Link to={to}>
            <FontAwesomeIcon
                icon={faArrowLeft}
                style={{ fontSize: '0.7rem' }}
                color="#a000e1"
            />{' '}
            {children}
        </Link>
    )
}

export default GoBackNavigation