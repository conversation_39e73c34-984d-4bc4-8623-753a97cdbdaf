import React from 'react'

type InputProps = {
    id?: string
    placeholder?: string
    type?: string
    icon?: React.ReactNode
    options?: string[]
    optionsComplex?: Array<{ name: string; code: string; code3: string }>
    defaultValue?: string
    className?: string
}

const InputForm = (props: InputProps) => {
    const {
        id,
        placeholder,
        type,
        options,
        optionsComplex,
        icon,
        defaultValue,
        className,
        ...rest
    } = props

    return type === 'select' ? (
        <select
            {...rest}
            id={id}
            className={`register__input ${className?.length && className}`}
            value={defaultValue}
        >
            {defaultValue ? null : (
                <option value="" label={placeholder}>
                    {placeholder}
                </option>
            )}
            {options?.map(option => (
                <option key={option} value={option} label={option}>
                    {option}
                </option>
            ))}
        </select>
    ) : type === 'selectComplex' ? (
        <select {...rest} id={id} className="register__input ">
            <option value="" label={placeholder}>
                {placeholder}
            </option>
            {optionsComplex?.map(option => (
                <option
                    key={option.code}
                    value={option.name}
                    label={option.name}
                >
                    {option.name}
                </option>
            ))}
        </select>
    ) : (
        <div className="register__wrapper">
            {icon ? icon : null}
            <input
                {...rest}
                id={id}
                placeholder={placeholder}
                type={type}
                className="register__input"
            />
        </div>
    )
}

export default InputForm
