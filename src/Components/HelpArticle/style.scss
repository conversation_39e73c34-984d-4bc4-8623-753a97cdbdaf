.article {

    h1 {
        font-family: '<PERSON><PERSON><PERSON>', sans-serif;
        font-size: 2.813rem;
        font-style: normal;
        font-weight: bold;
        line-height: 3.125rem;
    }

    &__header {
        border-radius: 6px;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
        margin: 25px 0 30px 0;
        max-width: 1383px;
        padding: 30px 25px;

        h2 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2.188rem;
            font-style: normal;
            font-weight: bold;
            line-height: 2.75rem;
            max-width: 900px;
        }
    }

    &__body {
        max-width: 1382px;

        p {
            font-family: 'Open Sans', sans-serif;
            font-size: 0.938rem;
            line-height: 1.563rem;

            & + p {
                margin: 30px 0;
            }
        }
    }

    &__footer {
        display: flex;
        flex-wrap: wrap;
        gap: 80px;
        justify-self: flex-start;
        margin: 80px 0;
    }

    &__card {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        max-width: 334px;

        h3 {
            font-family: '<PERSON>ser<PERSON>', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 1.563rem;
            line-height: 1.875rem;
            margin-bottom: 30px;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        p {
            font-family: 'Open Sans', sans-serif;
            font-size: 0.938rem;
            line-height: 1.563rem;

            & + p {
                margin-top: 30px;
            }
        }
    }
}

@media (max-width: 1014px) {
    .article {
        padding: 50px 27px 15px 27px;
    }
}

@media (max-width: 924px) {
    .article {
        &__card {
            & + .article__card {
                margin-top: 50px;
            }
        }
    }
}

@media (max-width: 600px) {
    .article {
        padding: 15px 27px 15px 27px;
    }
}
