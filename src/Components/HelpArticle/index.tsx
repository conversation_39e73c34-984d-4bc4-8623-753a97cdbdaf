import React, { useEffect } from 'react'
import { usePara<PERSON>, Link, useLocation } from 'react-router-dom'

import { FaqArticles } from 'Constants/faqArticles'
import ArrowRightIcon from 'Assets/arrowRight'

import './style.scss'

type ParamsType = {
    slug: string
}

export default function HelpArticle() {
    const { pathname } = useLocation()
    let { slug } = useParams<ParamsType>()

    useEffect(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' })
        return () => {}
    }, [pathname])

    return (
        <div className="content__container article pagePadding">
            <div className="help__navigation">
                <ul>
                    <li>
                        <Link to="/">Home</Link>
                        <ArrowRightIcon />
                    </li>
                    <li>
                        <Link to="/help">Help Center</Link>
                        <ArrowRightIcon />
                    </li>
                    <li>
                        <strong>Article</strong>
                    </li>
                </ul>
            </div>
            <h1>Gambyl Help Center</h1>
            <section className="article__header">
                <h2>
                    {FaqArticles.some(article => article.link === slug)
                        ? FaqArticles.find(article => article.link === slug)
                              ?.title
                        : 'Article not found'}
                </h2>
            </section>
            <section className="article__body">
                {FaqArticles.some(article => article.link === slug) ? (
                    <>
                        {' '}
                        <p>
                            In hac habitasse platea dictumst. Vivamus adipiscing
                            fermentum quam volutpat aliquam. Integer et elit
                            eget elit facilisis tristique. Nam vel iaculis
                            mauris. Sed ullamcorper tellus erat, non ultrices
                            sem tincidunt euismod. Fusce rhoncus porttitor
                            velit, eu bibendum nibh aliquet vel. Fusce lorem
                            leo, vehicula at nibh quis, facilisis accumsan
                            turpis.
                        </p>
                        <p>
                            Hella narwhal Cosby sweater McSweeney's, salvia
                            kitsch before they sold out High Life. Umami
                            tattooed sriracha meggings pickled Marfa Blue Bottle
                            High Life next level four loko PBR. Keytar pickled
                            next level keffiyeh drinking vinegar street art. Art
                            party vinyl Austin, retro whatever keytar mixtape.
                            Pickled ethnic farm-to-table distillery ugh chia.
                            Ethical Odd Future narwhal, mlkshk fap asymmetrical
                            next level High Life literally cred blog. Banh mi
                            swag art party, fashion axe you probably haven't
                            heard of them stumptown tousled food truck
                            post-ironic quinoa bicycle rights aesthetic keytar
                            Pitchfork.
                        </p>
                        <p>
                            Vestibulum rutrum quam vitae fringilla tincidunt.
                            Suspendisse nec tortor urna.{' '}
                        </p>
                        <p>
                            Ut laoreet sodales nisi, quis iaculis nulla iaculis
                            vitae. Donec sagittis faucibus lacus eget blandit.{' '}
                        </p>
                        <p>
                            Mauris vitae ultricies metus, at condimentum nulla.
                            Donec quis ornare lacus. Etiam gravida mollis tortor
                            quis porttitor.
                        </p>
                    </>
                ) : (
                    'The article you searched  does not exist'
                )}
            </section>
            <section className="article__footer">
                <div className="article__card">
                    <h3>Other related articles</h3>
                    {FaqArticles.map(article => (
                        <p key={article.link}>
                            <Link to={`${article.link}`}>{article.title} </Link>
                        </p>
                    ))}
                </div>
                <div className="article__card">
                    <h3>Recently viewed articles</h3>
                    <p>
                        {' '}
                        <Link to={`${FaqArticles[0].link}`}>
                            {FaqArticles[0].title}{' '}
                        </Link>
                    </p>
                </div>
            </section>
        </div>
    )
}
