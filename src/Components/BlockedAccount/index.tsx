import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import useScrollToTop from 'Hooks/useScrollToTop'
import './style.scss'

interface stateType {
    reason: string
}

export default function BlockedAccount() {
    useScrollToTop()

    const { state } = useLocation<stateType>()

    return (
        <div className="reopenAccount">
            <div className="reopenAccount__container">
                <div className="reopenAccount__content">
                    <h3>Your account is not active.</h3>
                    {state?.reason === 'Self Exclusion' ? (
                        <p>
                            You can get in touch{' '}
                            <a
                                href="http://help.gambyl.com/en/support/home"
                                target="_blank"
                                rel="noreferrer"
                            >
                                with us
                            </a>{' '}
                            so that we can reopen the account.
                        </p>
                    ) : null}
                    {state?.reason === 'Under Legal Age' ? (
                        <p>
                            Your account was blocked because you don´t have a
                            legal age for using Gambyl
                        </p>
                    ) : null}
                    {!state && (
                        <p>
                            You can get in touch{' '}
                            <a
                                href="http://help.gambyl.com/en/support/home"
                                target="_blank"
                                rel="noreferrer"
                            >
                                with us
                            </a>{' '}
                            .
                        </p>
                    )}
                    <Link
                        to="/"
                        className="btn btn__primary reopenAccount__btn"
                    >
                        Go to home page
                    </Link>
                </div>
            </div>
        </div>
    )
}
