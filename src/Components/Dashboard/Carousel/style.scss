@import '../../../Styles/colors';

.dashboard__carousel {
    .carousel-root {
        height: 100%;
        .carousel {
            .slider-wrapper {
                height: 100%;
                padding-bottom: 20px;
            }
            .slide {
                background-color: black;
            }
            .slider {
                height: 100%;
                .item {
                    height: 100%;
                    overflow: hidden;
                    max-width: 1403px;
                    .legend {
                        opacity: 1;
                        background: none;
                        width: 80%;
                        left: 60%;
                        padding-left: 30px;
                        .background {
                            background: $black;
                            filter: blur(10px);
                            height: 100%;
                            width: 100%;
                            position: absolute;
                            left: 0;
                            top: 0;
                            opacity: 0.5;
                        }
                        h3 {
                            font-family: Montserrat, sans-serif;
                            font-style: normal;
                            font-weight: bold;
                            font-size: 28px;
                            line-height: 40px;
                            position: relative;
                            text-align: left;
                        }
                        p {
                            font-family: Open Sans, sans-serif;
                            font-style: normal;
                            font-weight: normal;
                            font-size: 20px;
                            line-height: 26px;
                            text-align: left;
                            position: relative;
                        }
                    }
                    img {
                        min-width: 100%;
                        height: 100%;
                        object-fit: cover;
                        max-width: 1403px;
                    }
                }
            }
        }
        .carousel.carousel-slider {
            height: 100%;
        }

        .carousel .control-next.control-arrow:before {
            border-left: 8px solid $purple;
        }

        .carousel .control-prev.control-arrow:before {
            border-right: 8px solid $purple;
        }

        .carousel .control-dots .dot {
            transition: opacity 0.25s ease-in;
            opacity: 0.3;
            filter: alpha(opacity=30);
            box-shadow: 1px 1px 2px rgb(0 0 0 / 90%);
            background: $purple;
            border-radius: 6px;
            width: 30px;
            height: 5px;
            cursor: pointer;
            display: inline-block;
            margin: 0 8px;

            &.selected {
                opacity: 1;
                filter: alpha(opacity=100);
            }
        }

        @media (max-width: 960px) {
            .carousel .control-dots .dot {
                display: none;
            }

            .carousel .control-next.control-arrow:before {
                display: none;
            }

            .carousel .control-prev.control-arrow:before {
                display: none;
            }
        }
    }
}
