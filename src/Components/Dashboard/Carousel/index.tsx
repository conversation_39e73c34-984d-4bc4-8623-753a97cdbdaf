import React from 'react'
import { Carousel as CarouselLib } from 'react-responsive-carousel'
import { useNavigate } from 'react-router-dom'

import useIsAuthenticated from 'Hooks/useAuth'
import useLoginRedirection from 'Hooks/useLoginRedirection'

import AddsBanner from 'Components/AddsBanner'

import slide_1 from 'Assets/CarouselImages/Gambyl_Homepage_Slider-01.png'
import slide_2 from 'Assets/CarouselImages/Gambyl_Homepage_Slider-02.png'
import slide_3 from 'Assets/CarouselImages/Gambyl_Homepage_Slider-03.png'
import slide_4 from 'Assets/CarouselImages/Gambyl_Homepage_Slider-04.png'

import 'react-responsive-carousel/lib/styles/carousel.min.css'
import './style.scss'

export default function Carousel() {
    const { push } = useNavigate()
    const isAuthenticated = useIsAuthenticated()
    const login = useLoginRedirection()

    const handleClick = () => (isAuthenticated ? navigate('/deposit') : login())

    return (
        <>
            <div className="dashboard__carousel">
                <CarouselLib
                    showThumbs={false}
                    infiniteLoop
                    showArrows
                    swipeable
                    stopOnHover
                    autoPlay
                    showStatus={false}
                >
                    <div className="item" onClick={() => handleClick()}>
                        <AddsBanner
                            Addkey="1"
                            urlBannerString="https://servedbyadbutler.com/adserve/;ID=183145;size=1403x200;setID=552217;type=json;click=CLICK_MACRO_PLACEHOLDER"
                            errorFallback={slide_1}
                        />
                    </div>
                    <div className="item" onClick={() => handleClick()}>
                        <AddsBanner
                            Addkey="2"
                            urlBannerString="https://servedbyadbutler.com/adserve/;ID=183145;size=1403x200;setID=552218;type=json;click=CLICK_MACRO_PLACEHOLDER"
                            errorFallback={slide_2}
                        />
                    </div>
                    <div className="item" onClick={() => handleClick()}>
                        <AddsBanner
                            Addkey="3"
                            urlBannerString="https://servedbyadbutler.com/adserve/;ID=183145;size=1403x200;setID=552219;type=json;click=CLICK_MACRO_PLACEHOLDER"
                            errorFallback={slide_3}
                        />
                    </div>
                    <div className="item" onClick={() => handleClick()}>
                        <AddsBanner
                            Addkey="4"
                            urlBannerString="https://servedbyadbutler.com/adserve/;ID=183145;size=1403x200;setID=552220;type=json;click=CLICK_MACRO_PLACEHOLDER"
                            errorFallback={slide_4}
                        />
                    </div>
                </CarouselLib>
            </div>
        </>
    )
}
