import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

export default function BackToMainPage() {
    const { t } = useTranslation()
    return (
        <>
            <Link to={'/'} className="dashboard__card__row__left__linkPage">
                <FontAwesomeIcon
                    icon={faArrowLeft}
                    style={{ fontSize: '0.7rem' }}
                    color="#a000e1"
                />{' '}
                {t('backToMainPage')}
            </Link>
        </>
    )
}
