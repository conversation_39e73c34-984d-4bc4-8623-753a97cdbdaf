import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowUp } from '@fortawesome/free-solid-svg-icons'
import useMediaQuery from 'Hooks/useMediaQuery'

/**
 * @returns a component only for mobile screens
 */
export default function ScrollToTopBTN() {
    const [heightOfScroll, setHeightOfScroll] = React.useState<
        number | undefined
    >(0)
    const isMobile = useMediaQuery('(max-width: 630px)')

    React.useEffect(() => {
        const mainContainer = document.querySelector('.main__container')
        if (!mainContainer) return

        let isMounted = true // Ensure we don't update state on an unmounted component.

        const toggleVisibility = () => {
            if (isMounted) {
                setHeightOfScroll(mainContainer.scrollTop)
            }
        }

        // Attach scroll event listener to the main container.
        mainContainer.addEventListener('scroll', toggleVisibility, {
            passive: true
        })

        return () => {
            isMounted = false // Mark the component as unmounted.
            mainContainer.removeEventListener('scroll', toggleVisibility)
        }
    }, []) // Dependencies are empty since we reference `mainContainer` only inside the effect.

    const handleClick = () => {
        const mainContainer = document.querySelector('.main__container')
        if (mainContainer) {
            mainContainer.scrollTo({ top: 0, behavior: 'smooth' })
        }
    }

    return isMobile && heightOfScroll && heightOfScroll > 500 ? (
        <button onClick={handleClick} className="scrollUpBtn">
            <FontAwesomeIcon icon={faArrowUp} />
        </button>
    ) : null
}
