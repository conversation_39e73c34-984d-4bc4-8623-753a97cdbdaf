import React from 'react'
import { <PERSON> } from 'react-router-dom'

import { sports } from 'Constants/sports'

import './style.scss'

export default function MainCard() {
    return (
        <div className="dashboard__mainCard">
            <h3>QUICK LINKS TO TRENDING EVENTS</h3>
            <ul>
                <li>
                    <Link to="/">
                        {sports['horses']?.icon}
                        <span>Horse Racing</span>
                    </Link>
                </li>
                <li>
                    <Link to="/">
                        {sports['politics']?.icon}
                        <span>US Poltiics - Next President</span>
                    </Link>
                </li>
                <li>
                    <Link to="/">
                        {sports['soccer']?.icon}
                        <span>Belarus Cup - Game 1</span>
                    </Link>
                </li>
                <li>
                    <Link to="/">
                        {sports['inPlay']?.icon}
                        <span>CS:GO 2020 Tournament</span>
                    </Link>
                </li>
                <li>
                    <Link to="/">
                        {sports['cricket']?.icon}
                        <span>Cricket 2020 Tournament</span>
                    </Link>
                </li>
                <li>
                    <Link to="/">
                        {sports['boxing']?.icon}
                        <span>Boxing European Tournament</span>
                    </Link>
                </li>
            </ul>
        </div>
    )
}
