@import '../../../Styles/colors';

.dashboard__mainCard {
    padding-top: 23px;
    h3 {
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 0.8rem;
        line-height: 20px;
        letter-spacing: 1px;
        text-transform: uppercase;
        margin: 0 0 16px;
        padding-left: 32px;
    }
    ul {
        width: 100%;
        margin: 0;
        padding: 0;
        list-style-type: none;
        li {
            border-bottom: 1px solid $stepperGray;
            height: 55px;
            display: flex;
            align-items: center;
            padding-left: 32px;
            &:last-child {
                border-bottom: none;
            }
            a {
                font-family: Open Sans, sans-serif;
                font-style: normal;
                font-weight: normal;
                font-size: 0.938rem;
                line-height: 25px;
                color: $darkGrey;
                text-decoration: none;
                svg {
                    fill: $darkGrey;
                    width: 1em;
                }
                span {
                    margin-left: 12px;
                }
                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}
