import React, { useState } from 'react'
import {
    Service,
    BlockedService
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { Link, useNavigate } from 'react-router-dom'
import { useLedger, useParty } from '@daml/react'
import { signOut, useUserDispatch } from 'State/UserContext'
import LoaderSpinner from 'Components/Loader'
import { useTranslation } from 'react-i18next'

import useScrollToTop from 'Hooks/useScrollToTop'
import './style.scss'

export default function AccountDelete() {
    useScrollToTop()
    const navigate = useNavigate()
    const ledger = useLedger()
    const party = useParty()
    const userDispatch = useUserDispatch()
    const [loading, setLoading] = useState(false)

    const { t } = useTranslation()

    const handleConfirm = async () => {
        setLoading(true)
        let gamblingServiceQuery = await ledger.query(Service, {
            customer: party
        })
        await ledger.exercise(
            Service.BlockService,
            gamblingServiceQuery[0]?.contractId,
            {
                party,
                reason: 'Self Exclusion'
            }
        )
        const deleteResponse = await ledger.query(BlockedService, {
            service: { customer: party }
        })
        if (deleteResponse.length) {
            setLoading(false)
            signOut(userDispatch, history)
        }
    }

    return (
        <div className="deleteAccount">
            <div className="deleteAccount__container">
                {loading && <LoaderSpinner className="deleteAccount__loader" />}
                {!loading && (
                    <div className="deleteAccount__content">
                        <h3>{t("HeaderCloseAccount")}</h3>
                        <p>{t("P1CloseAccount")}</p>
                        <p>{t("P2CloseAccount")}</p>
                        <p>
                            {t("P3CloseAccount1")}{' '}
                            <a
                                href="https://help.gambyl.com/en/support/solutions/articles/***********-contact-us"
                                target="_blank"
                                rel="noreferrer"
                            >
                                {t("P3CloseAccount2")}
                            </a>{' '}
                            {t("P3CloseAccount3")} </p>
                        <Link to="/account" className="btn btn__primary">
                            {t("CloseAcccountCancel")}
                        </Link>
                        <button
                            className="btn btn__grey"
                            onClick={() => handleConfirm()}
                        >
                            {t("CloseAccountConfirm")}
                        </button>
                    </div>
                )}
            </div>
        </div>
    )
}
