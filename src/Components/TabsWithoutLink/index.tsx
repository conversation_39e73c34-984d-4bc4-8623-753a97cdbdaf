import React from 'react'
import './style.scss'

interface ITabsWithoutLink {
    tabs: {
        label: string
        value: string
        onclick: () => void
    }[]
    selectedTab: string
    classStyle?: string
}

export default function TabsWithoutLink({
    tabs,
    selectedTab,
    classStyle
}: ITabsWithoutLink) {
    return (
        <div
            className={`tabsWithoutLink__tabs ${classStyle ? classStyle : ''}`}
        >
            {tabs.length &&
                tabs.map(tab => (
                    <span
                        key={tab.label}
                        className={
                            selectedTab === tab.value
                                ? 'tabsWithoutLink__tab tabsWithoutLink__tab--active'
                                : 'tabsWithoutLink__tab'
                        }
                        onClick={() => tab.onclick()}
                    >
                        {tab.label}
                    </span>
                ))}
        </div>
    )
}
