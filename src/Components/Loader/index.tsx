import React from 'react'
import Loader from 'react-loader-spinner'

import './style.scss'

export default function LoaderSpinner({
    className,
    height,
    width
}: {
    className?: string
    height?: number
    width?: number
}) {
    return (
        <div className={className?.length ? className : 'loader'}>
            <Loader
                type="Puff"
                color="#a000e1"
                height={height ? height : 100}
                width={width ? width : 100}
            />
        </div>
    )
}
