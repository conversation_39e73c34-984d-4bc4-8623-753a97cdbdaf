@import '../../Styles/colors';

.help {

    &__navigation {
        margin-bottom: 32px;
        ul {
            display: flex;
            font-family: 'Open Sans', sans-serif;
            font-size: 0.938rem;
            gap: 10px;
            line-height: 1.25rem;
            list-style: none;

            li {
                align-items: center;
                cursor: pointer;
                display: flex;
                flex-wrap: wrap;
                gap: 15px;

                a {
                    color: inherit;
                    text-decoration: none;
                }
            }
        }
    }

    &__banner {
        margin-bottom: 60px;

        img {
            background: #0d0d0d;
            border-radius: 6px;
            height: auto;
            max-width: 100%;
            mix-blend-mode: normal;
            opacity: 0.9;
        }
    }

    &__title {
        display: flex;
        font-family: 'Montserrat', sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 1.563rem;
        justify-content: center;
        line-height: 1.875rem;
        margin-bottom: 60px;
        text-align: center;
    }

    &__cardGrid {
        display: grid;
        grid-gap: 20px;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        align-items: center;
    }

    &__card {
        border-radius: 6px;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 207px;

        h4 {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.25rem;
            font-weight: bold;
            line-height: 1.563rem;
            padding: 20px 0;
            text-align: center;
        }

        p {
            font-family: 'Montserrat', sans-serif;
            font-size: 0.688rem;
            font-style: normal;
            letter-spacing: 0.053rem;
            line-height: 1.25rem;
            max-width: 200px;
            text-align: center;
            text-transform: uppercase;
        }
    }

    &__faq {
        margin: 60px 0;
    }

    &__faqContainer {
        div {
            align-items: center;
            border-radius: 6px;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            display: flex;
            font-family: 'Montserrat', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 1.125rem;
            justify-content: space-between;
            line-height: 25px;
            margin: 0 auto;
            padding: 1.25rem;
            width: 90%;

            a {
                color: inherit;
                display: flex;
                justify-content: space-between;
                text-decoration: none;
                width: 100%;
            }
        }
    }
}

.contact {
    background: url('../../Assets/cover-bg-email.png');
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    min-height: 432px;

    &__formContainer {
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 600px;
        width: 100%;

        p {
            color: #ffffff;
            font-family: 'Open Sans', sans-serif;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            text-transform: uppercase;
        }

        h3 {
            color: #ffffff;
            font-family: 'Montserrat', sans-serif;
            font-size: 2.188rem;
            font-style: normal;
            font-weight: bold;
            line-height: 2.5rem;
            text-align: center;
        }
    }

    &__form {
        width: 100%;

        textarea {
            border: 1px solid #d3dce5;
            border-radius: 6px;
            color: $black;
            font-family: 'Open Sans', sans-serif;
            font-size: 0.813rem;
            font-style: normal;
            font-weight: normal;
            height: 100px;
            line-height: 1.25rem;
            margin-top: 23px;
            padding: 15px;
            resize: none;
            width: 100%;

            &:focus {
                border-color: $blueShaddow;
                box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
                outline: 0;
            }

            &::placeholder {
                color: $black;
                font-family: 'Open Sans', sans-serif;
                font-size: 0.813rem;
                font-style: normal;
                font-weight: normal;
                height: 50px;
                line-height: 1.25rem;
            }
        }
    }
}

@media (max-width: 1014px) {
    .help {
        padding: 50px 27px 15px 27px;
    }
}

@media (max-width: 600px) {
    .help {
        padding: 15px 27px 15px 27px;
    }
}
