import React from 'react'
import { Formik } from 'formik'
import * as Yup from 'yup'
import { Link } from 'react-router-dom'

import InputForm from 'Components/Inputs'
import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import FaqArticle from './FaqArticle'
import HelpCard from './HelpCard'

import HelpBanner from 'Assets/help-banner.png'
import Message from 'Assets/HelpIcons/message'
import ArrowRightIcon from 'Assets/arrowRight'

import { helpCards } from 'Constants/helpCardsContent'
import { FaqArticles } from 'Constants/faqArticles'

import useScrollToTop from 'Hooks/useScrollToTop'

import './style.scss'

const emailFormSchema = Yup.object({
    email: Yup.string()
        .email('Must be an email type')
        .required('Required field'),
    content: Yup.string().required('Required field')
})

export default function Help() {
    useScrollToTop()

    return (
        <div>
            <div className="content__container help pagePadding">
                <div className="help__navigation">
                    <ul>
                        <li>
                            <Link to="/">Home</Link>
                            <ArrowRightIcon />
                        </li>
                        <li>
                            <strong>Help Center</strong>
                        </li>
                    </ul>
                </div>
                <section className="help__banner">
                    <img src={HelpBanner} alt="help banner" />
                </section>
                <section>
                    <div className="help__title">
                        <h3>Choose a category to get started:</h3>
                    </div>
                    <div className="help__cardGrid">
                        {helpCards.map(helpCard => (
                            <HelpCard
                                key={helpCard.title}
                                icon={helpCard.icon}
                                title={helpCard.title}
                                subtitle={helpCard.subtitle}
                            />
                        ))}
                    </div>
                </section>
                <section className="help__faq">
                    <div className="help__title">
                        <h3>Top 5 questions asked</h3>
                    </div>
                    {FaqArticles.map(article => (
                        <FaqArticle
                            key={article.title}
                            link={article.link}
                            title={article.title}
                        />
                    ))}
                </section>
            </div>
            <section className="content__container contact">
                <div className="contact__formContainer">
                    <p>We are here to help, so get in touch.</p>
                    <h3>Still can’t find what you need?</h3>
                    <Formik
                        initialValues={{ email: '', content: '' }}
                        onSubmit={values => {
                            alert(JSON.stringify(values))
                        }}
                        validationSchema={emailFormSchema}
                    >
                        {formik => (
                            <form
                                className="contact__form"
                                onSubmit={formik.handleSubmit}
                            >
                                <InputForm
                                    id="email"
                                    placeholder="Please add your email"
                                    type="text"
                                    icon={<Message />}
                                    {...formik.getFieldProps('email')}
                                />
                                {formik.touched.email && formik.errors.email ? (
                                    <ErrorMessage
                                        message={formik.errors.email}
                                    />
                                ) : null}
                                <textarea
                                    placeholder="Your question or message"
                                    id="content"
                                    {...formik.getFieldProps('content')}
                                />
                                {formik.touched.content &&
                                formik.errors.content ? (
                                    <ErrorMessage
                                        message={formik.errors.content}
                                    />
                                ) : null}
                                <button
                                    className="registerbtn__nextStep"
                                    type="submit"
                                >
                                    Submit
                                </button>
                            </form>
                        )}
                    </Formik>
                </div>
            </section>
        </div>
    )
}
