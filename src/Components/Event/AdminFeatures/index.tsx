import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { useLedger } from '@daml/react'

import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import ToggleFeatureBtn from './ToggleFeatureBtn'

import './style.scss'
import CancelEventBTN from './CancelEventBTN'

export default function AdminFeatures({
    event
}: {
    event: CreateEvent<EventInstrument, EventInstrument.Key, string>
}) {
    const ledger = useLedger()

    return (
        <>
            <div className="adminAction__container">
                <ToggleFeatureBtn
                    ledger={ledger}
                    isEventFeatured={event.payload.featured}
                    eventKey={event.key._3}
                />
                <CancelEventBTN
                    ledger={ledger}
                    eventLabel={event?.payload?.eventId?.label}
                />
            </div>
        </>
    )
}
