import React from 'react'
import { toast } from 'react-toastify'
import Ledger from '@daml/ledger'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import BasicModal from 'Components/Modals/BasicModal'

interface ICancelEventBTN {
    eventLabel: string
    ledger: Ledger
}

export default function CancelEventBTN({
    eventLabel,
    ledger
}: ICancelEventBTN) {
    const [openModal, setOpenModal] = React.useState(false)
    const handleOpen = () => setOpenModal(true)
    const handleClose = () => setOpenModal(false)

    const handleClick = () => {
        ledger
            .query(EventManagerService)
            .then(serviceEventManager => {
                if (serviceEventManager.length > 0) {
                    ledger
                        .exercise(
                            EventManagerService.RequestCancelEvent,
                            serviceEventManager[0].contractId,
                            { eventLabel }
                        )
                        .then(() => {
                            toast.success('Requested event cancelation')
                            return handleClose()
                        })
                        .catch(e => {
                            console.log(e)
                            toast.error(
                                'Something went wrong, please try again later.'
                            )
                            return handleClose()
                        })
                    return
                }
                toast.error(
                    "You don't have permission to do the following request."
                )
                return handleClose()
            })
            .catch(e => {
                console.log(e)
                toast.error('Something went wrong, please try again later.')
                return handleClose()
            })
    }
    return (
        <>
            <button className="btn btn__primary" onClick={handleOpen}>
                Cancel
            </button>
            {openModal ? (
                <BasicModal
                    isOpenModal={openModal}
                    body={
                        <p>
                            Are you sure you want to cancel this event? All the
                            bets (matched and unmatched) associated with this
                            event will be canceled.
                        </p>
                    }
                    footerBody={
                        <>
                            <button
                                className="btn btn__primary"
                                onClick={handleClick}
                            >
                                Yes
                            </button>
                            <button
                                className="btn btn__grey"
                                onClick={handleClose}
                            >
                                No
                            </button>
                        </>
                    }
                    handleClose={handleClose}
                />
            ) : null}
        </>
    )
}
