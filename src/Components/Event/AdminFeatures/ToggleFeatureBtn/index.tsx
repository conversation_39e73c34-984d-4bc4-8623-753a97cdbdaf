import React from 'react'
import Switch from 'react-switch'
import { toast } from 'react-toastify'
import Ledger from '@daml/ledger'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service/index'

interface IToggleFeatBtn {
    isEventFeatured: boolean
    eventKey: string
    ledger: Ledger
}

export default function ToggleFeatureBtn({
    isEventFeatured,
    eventKey,
    ledger
}: IToggleFeatBtn) {
    const toggleFeatureEvent = () => {
        Promise.all([
            ledger.query(MarketingManagerService),
            ledger.query(EventManagerService)
        ]).then(([marketingManagerQuery, eventManagerQuery]) => {
            if (marketingManagerQuery.length > 0) {
                ledger
                    .exercise(
                        MarketingManagerService.RequestToggleFeaturedEvent,
                        marketingManagerQuery[0].contractId,
                        { label: eventKey }
                    )
                    .then(() =>
                        toast.success(
                            isEventFeatured
                                ? 'Event removed from featured successfully.'
                                : 'Event featured successfully.'
                        )
                    )
                    .catch(() =>
                        toast.error(
                            'Something went wrong, please try again later.'
                        )
                    )
                return
            }
            ledger
                .exercise(
                    EventManagerService.RequestToggleFeaturedEvent,
                    eventManagerQuery[0].contractId,
                    { label: eventKey }
                )
                .then(() =>
                    toast.success(
                        isEventFeatured
                            ? 'Event removed from featured successfully.'
                            : 'Event featured successfully.'
                    )
                )
                .catch(() =>
                    toast.error('Something went wrong, please try again later.')
                )
            return
        })
    }

    return (
        <div className="adminAction__featured">
            <label htmlFor="Featured">Featured:</label>
            <Routes
                checked={isEventFeatured}
                onChange={toggleFeatureEvent}
                onColor="#a000e1"
            />
        </div>
    );
}
