@import '../../Styles/colors';

@font-face {
    font-family: 'OpenSansCondensed';
    src: url('../../Assets/fonts/OpenSans-CondBold.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

.event {
    padding-bottom: 18px;

    &:first-of-type {
        padding: 18px 0;
    }
}

.eventIconAndLeage {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 7px;
    margin-bottom: 18px;
    &__icon {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        flex-direction: column;
        gap: 6px;
        svg {
            width: 20px !important;
            height: 20px !important;
        }
        p {
            font-size: 10px;
            font-weight: 400 !important;
        }
    }
    &__tournament {
        font-family: 'OpenSansCondensed', sans-serif;
        font-size: 18px;
        line-height: 20px;
        text-transform: uppercase;
    }
}

.betCard {
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    width: 100%;
    flex-wrap: wrap;
    padding: 10px 20px;
    box-shadow: 4px 4px 4px 4px #00000040;
    margin: 15px 0;
    font-family: 'OpenSansCondensed', sans-serif;

    &__title {
        display: flex;
        flex-direction: column;
        gap: 10px;
        font-size: 18px;
        font-weight: 700;

        &--metadata {
            color: $metadataGrey;
            @media (max-width: 1760px) {
                font-size: 12px;
            }
        }

        &--link {
            color: $purple;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            text-decoration: none;
            transition: filter 0.2s;
            @media (max-width: 1760px) {
                font-size: 12px;
            }

            &:hover {
                filter: brightness(110%);
            }
        }
    }

    &__titleMaindataContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;

        &--metadata {
            color: $metadataGrey;
            @media (max-width: 1760px) {
                font-size: 12px;
            }
        }
    }

    &__titleMetadataContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;
    }

    &__betsContainer {
        margin-top: 16px;
        gap: 50px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);

        @media (max-width: 1500px) {
            grid-template-columns: repeat(1, 1fr);
            gap: 10px;
        }
    }

    &__divider {
        @media (min-width: 1500px) {
            display: none;
        }
    }

    &__betsWrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 6px;
        max-width: 500px;
        @media (max-width: 1500px) {
            max-width: 100%;
            gap: 4px;
        }
    }

    &__betOddBoxesItemContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        gap: 5px;
    }

    &__betsWrapperTitle {
        font-weight: 700;
        font-size: 14px;
        margin-bottom: 15px;
        flex: 0 0 140px;
        text-align: center;
        height: 40px;
        @media (min-width: 1800px) {
            flex: 0 0 205px;
            font-size: 18px;
        }
        @media (max-width: 1500px) {
            display: none;
        }

        &--disabled {
            color: #e5e5e5;
        }
    }

    &__betNameBoxes {
        font-size: 18px;
        @media (max-width: 1800px) {
            font-size: 14px;
        }
        @media (max-width: 1500px) {
            font-size: 12px;
        }

        &--disabled {
            color: #e5e5e5;
        }
    }

    &__betOddBoxes {
        display: flex;
        gap: 5px;
    }

    &__betOddBox {
        display: flex;
        flex-direction: column;
        border-radius: 6px;
        justify-content: center;
        align-items: center;
        width: 70px;
        height: 50px;
        transition: filter 0.2s;
        font-size: 14px;
        cursor: pointer;

        @media (min-width: 1800px) {
            width: 100px;
            font-size: 16px;
        }

        &:hover {
            filter: brightness(110%);
        }

        &--purple {
            background: $purple;
            color: $white;
        }

        &--yellow {
            background: $yellow;
            color: $darkGrey;
        }

        &--disabled {
            background: #e5e5e5;
            color: #e5e5e5;
            cursor: not-allowed;
        }
    }

    &__betOddBoxMakeOffer {
        display: flex;
        flex-direction: column;
        border-radius: 6px;
        justify-content: center;
        align-items: center;
        width: 70px;
        height: 50px;
        transition: filter 0.2s;
        font-size: 14px;
        cursor: pointer;
        text-align: center;

        @media (min-width: 1800px) {
            width: 100px;
            font-size: 16px;
        }

        @media (max-width: 1500px) {
            font-size: 12px;
        }

        &:hover {
            filter: brightness(110%);
        }

        &--purple {
            background: $purple;
            color: $white;
        }

        &--yellow {
            background: $yellow;
            color: $darkGrey;
        }
    }

    &__betStake {
        font-size: 12px;

        @media (max-width: 1500px) {
            font-size: 10px;
        }
    }
}
