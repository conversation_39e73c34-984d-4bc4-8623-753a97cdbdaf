import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { CreateEvent } from '@daml/ledger'
import { faArrowRight } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { getFilteredLanguageName } from 'Components/Event/utils'
import getFormattedDate from 'Containers/Dashboard/getFormattedDate'
import numeral from 'numeral'
import { useMemo } from 'react'
import { Link } from 'react-router-dom'

import { useI18LanguageContext } from 'State/LanguageState'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import { generateURLPathSingleEvent } from 'Utils/getPathFromSubmarket'
import useExistsInGamblingListing from './hooks/useExistsInGamblingListing'
import MakeOfferBanner from './MakeOfferBanner'
import useHidePropBets from './hooks/useHidePropBets'

const TitleSection = ({
    eventContract,
    displayMakeOffer
}: {
    eventContract: CreateEvent<EventInstrument, EventInstrument.Key, string>
    displayMakeOffer: boolean
}) => {
    const { US } = useSportTranslationsContext()
    const { lang } = useI18LanguageContext()
    const { isSingleEventRoute } = useHidePropBets()

    const {
        eventId: { label },
        details: { eventTitle, eventGame, startDate }
    } = eventContract.payload
    const existsInGamblingListingContracts = useExistsInGamblingListing(label)

    let sumedMatchedAmmount = useMemo(() => {
        return existsInGamblingListingContracts.length > 0
            ? existsInGamblingListingContracts?.reduce(
                  (accumulator, currentValue) => {
                      return (
                          accumulator +
                          Number(currentValue?.payload.matchedAmount)
                      )
                  },
                  0
              )
            : 0
    }, [existsInGamblingListingContracts])

    let totalMatchedToDisplay =
        sumedMatchedAmmount > 0
            ? numeral(sumedMatchedAmmount).format('$0,0.00')
            : null

    const marketTag = eventContract.payload.details?.market.tag
    const marketValue =
        typeof eventContract.payload.details?.market.value === 'string'
            ? eventContract.payload.details?.market.value
            : ''
    const isSportEvent = marketTag === 'Sport'

    const baseName = getFilteredLanguageName(US, marketValue, isSportEvent)
    let league = eventContract?.payload.details.submarkets.filter(
        (value: any) => value.tag === 'Tournament'
    )[0]?.value
    const tournamentURLName = Boolean(league) === false ? 'tournament' : league
    const assetLabel = eventContract?.payload?.eventId?.label
    const sportName = baseName
        ? Object.values(baseName)[0].replaceAll(' ', '')
        : eventContract.payload.details.market.tag

    const englishEventTitleForPath = getEventTitleFromLang(
        eventContract?.payload?.details?.eventTitle,
        'EN'
    )

    const singleEventURL = generateURLPathSingleEvent(
        sportName,
        tournamentURLName,
        englishEventTitleForPath,
        assetLabel
    )

    return (
        <div className="betCard__title">
            <div className="betCard__titleMaindataContainer">
                <div>
                    <p>
                        {eventGame ? (
                            <p className="betCard__titleMaindataContainer--metadata">
                                Game {eventGame}:
                            </p>
                        ) : null}
                    </p>
                    <p>{getEventTitleFromLang(eventTitle, lang)}</p>
                    {totalMatchedToDisplay ? (
                        <p className="betCard__titleMaindataContainer--metadata">
                            Total Matched:{' '}
                            <strong>{totalMatchedToDisplay}</strong>
                        </p>
                    ) : null}
                </div>
                {displayMakeOffer ? <MakeOfferBanner /> : null}
            </div>
            <div className="betCard__titleMetadataContainer">
                <p className="betCard__title--metadata">
                    {getFormattedDate(startDate)}
                </p>
                {isSingleEventRoute ? (
                    <Link to={singleEventURL} className="betCard__title--link">
                        View Market
                        <FontAwesomeIcon
                            icon={faArrowRight}
                            style={{ fontSize: '0.7rem', marginLeft: '5px' }}
                            color="#a000e1"
                        />
                    </Link>
                ) : null}
            </div>
        </div>
    )
}

export default TitleSection
