import React from 'react'
import QuestionMarkIcon from 'Components/Icons/QuestionMark'
import { useTranslation } from 'react-i18next'

import './style.scss'
import TooltipComponent from 'Components/Tooltip'

const TooltipTopic = 'tooltip_makeOffer'

const MakeOfferTooltip = () => {
    const { t } = useTranslation()
    const text = t('makeOfferTooltip')
    const content = t('makeOfferTooltipContent')
    return (
        <div className="makeOfferTooltip">
            <TooltipComponent topic={TooltipTopic} />
            <span className="makeOfferTooltip__actions">
                {/* this button function just to propagate tooltip data to svg */}
                <button
                    style={{
                        background: 'none',
                        color: 'inherit',
                        border: 'none',
                        padding: '0',
                        font: 'inherit',
                        cursor: 'pointer',
                        outline: 'inherit'
                    }}
                    data-tip={content}
                    data-for={TooltipTopic}
                >
                    <QuestionMarkIcon />
                </button>
                <p>{text}</p>
            </span>
        </div>
    )
}

export default MakeOfferTooltip
