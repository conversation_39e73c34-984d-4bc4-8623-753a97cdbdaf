import { useTranslation } from 'react-i18next'

const MakeOfferBetBox = ({
    handleClick,
    classStyle
}: {
    handleClick: () => void
    classStyle: 'purple' | 'yellow'
}) => {
    const { t } = useTranslation()
    const makeOfferMessage = t('MakeOffer')
    return (
        <div
            className={`betCard__betOddBoxMakeOffer betCard__betOddBoxMakeOffer--${classStyle}`}
            onClick={handleClick}
        >
            <strong className="makeOfferText">{makeOfferMessage}</strong>
        </div>
    )
}

export default MakeOfferBetBox
