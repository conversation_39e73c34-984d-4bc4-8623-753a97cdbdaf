import React from 'react'

const BaseOddBoxes = ({
    classStyle,
    handleClick,
    children
}: {
    classStyle: 'purple' | 'yellow' | 'disabled'
    handleClick: () => void
    children: React.ReactNode
}) => {
    return (
        <div
            className={`betCard__betOddBox betCard__betOddBox--${classStyle}`}
            onClick={handleClick}
        >
            {children}
        </div>
    )
}

export default BaseOddBoxes
