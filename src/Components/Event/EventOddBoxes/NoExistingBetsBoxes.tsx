import React from 'react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { CreateEvent } from '@daml/ledger'
import { OutcomeType } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'
import { generateParticipantsRefactor } from '../utils'
import { useTranslation } from 'react-i18next'
import BetWrapperContainer from '../BetSections/BetWrapperContainer'
import BaseContainerEventOddBoxes from './BaseContainerEventOddBoxes'
import BaseOddBoxes from './BaseOddBoxes'

const NoExistingBetsBoxesBaseContainer = ({
    participantName
}: {
    participantName: string
}) => {
    return (
        <BaseContainerEventOddBoxes
            isDisabled
            betTitle={participantName}
            children={
                <>
                    {React.Children.toArray(
                        [1, 2].map(ele => (
                            <BaseOddBoxes
                                handleClick={() => {}}
                                classStyle="disabled"
                                children={<strong>-</strong>}
                            />
                        ))
                    )}
                </>
            }
        />
    )
}

const NoExistingBetsBoxes = ({
    event,
    parentBet
}: {
    event: CreateEvent<EventInstrument, EventInstrument.Key, string>
    parentBet: OutcomeType['tag']
}) => {
    const { t } = useTranslation()

    if (parentBet === 'OverUnder') {
        return (
            <BetWrapperContainer
                hasDiviser
                betWrapperTitle="Total Not Available"
                isDisabled={true}
                children={
                    <>
                        <NoExistingBetsBoxesBaseContainer participantName="Under" />
                        <NoExistingBetsBoxesBaseContainer participantName="Over" />
                    </>
                }
            />
        )
    }

    const { eventParticipants } = event.payload.details

    const { A: sideA, B: sideB } = generateParticipantsRefactor({
        eventParticipants
    })
    const titleSideA = sideA.join('/')
    const titleSideB = sideB.join(' / ')

    return (
        <BetWrapperContainer
            hasDiviser
            isDisabled={true}
            betWrapperTitle="Spread Not Available"
            children={
                <>
                    <NoExistingBetsBoxesBaseContainer
                        participantName={titleSideA}
                    />
                    <NoExistingBetsBoxesBaseContainer
                        participantName={t('Draw')}
                    />
                    <NoExistingBetsBoxesBaseContainer
                        participantName={titleSideB}
                    />
                </>
            }
        />
    )
}

export default NoExistingBetsBoxes
