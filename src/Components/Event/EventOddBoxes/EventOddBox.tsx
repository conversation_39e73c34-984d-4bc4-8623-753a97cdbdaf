import { Outcome } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'
import { addPlusToUnderdog, formatDisplayStake } from '../utils'
import { useTranslation } from 'react-i18next'
import { MakeOfferOddValues } from 'Constants/MakeOfferOddValues'
import MakeOfferBetBox from './MakeOfferBetBox'
import BaseOddBoxes from './BaseOddBoxes'
import BaseContainerEventOddBoxes from './BaseContainerEventOddBoxes'

type EventOddBoxProps = {
    participantName?: string
    betTitle?: string
    oddBack: any //missing this type
    oddLay: any //missing this type
    stakeBack: string
    stakeLay: string
    outcome: Outcome
    isDisabled: string
    setBackBet: () => void
    setLayBet: () => void
    oddState: string
}

/**
 *
 * @returns a jsx component with a pair of a purple and yellow box or grey if disabled
 */
const EventOddBox = ({
    participantName,
    betTitle,
    oddBack,
    oddLay,
    stakeBack,
    stakeLay,
    outcome,
    isDisabled,
    setBackBet,
    setLayBet,
    oddState
}: EventOddBoxProps) => {
    const isDisabledClass = (layOrBack: 'lay' | 'back') =>
        isDisabled.length > 0
            ? 'disabled'
            : layOrBack === 'back'
            ? 'purple'
            : 'yellow'

    const showMakeOfferMessageBack = MakeOfferOddValues.has(
        oddBack?.oddForPresentation
    )
    const showMakeOfferMessageLay = MakeOfferOddValues.has(
        oddLay?.oddForPresentation
    )
    const { t } = useTranslation()

    const participantDisplayName = participantName
        ? participantName.toLocaleLowerCase() === 'draw'
            ? t('Draw')
            : participantName
        : ''

    return (
        <BaseContainerEventOddBoxes
            betTitle={`${participantDisplayName ?? ''} ${betTitle}`}
            children={
                <>
                    {showMakeOfferMessageBack ? (
                        <MakeOfferBetBox
                            classStyle="purple"
                            handleClick={setBackBet}
                        />
                    ) : (
                        <BaseOddBoxes
                            classStyle={isDisabledClass('back')}
                            handleClick={() =>
                                !outcome ? () => {} : setBackBet()
                            }
                            children={
                                <>
                                    <strong>
                                        {addPlusToUnderdog(
                                            oddBack?.oddForPresentation,
                                            oddState
                                        )}
                                        {oddBack?.oddForPresentation}
                                    </strong>
                                    <span className="betCard__betStake">
                                        {formatDisplayStake(stakeBack)}
                                    </span>
                                </>
                            }
                        />
                    )}
                    {showMakeOfferMessageLay ? (
                        <MakeOfferBetBox
                            classStyle="yellow"
                            handleClick={setLayBet}
                        />
                    ) : (
                        <BaseOddBoxes
                            classStyle={isDisabledClass('lay')}
                            handleClick={() =>
                                !outcome ? () => {} : setLayBet()
                            }
                            children={
                                <>
                                    <strong>
                                        {addPlusToUnderdog(
                                            oddLay?.oddForPresentation,
                                            oddState
                                        )}
                                        {oddLay?.oddForPresentation}
                                    </strong>
                                    <span className="betCard__betStake">
                                        {formatDisplayStake(stakeLay)}
                                    </span>
                                </>
                            }
                        />
                    )}
                </>
            }
        />
    )
}

export default EventOddBox
