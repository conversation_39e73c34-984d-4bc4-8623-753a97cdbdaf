import TooltipComponent from 'Components/Tooltip'
import React from 'react'

const BaseContainerEventOddBoxes = ({
    children,
    betTitle,
    isDisabled = false
}: {
    children: React.ReactNode
    betTitle: string
    isDisabled?: boolean
}) => {
    const trimmedTitleLength = betTitle.trim().length
    return (
        <div className="betCard__betOddBoxesItemContainer">
            <p
                className={`betCard__betNameBoxes ${
                    isDisabled ? 'betCard__betNameBoxes--disabled' : ''
                }`}
            >
                {trimmedTitleLength > 25 ? (
                    <>
                        <TooltipComponent topic={betTitle} />
                        <span data-tip={betTitle} data-for={betTitle}>
                            {betTitle.slice(0, 15)}...
                        </span>
                    </>
                ) : (
                    betTitle
                )}
            </p>
            <div className="betCard__betOddBoxes">{children}</div>
        </div>
    )
}

export default BaseContainerEventOddBoxes
