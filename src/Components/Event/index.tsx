import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import ThreewaySection from './BetSections/ThreewaySection'
import OverUnderSection from './BetSections/OverUnderSection'
import TwoWaySection from './BetSections/TwoWaySection'
import ThreewayHandicapSection from './BetSections/ThreewayHandicapSection'
import useHidePropBets from './hooks/useHidePropBets'
import MakeOfferTooltip from 'Components/Event/MakeOfferTooltip'
import AdminFeatures from 'Components/Event/AdminFeatures'
import { useManagerLoginState } from 'State/ManagerLoginContext'
import EventIconAndLeague from './EventIconAndLeague'
import useShouldDisplayMakeOffer from './hooks/useShouldDisplayMakeOffer'
import TitleSection from './TitleSection'

import './style.scss'

const Event = ({
    event
}: {
    event: CreateEvent<EventInstrument, EventInstrument.Key, string>
}) => {
    const { isAuthenticated: isAdminAuthenticated } = useManagerLoginState()
    const { hidePropBets } = useHidePropBets()
    const displayMakeOffer = useShouldDisplayMakeOffer(event)

    return (
        <div className="event">
            <EventIconAndLeague event={event} />
            <div className="betCard">
                <TitleSection
                    eventContract={event}
                    displayMakeOffer={displayMakeOffer}
                />
                <div className="betCard__betsContainer">
                    <ThreewaySection eventContract={event} />
                    <TwoWaySection eventContract={event} />
                    {hidePropBets ? null : (
                        <OverUnderSection eventContract={event} />
                    )}
                    {hidePropBets ? null : (
                        <ThreewayHandicapSection eventContract={event} />
                    )}
                </div>
                {displayMakeOffer ? <MakeOfferTooltip /> : null}
                {isAdminAuthenticated ? <AdminFeatures event={event} /> : null}
            </div>
        </div>
    )
}

export default Event
