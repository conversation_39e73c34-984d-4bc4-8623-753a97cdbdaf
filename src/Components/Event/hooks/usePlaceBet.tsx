import { baseStakeForBetSlip, prepareStake } from '../utils'
import { showBets, useBetsDispatch, addBet } from 'State/BetsContext'
import { v4 as uuidV4 } from 'uuid'

const usePlaceBet = () => {
    const betsDispatch = useBetsDispatch()
    const setBet = async (
        odd: any,
        sideTag: string,
        outcome: any,
        eventkey: any,
        title: string,
        participant: string,
        startDate: string
    ) => {
        let stakeToPass = prepareStake(baseStakeForBetSlip)
        await addBet(betsDispatch, {
            betPlacementId: uuidV4(),
            stake: stakeToPass,
            odd,
            sideTag,
            outcome,
            eventkey,
            title,
            participant,
            startDate
        })
        await showBets(betsDispatch)
    }
    return { setBet }
}

export default usePlaceBet
