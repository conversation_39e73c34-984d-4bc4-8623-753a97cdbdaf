import { useRouteMatch } from 'react-router-dom'
import useMediaQuery from 'Hooks/useMediaQuery'

const useHidePropBets = () => {
    const routeMatcher = useRouteMatch(
        '/:sport/:tournament/:eventTitle/:assetLabel'
    )
    const matchSingleEvent = routeMatcher
        ? !routeMatcher?.url.split('/').some(v => v === 'events')
        : false
    const isWiderScreen = useMediaQuery('(min-width: 1500px)')

    return {
        hidePropBets: !isWiderScreen && !matchSingleEvent,
        isSingleEventRoute: !matchSingleEvent
    }
}

export default useHidePropBets
