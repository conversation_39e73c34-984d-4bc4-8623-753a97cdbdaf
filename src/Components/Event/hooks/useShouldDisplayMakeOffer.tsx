import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { shouldDisplayMakeOffer } from '../utils'
import useExistsInGamblingListing from './useExistsInGamblingListing'

const useShouldDisplayMakeOffer = (
    event: CreateEvent<EventInstrument, EventInstrument.Key, string>
) => {
    const { label } = event.payload.eventId
    const existsInGamblingListingContracts = useExistsInGamblingListing(label)
    const displayMakeOfferBanner = React.useMemo(
        () =>
            shouldDisplayMakeOffer(
                event,
                'Moneyline',
                existsInGamblingListingContracts
            ),
        [event, existsInGamblingListingContracts]
    )

    return displayMakeOfferBanner
}

export default useShouldDisplayMakeOffer
