import { useMemo } from 'react'
import { useGamblingListingContext } from 'State/GamblingListingContext'

const useExistsInGamblingListing = (label: string) => {
    const { gamblingListingContracts } = useGamblingListingContext()
    const existsInGamblingListingContracts = useMemo(() => {
        return gamblingListingContracts.filter(a => {
            return a?.payload.internalId.split('_').pop() === label
        })
    }, [gamblingListingContracts, label])
    return existsInGamblingListingContracts
}

export default useExistsInGamblingListing
