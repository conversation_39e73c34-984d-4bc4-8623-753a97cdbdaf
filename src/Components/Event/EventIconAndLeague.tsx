import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { useTranslation } from 'react-i18next'
import {
    getCheckIfIsCustom,
    getFilteredLanguageName,
    getIcon,
    getSubmarketName,
    isCustomEvent
} from 'Components/Event/utils'
import { getLanguage } from 'Utils/getLanguage'
import { useI18LanguageContext } from 'State/LanguageState'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTrophy } from '@fortawesome/free-solid-svg-icons'

import './style.scss'

const EventIconAndLeague = ({
    event
}: {
    event: CreateEvent<EventInstrument, EventInstrument.Key, string>
}) => {
    const { lang } = useI18LanguageContext()
    const { BR, MX, US } = useSportTranslationsContext()
    const language = getLanguage({ lang, BR, MX, US })
    const marketTag = event.payload.details?.market.tag
    const marketValue =
        typeof event.payload.details?.market.value === 'string'
            ? event.payload.details?.market.value
            : ''
    const isSportEvent = marketTag === 'Sport'
    const name = getFilteredLanguageName(language, marketValue, isSportEvent)
    const checkIfIsCustom = getCheckIfIsCustom(name, event)
    const baseName = getFilteredLanguageName(US, marketValue, isSportEvent)
    const { t } = useTranslation()
    return (
        <div className="eventIconAndLeage">
            <div className="eventIconAndLeage__icon">
                {getIcon(baseName) || <FontAwesomeIcon icon={faTrophy} />}
                <p>
                    {isCustomEvent(checkIfIsCustom)
                        ? t(checkIfIsCustom)
                        : checkIfIsCustom}
                </p>
            </div>
            <p className="eventIconAndLeage__tournament">
                {getSubmarketName(event)}
            </p>
        </div>
    )
}

export default EventIconAndLeague
