import React from 'react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { CreateEvent } from '@daml/ledger'
import { useOddType } from 'State/OddTypeContext'
import {
    getOdds,
    returnOddValue,
    returnStakeAmount,
    outcomeSorterAndFilterByType
} from '../utils'
import EventOddBox from '../EventOddBoxes/EventOddBox'
import useExistsInGamblingListing from '../hooks/useExistsInGamblingListing'
import usePlaceBet from '../hooks/usePlaceBet'
import NoExistingBetsBoxes from '../EventOddBoxes/NoExistingBetsBoxes'
import BetWrapperContainer from './BetWrapperContainer'

const OverUnderSection = ({
    eventContract
}: {
    eventContract: CreateEvent<EventInstrument, EventInstrument.Key, string>
}) => {
    const {
        details: { outcomes },
        eventId: { label }
    } = eventContract.payload

    const { oddState } = useOddType()
    const existsInGamblingListingContracts = useExistsInGamblingListing(label)
    const { setBet } = usePlaceBet()
    const OverUnderOutcomes = outcomeSorterAndFilterByType({
        outcomes,
        typeOfOutcome: 'OverUnder'
    })

    if (!OverUnderOutcomes.length) {
        return (
            <NoExistingBetsBoxes parentBet="OverUnder" event={eventContract} />
        )
    }

    return (
        <BetWrapperContainer
            betWrapperTitle="Total"
            hasDiviser
            children={
                <>
                    {React.Children.toArray(
                        OverUnderOutcomes.map(outcome => {
                            const subtype = outcome.outcome.subtype
                            const typeValue = outcome.outcome.type_.value
                            const oddBack = returnOddValue(
                                oddState,
                                existsInGamblingListingContracts,
                                null,
                                'Back',
                                getOdds(outcome, oddState),
                                true,
                                outcome?.outcome
                            )
                            const oddLay = returnOddValue(
                                oddState,
                                existsInGamblingListingContracts,
                                null,
                                'Lay',
                                getOdds(outcome, oddState),
                                true,
                                outcome?.outcome
                            )
                            const stakeBack = returnStakeAmount(
                                existsInGamblingListingContracts,
                                null,
                                'Back',
                                true,
                                outcome?.outcome
                            )

                            const stakeLay = returnStakeAmount(
                                existsInGamblingListingContracts,
                                null,
                                'Lay',
                                true,
                                outcome?.outcome
                            )

                            const eventTitle = (
                                eventContract?.payload?.details
                                    ?.eventTitle as any
                            )?._kvs
                            const startDate =
                                eventContract.payload.details.startDate
                            const betTitle = `${subtype} ${typeValue}`
                            const isDisabled = !outcome ? 'disabled' : ''

                            const setBackBet = () =>
                                setBet(
                                    returnOddValue(
                                        oddState,
                                        existsInGamblingListingContracts,
                                        null,
                                        'Back',
                                        getOdds(outcome, oddState),
                                        true,
                                        outcome?.outcome
                                    )?.oddValueForBetSlip,
                                    'Back',
                                    outcome,
                                    eventContract.key,
                                    eventTitle,
                                    `${outcome.outcome.subtype} ${outcome.outcome.type_.value}`,
                                    startDate
                                )

                            const setLayBet = () =>
                                setBet(
                                    returnOddValue(
                                        oddState,
                                        existsInGamblingListingContracts,
                                        null,
                                        'Lay',
                                        getOdds(outcome, oddState),
                                        true,
                                        outcome?.outcome
                                    )?.oddValueForBetSlip,
                                    'Lay',
                                    outcome,
                                    eventContract.key,
                                    eventTitle,
                                    `${outcome.outcome.subtype} ${outcome.outcome.type_.value}`,
                                    startDate
                                )

                            return (
                                <EventOddBox
                                    betTitle={betTitle}
                                    oddBack={oddBack}
                                    oddLay={oddLay}
                                    stakeBack={stakeBack}
                                    stakeLay={stakeLay}
                                    outcome={outcome.outcome}
                                    isDisabled={isDisabled}
                                    setBackBet={setBackBet}
                                    setLayBet={setLayBet}
                                    oddState={oddState}
                                />
                            )
                        })
                    )}
                </>
            }
        />
    )
}

export default OverUnderSection
