import React from 'react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { CreateEvent } from '@daml/ledger'

import { useOddType } from 'State/OddTypeContext'
import {
    getOdds,
    returnOddValue,
    returnStakeAmount,
    outcomeSorterAndFilterByType
} from '../utils'
import EventOddBox from '../EventOddBoxes/EventOddBox'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import { useTranslation } from 'react-i18next'
import useExistsInGamblingListing from '../hooks/useExistsInGamblingListing'
import usePlaceBet from '../hooks/usePlaceBet'
import NoExistingBetsBoxes from '../EventOddBoxes/NoExistingBetsBoxes'
import BetWrapperContainer from './BetWrapperContainer'

const ThreewayHandicapSection = ({
    eventContract
}: {
    eventContract: CreateEvent<EventInstrument, EventInstrument.Key, string>
}) => {
    const {
        details: { outcomes },
        eventId: { label }
    } = eventContract.payload

    const { oddState } = useOddType()
    const existsInGamblingListingContracts = useExistsInGamblingListing(label)
    const { t } = useTranslation()
    const { setBet } = usePlaceBet()

    const threewayHandicapOutcomes = outcomeSorterAndFilterByType({
        outcomes,
        typeOfOutcome: 'ThreeWayHandicap'
    })

    const getParticipantName = (id: any) => {
        const name = eventContract?.payload?.details?.eventParticipants.filter(
            participant => participant?.id === id
        )[0]?.name
        return name ? descriptCamelCase(name) : 'participant'
    }

    if (!threewayHandicapOutcomes.length) {
        return (
            <NoExistingBetsBoxes
                event={eventContract}
                parentBet="ThreeWayHandicap"
            />
        )
    }

    return (
        <BetWrapperContainer
            hasDiviser
            betWrapperTitle="Spread"
            children={
                <>
                    {React.Children.toArray(
                        threewayHandicapOutcomes.map(outcome => {
                            const subtype = outcome?.outcome?.subtype
                            const typeValue = outcome?.outcome?.type_.value
                            const participantId =
                                outcome?.outcome?.participantId
                            const isDraw = subtype === 'Draw'
                            const participantName = isDraw
                                ? t('Draw')
                                : getParticipantName(participantId)
                            const betTitle = isDraw
                                ? ''
                                : typeof typeValue === 'string'
                                ? typeValue
                                : ''
                            const oddBack = returnOddValue(
                                oddState,
                                existsInGamblingListingContracts,
                                participantId,
                                'Back',
                                getOdds(outcome, oddState),
                                true,
                                outcome?.outcome
                            )
                            const oddLay = returnOddValue(
                                oddState,
                                existsInGamblingListingContracts,
                                participantId,
                                'Lay',
                                getOdds(outcome, oddState),
                                true,
                                outcome?.outcome
                            )
                            const stakeBack = returnStakeAmount(
                                existsInGamblingListingContracts,
                                participantId,
                                'Back',
                                true,
                                outcome?.outcome
                            )

                            const stakeLay = returnStakeAmount(
                                existsInGamblingListingContracts,
                                participantId,
                                'Lay',
                                true,
                                outcome?.outcome
                            )

                            const eventTitle = (
                                eventContract?.payload?.details
                                    ?.eventTitle as any
                            )?._kvs
                            const startDate =
                                eventContract.payload.details.startDate
                            const isDisabled = !outcome ? 'disabled' : ''

                            const setBackBet = () =>
                                setBet(
                                    returnOddValue(
                                        oddState,
                                        existsInGamblingListingContracts,
                                        outcome.outcome.participantId,
                                        'Back',
                                        getOdds(outcome, oddState),
                                        true,
                                        outcome?.outcome
                                    )?.oddValueForBetSlip,
                                    'Back',
                                    outcome,
                                    eventContract.key,
                                    eventTitle,
                                    `${outcome.outcome.subtype} ${outcome.outcome.type_.value}`,
                                    startDate
                                )

                            const setLayBet = () =>
                                setBet(
                                    returnOddValue(
                                        oddState,
                                        existsInGamblingListingContracts,
                                        outcome.outcome.participantId,
                                        'Lay',
                                        getOdds(outcome, oddState),
                                        true,
                                        outcome?.outcome
                                    )?.oddValueForBetSlip,
                                    'Lay',
                                    outcome,
                                    eventContract.key,
                                    eventTitle,
                                    `${outcome.outcome.subtype} ${outcome.outcome.type_.value}`,
                                    startDate
                                )

                            return (
                                <EventOddBox
                                    participantName={participantName}
                                    betTitle={betTitle}
                                    oddBack={oddBack}
                                    oddLay={oddLay}
                                    stakeBack={stakeBack}
                                    stakeLay={stakeLay}
                                    outcome={outcome.outcome}
                                    isDisabled={isDisabled}
                                    setBackBet={setBackBet}
                                    setLayBet={setLayBet}
                                    oddState={oddState}
                                />
                            )
                        })
                    )}
                </>
            }
        />
    )
}

export default ThreewayHandicapSection
