const BetWrapperContainer = ({
    children,
    betWrapperTitle,
    hasDiviser = false,
    isDisabled = false
}: {
    children: React.ReactNode
    betWrapperTitle: string
    hasDiviser?: boolean
    isDisabled?: boolean
}) => {
    return (
        <>
            {hasDiviser ? <hr className="betCard__divider" /> : null}
            <div className="betCard__betsWrapper">
                <div className="betCard__betOddBoxesItemContainer">
                    <div />
                    <p
                        className={`betCard__betsWrapperTitle ${
                            isDisabled
                                ? 'betCard__betsWrapperTitle--disabled'
                                : ''
                        }`}
                    >
                        {betWrapperTitle}
                    </p>
                </div>
                {children}
            </div>
        </>
    )
}

export default BetWrapperContainer
