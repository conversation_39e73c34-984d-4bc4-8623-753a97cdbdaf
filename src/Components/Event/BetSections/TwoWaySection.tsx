import {
    EventInstrument,
    OutcomeOdds
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { CreateEvent } from '@daml/ledger'

import { useOddType } from 'State/OddTypeContext'
import EventOddBox from '../EventOddBoxes/EventOddBox'
import useExistsInGamblingListing from '../hooks/useExistsInGamblingListing'
import usePlaceBet from '../hooks/usePlaceBet'
import BetWrapperContainer from './BetWrapperContainer'
import {
    outcomeSorterAndFilterByType,
    generateParticipantsRefactor,
    getSportCardProps
} from '../utils'

const TwoWaySection = ({
    eventContract
}: {
    eventContract: CreateEvent<EventInstrument, EventInstrument.Key, string>
}) => {
    const {
        details: { outcomes, eventParticipants },
        eventId: { label }
    } = eventContract.payload

    const { oddState } = useOddType()
    const existsInGamblingListingContracts = useExistsInGamblingListing(label)
    const { setBet } = usePlaceBet()
    //EVENT TITLE
    const { A: sideA, B: sideB } = generateParticipantsRefactor({
        eventParticipants
    })
    const titleSideA = sideA.join('/')
    const titleSideB = sideB.join(' / ')

    //PARTICIPANT ID
    const sideAParticipantID = eventParticipants.filter(p => p.order === '1')[0]
        ?.id
    const sideBParticipantID = eventParticipants.filter(p => p.order === '2')[0]
        ?.id

    //OUTCOMES
    const twowayOutcomes = outcomeSorterAndFilterByType({
        outcomes,
        typeOfOutcome: 'TwoWay'
    })
    const sideAOutcome = twowayOutcomes.filter(
        data => data.outcome.participantId === sideAParticipantID
    )[0]

    const sideBOutcome = twowayOutcomes.filter(
        data => data.outcome.participantId === sideBParticipantID
    )[0]

    const sideAProps = getSportCardProps({
        participantId: sideAParticipantID,
        outcomes: twowayOutcomes,
        oddState,
        existsInGamblingListingContracts,
        description: eventContract,
        sideOutcome: sideAOutcome?.outcome
    })

    const sideBProps = getSportCardProps({
        participantId: sideBParticipantID,
        outcomes: twowayOutcomes,
        oddState,
        existsInGamblingListingContracts,
        description: eventContract,
        sideOutcome: sideBOutcome?.outcome
    })

    const eventCardBoxesProps = (
        props: any,
        participantName: string,
        outcome: OutcomeOdds
    ) => ({
        participantName,
        betTitle: '',
        oddBack: {
            oddForPresentation: props.oddBack,
            oddValueForBetSlip: props.backOddValueForBetSlip
        },
        oddLay: {
            oddForPresentation: props.oddLay,
            oddValueForBetSlip: props.layOddValueForBetSlip
        },
        stakeBack: props.stakeBack,
        stakeLay: props.stakeLay,
        outcome: props.outcomes,
        isDisabled: !outcome ? 'disabled' : '',
        setBackBet: () =>
            setBet(
                props.backOddValueForBetSlip,
                'Back',
                outcome,
                eventContract.key,
                (eventContract.payload.details.eventTitle as any)?._kvs,
                participantName,
                eventContract.payload.details.startDate
            ),
        setLayBet: () =>
            setBet(
                props.layOddValueForBetSlip,
                'Lay',
                outcome,
                eventContract.key,
                (eventContract.payload.details.eventTitle as any)?._kvs,
                participantName,
                eventContract.payload.details.startDate
            ),
        oddState: oddState
    })

    if (!twowayOutcomes.length) {
        return null
    }

    return (
        <BetWrapperContainer
            betWrapperTitle="To Win"
            children={
                <>
                    <EventOddBox
                        {...eventCardBoxesProps(
                            sideAProps,
                            titleSideA,
                            sideAOutcome
                        )}
                    />
                    <EventOddBox
                        {...eventCardBoxesProps(
                            sideBProps,
                            titleSideB,
                            sideBOutcome
                        )}
                    />
                </>
            }
        />
    )
}

export default TwoWaySection
