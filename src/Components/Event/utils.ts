import { useRouteMatch } from 'react-router-dom'
import { sports } from '../../Constants/sports'
import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { checkIfNonSportEvent } from 'Utils/checkIfNonSportEvent'
import { langArray } from 'State/SportTranslationsContext'
import { GamblingListing } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Listing/Model'
import { convertDecimalNumberToFraction } from 'Components/MyBets/utils'
import { MakeOfferOddValues } from 'Constants/MakeOfferOddValues'
import numeral from 'numeral'
import {
    OutcomeType,
    Participant
} from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'
import { OutcomeOdds } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'

// Helper function to get SportCardElement props
export function getSportCardProps({
    participantId,
    outcomes,
    oddState,
    existsInGamblingListingContracts,
    description,
    sideOutcome
}: {
    participantId: string | null
    outcomes: OutcomeOdds[]
    oddState: string
    existsInGamblingListingContracts: CreateEvent<
        GamblingListing,
        GamblingListing.Key,
        string
    >[]
    description: CreateEvent<EventInstrument, EventInstrument.Key, string>
    sideOutcome: OutcomeOdds['outcome']
}) {
    const odds = getOddsFromAGivenSide(participantId, outcomes, oddState)

    return {
        participantId,
        description,
        oddState,
        oddBack: returnOddValue(
            oddState,
            existsInGamblingListingContracts,
            participantId,
            'Back',
            odds,
            false,
            sideOutcome
        )?.oddForPresentation,
        backOddValueForBetSlip: returnOddValue(
            oddState,
            existsInGamblingListingContracts,
            participantId,
            'Back',
            odds,
            false,
            sideOutcome
        )?.oddValueForBetSlip,
        stakeBack: returnStakeAmount(
            existsInGamblingListingContracts,
            participantId,
            'Back',
            false,
            sideOutcome
        ),
        oddLay: returnOddValue(
            oddState,
            existsInGamblingListingContracts,
            participantId,
            'Lay',
            odds,
            false,
            sideOutcome
        )?.oddForPresentation,
        layOddValueForBetSlip: returnOddValue(
            oddState,
            existsInGamblingListingContracts,
            participantId,
            'Lay',
            odds,
            false,
            sideOutcome
        )?.oddValueForBetSlip,
        stakeLay: returnStakeAmount(
            existsInGamblingListingContracts,
            participantId,
            'Lay',
            false,
            sideOutcome
        ),
        outcomes
    }
}

export const baseStakeForBetSlip = ''

const minUnderDogOdd = 99
export const addPlusToUnderdog = (odd: string | Number, oddState: string) =>
    oddState === 'Moneyline' && Number(odd) > minUnderDogOdd ? ' +' : null

export const prepareStake = (stake: string | number) =>
    stake === 0 || stake === '0.0' ? '' : stake

export const isDisplayDraw = (arr: any) =>
    arr?.payload.details.outcomes.some(
        (data: any) => data.outcome.type_.tag === 'ThreeWay'
    )

//generates object with participants array
export const generateParticipants = (eventParticipants: any[]) =>
    eventParticipants.reduce(
        (coops: any, participant: any) => {
            const { A, B } = coops
            if (participant.order % 2 === 0) {
                return { ...coops, B: [...B, participant.name] }
            } else {
                return { ...coops, A: [...A, participant.name] }
            }
        },
        { A: [], B: [] }
    )

//Get outcome for a given participant ID
export const getOutcomeByParticipantId = (
    participantId: string | null,
    arr: any[] // description?.payload.details.outcomes
) => arr.find((element: any) => participantId === element.outcome.participantId)

//Get odds for a given participant ID
export const getOddsFromAGivenSide = (
    id: string | null,
    arr: any[],
    oddState: string
) =>
    getOutcomeByParticipantId(id, arr)
        ?.odds.map?.entriesArray()
        .find((element: any) => element[0].tag === oddState)

export const getOddValueForBetSlip = (valueOddSide: any) =>
    valueOddSide && valueOddSide[0]

export const presentFormatedOddValue = (valueOddSide: any, oddState: string) =>
    oddState === 'Fractional'
        ? valueOddSide && convertDecimalNumberToFraction(valueOddSide[0].value)
        : valueOddSide && valueOddSide[0].value

export const payloadOddsOverUnder = (
    participantId: string | null,
    outcome: any,
    existsInGamblingListingContracts: CreateEvent<
        GamblingListing,
        GamblingListing.Key,
        string
    >[]
) => {
    return existsInGamblingListingContracts.filter(a => {
        //NOTE: Problem with DAML.js type conversion on decimal/Numeric
        const prepareTypeComparission = (type: string | {}) =>
            Number(type).toFixed(1)
        return (
            a?.payload.outcome.participantId === participantId &&
            a?.payload?.outcome?.type_?.tag === outcome?.type_?.tag &&
            prepareTypeComparission(a?.payload?.outcome?.type_?.value) ===
                prepareTypeComparission(outcome?.type_?.value) &&
            a?.payload?.outcome?.subtype === outcome?.subtype &&
            a?.payload?.outcome?.order === outcome?.order &&
            a?.payload?.outcome?.participantOrder === outcome?.participantOrder
        )
    })[0]?.payload
}

export const payloadOdds = (
    participantId: string | null,
    existsInGamblingListingContracts: any[],
    outcome: any
) => {
    return existsInGamblingListingContracts.filter(
        a =>
            a?.payload?.outcome?.type_?.tag === outcome?.type_?.tag &&
            a?.payload.outcome.participantId === participantId
    )[0]?.payload
}

export const shouldDisplayMakeOffer = (
    event: CreateEvent<EventInstrument, EventInstrument.Key, string>,
    oddState: string,
    existsInGamblingListingContracts: any[]
) => {
    //Logica de outcomes returnOddValue Back e returnOddValue Lay para todas as odds e verificar
    // se alguma esta na lista de odds a ser override
    const outcomes = event.payload.details.outcomes
    const mainBets = outcomes.filter(
        outcome =>
            outcome.outcome.type_.tag !== 'OverUnder' &&
            outcome.outcome.type_.tag !== 'ThreeWayHandicap'
    )
    const overUnder = outcomes.filter(
        outcome => outcome.outcome.type_.tag === 'OverUnder'
    )
    const threeWayHandicap = outcomes.filter(
        outcome => outcome.outcome.type_.tag === 'ThreeWayHandicap'
    )

    let mainBetsOutcomes = mainBets
        .map(outcome => {
            const participantId = outcome?.outcome?.participantId
            const oddBack = returnOddValue(
                oddState,
                existsInGamblingListingContracts,
                participantId,
                'Back',
                getOddsFromAGivenSide(participantId, outcomes, oddState),
                false,
                outcome?.outcome
            )?.oddForPresentation
            const oddLay = returnOddValue(
                oddState,
                existsInGamblingListingContracts,
                participantId,
                'Lay',
                getOddsFromAGivenSide(participantId, outcomes, oddState),
                false,
                outcome?.outcome
            )?.oddForPresentation
            return [oddBack, oddLay]
        })
        .flat()

    let threeWayHandicapOdds = threeWayHandicap
        .map(outcome => {
            const participantId = outcome?.outcome?.participantId
            const oddBack = returnOddValue(
                oddState,
                existsInGamblingListingContracts,
                participantId,
                'Back',
                getOdds(outcome, oddState),
                true,
                outcome?.outcome
            )?.oddForPresentation
            const oddLay = returnOddValue(
                oddState,
                existsInGamblingListingContracts,
                participantId,
                'Lay',
                getOdds(outcome, oddState),
                true,
                outcome?.outcome
            )?.oddForPresentation
            return [oddBack, oddLay]
        })
        .flat()

    let overUnderOdds = overUnder
        .map(outcome => {
            const oddBack = returnOddValue(
                oddState,
                existsInGamblingListingContracts,
                null,
                'Back',
                getOdds(outcome, oddState),
                true,
                outcome?.outcome
            )?.oddForPresentation
            const oddLay = returnOddValue(
                oddState,
                existsInGamblingListingContracts,
                null,
                'Lay',
                getOdds(outcome, oddState),
                true,
                outcome?.outcome
            )?.oddForPresentation
            return [oddLay, oddBack]
        })
        .flat()
    let oddArray = [
        ...mainBetsOutcomes,
        ...overUnderOdds,
        ...threeWayHandicapOdds
    ]
    const hasDisallowed = oddArray.some(odd => MakeOfferOddValues.has(odd))
    return hasDisallowed
}

export const returnOddValue = (
    oddState: string,
    existsInGamblingListingContracts: any,
    participantId: string | null,
    side: 'Back' | 'Lay',
    defaultOdds: any,
    isPropBet: boolean,
    outcome: any
) => {
    const oddValues = isPropBet
        ? payloadOddsOverUnder(
              participantId,
              outcome,
              existsInGamblingListingContracts
          )
        : payloadOdds(participantId, existsInGamblingListingContracts, outcome)
    if (!oddValues) {
        return {
            oddForPresentation: presentFormatedOddValue(defaultOdds, oddState),
            oddValueForBetSlip: getOddValueForBetSlip(defaultOdds)
        }
    }
    if (side === 'Back') {
        const val = oddValues?.backOdds[0]?._1?.map
            ?.entriesArray()
            .find((element: any) => element[0].tag === oddState)
        return {
            oddForPresentation: presentFormatedOddValue(val, oddState),
            oddValueForBetSlip: getOddValueForBetSlip(val)
        }
    }
    if (side === 'Lay') {
        const val = oddValues?.layOdds[0]?._1?.map
            ?.entriesArray()
            .find((element: any) => element[0].tag === oddState)
        return {
            oddForPresentation: presentFormatedOddValue(val, oddState),
            oddValueForBetSlip: getOddValueForBetSlip(val)
        }
    }
    return {
        oddForPresentation: presentFormatedOddValue(defaultOdds, oddState),
        oddValueForBetSlip: getOddValueForBetSlip(defaultOdds)
    }
}

export const returnStakeAmount = (
    existsInGamblingListingContracts: any[],
    participantId: string | null,
    side: 'Back' | 'Lay',
    isOverUnder?: boolean,
    outcome?: any
) => {
    let defaultStakeValue = ''
    const oddValues = isOverUnder
        ? payloadOddsOverUnder(
              participantId,
              outcome,
              existsInGamblingListingContracts
          )
        : payloadOdds(participantId, existsInGamblingListingContracts, outcome)
    if (!oddValues) {
        return defaultStakeValue
    }
    if (side === 'Back') {
        return oddValues?.backOdds[0]?._2
    }
    if (side === 'Lay') {
        return oddValues?.layOdds[0]?._2
    }
    return defaultStakeValue
}

export const formatDisplayStake = (stake: string) =>
    stake === '' || stake === '0.0' || stake === '-'
        ? '-'
        : numeral(Number(stake)).format('$0,0.00')

export const getOdds = (
    outcome: {
        odds: { map: { entriesArray: () => any[] } }
    },
    oddState: string
) =>
    outcome.odds.map
        ?.entriesArray()
        .find((element: any) => element[0].tag === oddState)

export const outcomeSorterAndFilterByType = ({
    outcomes,
    typeOfOutcome
}: {
    outcomes: OutcomeOdds[]
    typeOfOutcome: OutcomeType['tag']
}) => {
    return outcomes
        .filter(outcome => outcome.outcome.type_.tag === typeOfOutcome)
        .sort((a, b) =>
            Number(a?.outcome?.order) > Number(b?.outcome?.order)
                ? 1
                : Number(b?.outcome?.order) > Number(a?.outcome?.order)
                ? -1
                : 0
        )
}

export const generateParticipantsRefactor = ({
    eventParticipants
}: {
    eventParticipants: Participant[]
}): { A: Array<String>; B: Array<String> } =>
    eventParticipants.reduce(
        (coops: { A: Array<String>; B: Array<String> }, participant) => {
            const { A, B } = coops
            if (Number(participant.order) % 2 === 0) {
                return { ...coops, B: [...B, participant.name] }
            } else {
                return { ...coops, A: [...A, participant.name] }
            }
        },
        { A: [], B: [] }
    )

// Utility function to match the relevant routes
export function useRouteMatchers() {
    const matchMainPage = useRouteMatch('/')
    const matchMarketPage = useRouteMatch('/events/:id')
    const matchMarketGeoPage = useRouteMatch('/events/:id/:geo')
    const matchLeagePage = useRouteMatch('/events/:id/:geo/:league')

    return (
        matchMainPage?.isExact ||
        matchMarketPage?.isExact ||
        matchMarketGeoPage?.isExact ||
        matchLeagePage?.isExact
    )
}

// Utility function to get the filtered language name
export function getFilteredLanguageName(
    langArray: langArray[],
    marketValue: string,
    isSportEvent: boolean
) {
    return langArray?.filter(a => isSportEvent && a[marketValue])[0]
}

// Utility function to check if event is custom
export function getCheckIfIsCustom(
    name: langArray,
    description: CreateEvent<EventInstrument, EventInstrument.Key, string>
) {
    const marketDetails = description.payload.details?.market
    const nonSportEventCheck = checkIfNonSportEvent(marketDetails)

    return isNaN(Number(nonSportEventCheck))
        ? nonSportEventCheck
        : name && Object.values(name)[0]
}

// Utility function to get the correct icon
export function getIcon(baseName: any) {
    return baseName && sports[Object.values(baseName as string)[0]]?.icon
}

// Utility function to check if event is custom (Entertainment or Politics)
export function isCustomEvent(eventType: string) {
    return eventType === 'Entertainment' || eventType === 'Politics'
}

// Utility function to get the submarket name
export function getSubmarketName(
    description: CreateEvent<EventInstrument, EventInstrument.Key, string>
) {
    const submarkets = description.payload.details?.submarkets
    if (!submarkets?.length) return ''

    return submarkets.length > 1 ? submarkets[1].value : submarkets[0].value
}
