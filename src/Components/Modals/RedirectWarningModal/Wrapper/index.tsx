import React from 'react'
import { useHistory } from 'react-router-dom'
import RedirectWarningModal from 'Components/Modals/RedirectWarningModal'
import { MODAL_KEY } from 'Constants/redirectModalKey'
import { cache } from 'Utils/cache'
import { isLocalDev } from 'config'
import { useAuth0 } from '@auth0/auth0-react'
import { loginParams } from 'Constants/Auth0Constants'

export default function WarningModalWrapper({
    closeModal,
    modalIsOpen
}: {
    closeModal: () => void
    modalIsOpen: boolean
}) {
    const { push } = useHistory()
    const { loginWithRedirect } = useAuth0()

    const handleLogin = async () => {
        await loginWithRedirect(loginParams)
    }

    // CHANGE HERE AUTH0
    const checkLogin = () => (isLocalDev ? push('/signin') : handleLogin())
    const { save, load } = cache({ permanent: true })
    const [hideModal, setHideModal] = React.useState(() =>
        load(MODAL_KEY) ? true : false
    )

    React.useEffect(() => {
        if (modalIsOpen && hideModal) {
            checkLogin()
            closeModal()
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [hideModal, modalIsOpen])

    const onSetHide = () => {
        setHideModal(true)
        save(MODAL_KEY, 'true')
    }

    const RenderModal = () =>
        !modalIsOpen ? null : (
            <RedirectWarningModal
                hideModal={hideModal}
                setHideModal={onSetHide}
                modalIsOpen={modalIsOpen}
                closeModal={closeModal}
                loginRedirect={checkLogin}
            />
        )
    return hideModal ? null : <RenderModal />
}
