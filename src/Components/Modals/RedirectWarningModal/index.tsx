import React from 'react'
import Modal from 'react-modal'
import { useTranslation } from 'react-i18next'

import '../style.scss'

export default function RedirectWarningModal({
    modalIsOpen,
    closeModal,
    hideModal,
    setHideModal,
    loginRedirect
}: {
    modalIsOpen: boolean
    closeModal: () => void
    hideModal: boolean
    setHideModal: () => void
    loginRedirect: () => void
}) {
    const { t } = useTranslation()
    const [count, setCount] = React.useState(5)

    React.useEffect(() => {
        const TimerInt = setInterval(() => {
            setCount(count => count - 1)
        }, 1000)
        if (count <= 0) {
            closeModal()
            loginRedirect()
        }
        return () => {
            clearInterval(TimerInt)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [count])

    function handleClose() {
        closeModal()
        setCount(5)
        loginRedirect()
    }

    function handleChange() {
        setHideModal()
        loginRedirect()
    }

    return (
        <Modal
            isOpen={modalIsOpen}
            className="warningModal"
            overlayClassName="warningOverlay"
            shouldCloseOnOverlayClick
            ariaHideApp={false}
            onRequestClose={closeModal}
            onAfterClose={() => setCount(5)}
            contentLabel="redirect modal"
        >
            <h3>
                {t('RedirectModalHeader')} {count}
            </h3>
            <button className="btn btn__primary" onClick={() => handleClose()}>
                {t('RedirectModalButton')}
            </button>
            <div className="warningModal__check">
                <p>{t('RedirectModalWarning')}</p>
                <input
                    type="checkbox"
                    checked={hideModal}
                    onChange={() => handleChange()}
                />
            </div>
        </Modal>
    )
}
