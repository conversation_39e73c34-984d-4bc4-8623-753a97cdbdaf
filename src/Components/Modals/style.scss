@import '../../Styles/colors';

.warningOverlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 3;
}

.warningModal {
    background-color: $white;
    color: $black;
    accent-color: $purple;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 50px;
    border-radius: 1.6rem;
    position: absolute;
    max-width: 500px;
    width: 90%;
    max-height: 750px;
    height: fit-content;
    box-sizing: border-box;
    position: absolute;
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 5;
    word-wrap: break-word;
    word-break: break-word;
    overflow: scroll;

    &__body {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 20px;
        padding: 2rem 0px;
        width: 100%;
    }

    &__check {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
    }

    &__actions {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        width: 100%;
    }

    button {
        width: 40%;
    }
}
