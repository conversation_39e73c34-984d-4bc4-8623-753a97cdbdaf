import React from 'react'
import Modal from 'react-modal'

import '../style.scss'

interface IBasicModal {
    body: React.ReactNode
    isOpenModal: boolean;
    footerBody: React.ReactNode
    handleClose: () => void,
    shouldCloseOnOverlayClickProp?: boolean
}

export default function BasicModal({ body, isOpenModal, footerBody, handleClose, shouldCloseOnOverlayClickProp = true }: IBasicModal) {
    return (
        <Modal
            isOpen={isOpenModal}
            className="warningModal"
            overlayClassName="warningOverlay"
            shouldCloseOnOverlayClick={shouldCloseOnOverlayClickProp}
            ariaHideApp={false}
            onRequestClose={handleClose}
            contentLabel="confirm modal"
        >
            <div className="warningModal__body">
                {body}
            </div>

            <div className='warningModal__actions'>
                {footerBody}
            </div>
        </Modal>
    )
}
