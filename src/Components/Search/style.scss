@import '../../Styles/colors';

.search{
    color: $white;
    position: relative;
    margin-right: 15px;
    input[type="search"]::-webkit-search-cancel-button {
      display: none;
    }

    //@media (max-width: 1014px) {
    //  margin-top: -32px;
    //  margin-right: 0;
    //}
    button{
    position: absolute;
    top: 10px;
    right: 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    @media (max-width: 1014px) {
      display: none;
    }
    svg{
      font-size: 18px;
      color: $white;
    }
  }
    .mobile_icon{
      display: none;
      @media (max-width: 1014px) {
        display: block;
        font-size: 18px;
        color: $white;
      }
    }

    input[type=search] {
    padding: 10px;
    font-size: 16px;
    border: none;
    float: left;
    background: $darkGrey;
    color: $white;
    width: 100%;
    border-radius: 10px;
      &:focus{
        outline: none;
        box-shadow:0 0 8px #aeaeae;
      }
      @media (max-width: 1014px) {
        display: none;
      }
  }
}

.searchField{
  position: fixed;
  width: 100%;
  background-color: $black;
  height: 100px;
  top: 0;
  left: 0;
  z-index: 300;
  display: flex;
  justify-content: center;
  align-items: center;
  input[type="search"]::-webkit-search-cancel-button {
    display: none;
  }
  input[type=search] {
    width: 70%;
    padding: 10px;
    font-size: 16px;
    border: none;
    float: left;
    background: $darkGrey;
    color: $white;
    border-radius: 10px;
    margin-right: 20px;
    &:focus{
      outline: none;
      box-shadow:0 0 8px #aeaeae;
    }
  }
  &__button{
    margin-left: -50px;
    margin-right: 30px;
    background: transparent;
    border: none;
    color: white;
    cursor: pointer;
  }
}