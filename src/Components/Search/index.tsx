import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faSearch, faUndoAlt } from '@fortawesome/free-solid-svg-icons'

import './style.scss'

export default function Search() {
    const [showSearch, setShowSearch] = useState(false)
    return (
        <>
            {showSearch && (
                <div className="searchField">
                    <input type="search" placeholder="Search" name="search" />
                    <button
                        type="submit"
                        onClick={() => console.log('submit search')}
                        className="searchField__button"
                    >
                        <FontAwesomeIcon icon={faSearch} />
                    </button>
                    <button
                        className="btn btn__secondary"
                        onClick={() => setShowSearch(false)}
                    >
                        Back <FontAwesomeIcon icon={faUndoAlt} />
                    </button>
                </div>
            )}
            <form className="search">
                <input type="search" placeholder="Search" name="search" />
                <button
                    type="submit"
                    onClick={() => console.log('submit search')}
                >
                    <FontAwesomeIcon icon={faSearch} />
                </button>
                <FontAwesomeIcon
                    onClick={() => setShowSearch(true)}
                    icon={faSearch}
                    className="mobile_icon"
                />
            </form>
        </>
    )
}
