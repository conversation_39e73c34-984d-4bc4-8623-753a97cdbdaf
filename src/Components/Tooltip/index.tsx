import ReactTooltip from 'react-tooltip'
import './style.scss'

/**
 *
 * @param topic type string - the topic is the id of the tooltip please make sure it matches the  data-for property on the component you want to render the tooltip
 * @returns a tooltip with the data set on the partent component in data-tip property
 */
const TooltipComponent = ({ topic }: { topic: string }) => {
    return (
        <ReactTooltip
            id={topic}
            place="top"
            type="light"
            effect="solid"
            clickable
            class="newTooltip"
            border
            borderColor="#dee2e6"
        />
    )
}

export default TooltipComponent
