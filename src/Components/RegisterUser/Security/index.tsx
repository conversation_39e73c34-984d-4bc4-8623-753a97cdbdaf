import React from 'react'
import { Formik } from 'formik'
import * as Yup from 'yup'

import InputForm from '../../../Components/Inputs'
import ErrorMessage from '../../../Components/RegisterUser/ErrorMessage'
import ArrowLeftIcon from '../../../Assets/arrrowLeft'

import { IPersonalDetails } from '../../../Containers/RegisterUser'

const SecurityDetailsSchema = Yup.object({
    promoCode: Yup.string(),
    ageConfirm: Yup.boolean(),
    emailList: Yup.boolean()
})

const SecurityDetails = ({
    formData,
    handleNextStep,
    handlePreviousStep
}: {
    formData: IPersonalDetails
    handleNextStep: (data: IPersonalDetails, boolean: boolean) => void
    handlePreviousStep: (data: IPersonalDetails) => void
}) => {
    return (
        <Formik
            initialValues={formData}
            onSubmit={values => {
                handleNextStep(values, true)
            }}
            validationSchema={SecurityDetailsSchema}
        >
            {formik => {
                const { value: emailValue } = formik.getFieldProps('emailList')
                const { value: ageConfirm } = formik.getFieldProps('ageConfirm')

                return (
                    <form onSubmit={formik.handleSubmit}>
                        <InputForm
                            id="promoCode"
                            placeholder="Have a promo code?"
                            type="text"
                            {...formik.getFieldProps('promoCode')}
                        />
                        {formik.touched.promoCode && formik.errors.promoCode ? (
                            <ErrorMessage message={formik.errors.promoCode} />
                        ) : null}
                        <div className="register__checkboxContainer">
                            <InputForm
                                id="emailList"
                                placeholder=""
                                type="checkbox"
                                checked={emailValue}
                                {...formik.getFieldProps('emailList')}
                            />
                            <label>
                                Yes, I would like to recieve the occasional
                                email and offer from SportsMarket. We won’t
                                bombard you and the preferences can be changed
                                any time in your account.
                            </label>
                        </div>
                        <div className="register__checkboxContainer">
                            <InputForm
                                id="ageConfirm"
                                placeholder=""
                                type="checkbox"
                                checked={ageConfirm}
                                {...formik.getFieldProps('ageConfirm')}
                            />
                            <label>
                                I confirm that I am at least 18 years of age and
                                that I accept the terms and conditions.
                                (incorporating the privacy policy) and Age
                                Verification Policy as published on the site.
                            </label>
                        </div>
                        <div className="register__btnContainer">
                            <button
                                className="registerbtn__finalStep"
                                disabled={!ageConfirm}
                                type="submit"
                            >
                                Join now
                            </button>
                            <button
                                className="registerbtn__previousStep"
                                onClick={() =>
                                    handlePreviousStep(formik.values)
                                }
                            >
                                <ArrowLeftIcon />
                                Go Back
                            </button>
                        </div>
                    </form>
                )
            }}
        </Formik>
    )
}

export default SecurityDetails
