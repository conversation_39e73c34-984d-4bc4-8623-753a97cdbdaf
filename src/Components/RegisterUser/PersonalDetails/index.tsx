import React from 'react'
import { Formik } from 'formik'
import * as Yup from 'yup'

import InputForm from '../../../Components/Inputs'
import ErrorMessage from '../../../Components/RegisterUser/ErrorMessage'
import PeopleIcon from '../../../Assets/InputIcons/people'
import EmailIcon from '../../../Assets/InputIcons/email'

import { IPersonalDetails } from '../../../Containers/RegisterUser'

const PersonalDetailsSchema = Yup.object({
    firstName: Yup.string().required('Required field'),
    lastName: Yup.string().required('Required field'),
    emailAddress: Yup.string().email().required('Required field')
})

const PersonalDetails = ({
    formData,
    handleNextStep
}: {
    formData: IPersonalDetails
    handleNextStep: (data: IPersonalDetails) => void
}) => {
    return (
        <Formik
            initialValues={formData}
            onSubmit={(values: IPersonalDetails) => {
                handleNextStep(values)
            }}
            validationSchema={PersonalDetailsSchema}
        >
            {formik => (
                <form onSubmit={formik.handleSubmit}>
                    <InputForm
                        id="firstName"
                        placeholder="First Name"
                        type="text"
                        icon={<PeopleIcon />}
                        {...formik.getFieldProps('firstName')}
                    />
                    {formik.touched.firstName && formik.errors.firstName ? (
                        <ErrorMessage message={formik.errors.firstName} />
                    ) : null}
                    <InputForm
                        id="lastName"
                        placeholder="Last Name"
                        type="text"
                        icon={<PeopleIcon />}
                        {...formik.getFieldProps('lastName')}
                    />
                    {formik.touched.lastName && formik.errors.lastName ? (
                        <ErrorMessage message={formik.errors.lastName} />
                    ) : null}
                    <InputForm
                        id="emailAddress"
                        placeholder="Email Address"
                        type="email"
                        icon={<EmailIcon />}
                        {...formik.getFieldProps('emailAddress')}
                    />
                    {formik.touched.emailAddress &&
                    formik.errors.emailAddress ? (
                        <ErrorMessage message={formik.errors.emailAddress} />
                    ) : null}
                    <button className="registerbtn__nextStep" type="submit">
                        next step
                    </button>
                </form>
            )}
        </Formik>
    )
}

export default PersonalDetails
