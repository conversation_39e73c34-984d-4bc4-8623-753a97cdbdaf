import React from 'react'
import { Formik } from 'formik'
import * as Yup from 'yup'
import DatePicker from 'react-datepicker'

import InputForm from '../../../Components/Inputs'
import ArrowLeftIcon from '../../../Assets/arrrowLeft'
import ErrorMessage from '../../../Components/RegisterUser/ErrorMessage'
import TelephoneIcon from '../../../Assets/InputIcons/telephone'
import { IPersonalDetails } from '../../../Containers/RegisterUser'

import { countriesAllowed } from '../../../Constants/countries'
import { currencyList } from '../../../Constants/currencies'

import 'react-datepicker/dist/react-datepicker.css'

const rePhoneNumber =
    /^(\+?\d{0,4})?\s?-?\s?(\(?\d{3}\)?)\s?-?\s?(\(?\d{3}\)?)\s?-?\s?(\(?\d{4}\)?)?$/

const AccountDetailsSchema = Yup.object({
    country: Yup.string().min(2).required('Required field'),
    postcode: Yup.string().min(2).required('Required field'),
    dateOfBirth: Yup.date().required('Required field'),
    phoneNumber: Yup.string()
        .matches(rePhoneNumber, 'Phone number is not valid')
        .required('Required field'),
    currency: Yup.string().required('Required field')
})

const AccountDetails = ({
    formData,
    handleNextStep,
    handlePreviousStep
}: {
    formData: IPersonalDetails
    handleNextStep: (data: IPersonalDetails) => void
    handlePreviousStep: (data: IPersonalDetails) => void
}) => {
    return (
        <Formik
            initialValues={formData}
            onSubmit={values => {
                handleNextStep(values)
            }}
            validationSchema={AccountDetailsSchema}
        >
            {formik => (
                <form onSubmit={formik.handleSubmit}>
                    {/* <InputForm
                        id="country"
                        placeholder="Your country of residence"
                        type="select"
                        optionsComplex={countries}
                        {...formik.getFieldProps('country')}
                    />
                    {formik.touched.country && formik.errors.country ? (
                        <ErrorMessage message={formik.errors.country} />
                    ) : null} */}
                    <InputForm
                        id="postcode"
                        placeholder="Postcode"
                        type="text"
                        {...formik.getFieldProps('postcode')}
                    />
                    {formik.touched.postcode && formik.errors.postcode ? (
                        <ErrorMessage message={formik.errors.postcode} />
                    ) : null}
                    <DatePicker
                        className="register__input"
                        dateFormat="MM-dd-yyyy"
                        name="dateOfBirth"
                        onChange={date =>
                            formik.setFieldValue('dateOfBirth', date)
                        }
                        selected={formik.values.dateOfBirth}
                        placeholderText="Please insert your birth date (MM-DD-YYYY)"
                    />
                    {formik.touched.dateOfBirth && formik.errors.dateOfBirth ? (
                        <ErrorMessage message={formik.errors.dateOfBirth} />
                    ) : null}
                    <InputForm
                        id="phoneNumber"
                        placeholder="Mobile Phone"
                        type="text"
                        icon={<TelephoneIcon />}
                        {...formik.getFieldProps('phoneNumber')}
                    />
                    {formik.touched.phoneNumber && formik.errors.phoneNumber ? (
                        <ErrorMessage message={formik.errors.phoneNumber} />
                    ) : null}
                    {/* <InputForm
                        id="currency"
                        placeholder="Select a Currency"
                        type="select"
                        optionsComplex={currencyList}
                        {...formik.getFieldProps('currency')}
                    />
                    {formik.touched.currency && formik.errors.currency ? (
                        <ErrorMessage message={formik.errors.currency} />
                    ) : null} */}
                    <div className="register__btnContainer">
                        <button className="registerbtn__nextStep" type="submit">
                            next step
                        </button>
                        <button
                            className="registerbtn__previousStep"
                            onClick={() => handlePreviousStep(formik.values)}
                        >
                            <ArrowLeftIcon />
                            Go Back
                        </button>
                    </div>
                </form>
            )}
        </Formik>
    )
}

export default AccountDetails
