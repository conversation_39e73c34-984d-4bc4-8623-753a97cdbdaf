import React from 'react'
import { FieldArray, useFormikContext, Field } from 'formik'
import { Participant } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'
import {
    TFormDataUpdateOdds,
    getLabelForForm
} from '../../../../Containers/Admin/OddsUpdate/utils'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'

const OddsUpdateFormFields = ({
    eventParticipants,
    minOdd,
    maxOdd,
    handleClick
}: {
    eventParticipants: Participant[]
    minOdd: string
    maxOdd: string
    handleClick: () => void
}) => {
    const { values, errors, validateForm, isValid } =
        useFormikContext<TFormDataUpdateOdds>()
    const { outcomeOdds } = values

    const validateCallBack = React.useCallback(() => {
        validateForm()
    }, [validateForm])

    React.useEffect(() => {
        validateCallBack()
    }, [validateCallBack])

    const isTwoWay = outcomeOdds.some(
        ({ outcome }) => outcome.type_.tag === 'TwoWay'
    )

    return (
        <>
            <FieldArray name="outcomeOdds">
                {() => (
                    <div
                        className={
                            isTwoWay ? 'formGrid--twoWay' : 'formGrid--threeWay'
                        }
                    >
                        {outcomeOdds.length > 0
                            ? React.Children.toArray(
                                  outcomeOdds.map(({ outcome }, index) => (
                                      <div>
                                          <strong>
                                              {getLabelForForm(
                                                  outcome,
                                                  eventParticipants
                                              )}
                                          </strong>
                                          <p>
                                              {descriptCamelCase(
                                                  outcome.type_.tag
                                              )}{' '}
                                              {typeof outcome.type_.value ===
                                              'string'
                                                  ? `${outcome.type_.value}`
                                                  : ''}
                                          </p>
                                          <Field
                                              name={`outcomeOdds.${index}.odd.value`}
                                              className="register__input"
                                          />
                                      </div>
                                  ))
                              )
                            : null}
                    </div>
                )}
            </FieldArray>
            {errors?.outcomeOdds && errors.outcomeOdds.length > 0 ? (
                <>
                    <p>
                        Please verify the inputed odds, some of them might have
                        an invalid value.
                    </p>
                    <p>
                        The value should be a decimal odd between {minOdd} and{' '}
                        {maxOdd}.
                    </p>
                </>
            ) : null}
            <button
                className="registerbtn__nextStep"
                disabled={!isValid}
                onClick={e => {
                    e.preventDefault()
                    handleClick()
                }}
            >
                Update Event Odds
            </button>
        </>
    )
}

export default OddsUpdateFormFields
