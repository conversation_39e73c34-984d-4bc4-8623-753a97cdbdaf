import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import React, { SetStateAction, useEffect } from 'react'
import { Formik } from 'formik'
import { RequiredStar } from '../Form'
import { Outcome } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'
import { generateSortedOutcomes } from './utils'

export const ModalBody = ({
    event,
    confirmationStep,
    setEventStatus,
    setResult,
    result
}: {
    event: EventInstrument
    confirmationStep?: boolean
    setEventStatus: React.Dispatch<
        React.SetStateAction<
            | 'NotStarted'
            | 'InProgress'
            | 'Finished'
            | 'Cancelled'
            | 'Unknown'
            | 'Interrupted'
            | 'Postponed'
            | 'Other'
        >
    >
    setResult: React.Dispatch<SetStateAction<Outcome[]>>
    result: Outcome[]
}) => {
    const [formValues, setFormValues] = React.useState(
        event?.details?.eventStatus ?? 'NotStarted'
    )

    const eventStatusOptions = [
        {
            label: 'Not Started',
            value: 'NotStarted'
        },
        {
            label: 'In Progress',
            value: 'InProgress'
        },
        {
            label: 'Finished',
            value: 'Finished'
        },
        {
            label: 'Cancelled',
            value: 'Cancelled'
        },
        {
            label: 'Interrupted',
            value: 'Interrupted'
        },
        {
            label: 'Postponed',
            value: 'Postponed'
        }
    ]

    const participants = event?.details?.eventParticipants

    const threewayOutcomes = generateSortedOutcomes(event, 'ThreeWay')
    const twowayOutcomes = generateSortedOutcomes(event, 'TwoWay')
    const threeWayHandicapOutcomes = generateSortedOutcomes(
        event,
        'ThreeWayHandicap'
    )
    const overUnderOutcomes = generateSortedOutcomes(event, 'OverUnder')

    const handleSelectResult = (newOutcome: Outcome) => {
        let newResult = [...result]
        const existingOutcome = result.findIndex(
            outcome => outcome.subtype === newOutcome.subtype
        )

        const isCurrentOutcomeDraw = Boolean(
            result.find(outcome => outcome.subtype === 'Draw')
        )

        const isDrawOutcome = newOutcome.subtype === 'Draw'
        const isWinOutcome = newOutcome.subtype === 'Win'

        if (isDrawOutcome) {
            newResult = newResult.filter(outcome => outcome.subtype === 'Draw')
            newResult.push(newOutcome)
        } else if (isWinOutcome && isCurrentOutcomeDraw) {
            newResult = newResult.filter(outcome => outcome.subtype !== 'Draw')
            newResult.push(newOutcome)
        } else if (existingOutcome >= 0) {
            newResult[existingOutcome] = newOutcome
        } else {
            newResult.push(newOutcome)
        }

        setResult(newResult)
    }

    const handleEnhanceResultOutcomes = (outcome: Outcome) => {
        let newResult = [...result]

        const existingOutcome = result.findIndex(
            existingOutcome => existingOutcome.type_.tag === outcome.type_.tag
        )

        const existingThreeWayHandicap = result.findIndex(
            existingOutcome =>
                existingOutcome.type_.tag === 'ThreeWayHandicap' &&
                existingOutcome.type_.value === outcome.type_.value &&
                existingOutcome.participantId === outcome.participantId &&
                existingOutcome.subtype === outcome.subtype
        )
        if (existingThreeWayHandicap >= 0) {
            newResult = newResult.filter(
                existingOutcome =>
                    existingOutcome.type_.tag !== 'ThreeWayHandicap' &&
                    existingOutcome.type_.value !== outcome.type_.value
            )
        } else if (existingOutcome >= 0) {
            newResult[existingOutcome] = outcome
        } else {
            newResult.push(outcome)
        }

        setResult(newResult)
    }

    useEffect(() => {
        return () => {
            setResult([])
        }
        // eslint-disable-next-line
    }, [])

    if (!event) return null

    return (
        <div>
            <h1 style={{ paddingBottom: '40px' }}>Edit Event Status</h1>
            <div>
                {confirmationStep && (
                    <section>
                        <h3>
                            Are you sure you want to change the status of this
                            event?
                        </h3>
                    </section>
                )}
                {!confirmationStep && (
                    <Formik
                        initialValues={{
                            eventStatus: formValues,
                            eventParticipants: event.details.eventParticipants,
                            outcome: '',
                            overUnder: '',
                            threeWayHandicap: ''
                        }}
                        onSubmit={values => {
                            setEventStatus(
                                values.eventStatus as
                                    | 'NotStarted'
                                    | 'InProgress'
                                    | 'Finished'
                                    | 'Cancelled'
                                    | 'Unknown'
                                    | 'Interrupted'
                                    | 'Postponed'
                                    | 'Other'
                            )
                            const outcomes = JSON.parse(values.outcome)
                            setResult(outcomes)
                        }}
                    >
                        {formik => (
                            <div>
                                <form onSubmit={formik.handleSubmit}>
                                    <div>
                                        <select
                                            id="eventStatus"
                                            name="eventStatus"
                                            value={formValues}
                                            className="register__input"
                                            onChange={e => {
                                                formik.setFieldValue(
                                                    'eventStatus',
                                                    e.target.value
                                                )
                                                setFormValues(
                                                    e.target.value as
                                                        | 'NotStarted'
                                                        | 'InProgress'
                                                        | 'Finished'
                                                        | 'Cancelled'
                                                        | 'Unknown'
                                                        | 'Interrupted'
                                                        | 'Postponed'
                                                        | 'Other'
                                                )
                                                setEventStatus(
                                                    e.target.value as
                                                        | 'NotStarted'
                                                        | 'InProgress'
                                                        | 'Finished'
                                                        | 'Cancelled'
                                                        | 'Unknown'
                                                        | 'Interrupted'
                                                        | 'Postponed'
                                                        | 'Other'
                                                )
                                            }}
                                        >
                                            {eventStatusOptions.map(option => (
                                                <option
                                                    key={option.label}
                                                    value={option.value}
                                                    label={option.label}
                                                >
                                                    {option.label}
                                                </option>
                                            ))}
                                        </select>
                                        {formValues === 'Finished' && (
                                            <>
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        flexDirection: 'column'
                                                    }}
                                                >
                                                    <>
                                                        <div className="formEvent__labelContainer">
                                                            <label
                                                                className="formEvent__label"
                                                                htmlFor="description"
                                                            >
                                                                Select a winner{' '}
                                                                <RequiredStar />
                                                            </label>
                                                        </div>
                                                        {threewayOutcomes.length >
                                                        0 ? (
                                                            <div
                                                                style={{
                                                                    display:
                                                                        'flex',
                                                                    alignItems:
                                                                        'center',
                                                                    justifyContent:
                                                                        'center',
                                                                    gap: '10px'
                                                                }}
                                                            >
                                                                {React.Children.toArray(
                                                                    threewayOutcomes.map(
                                                                        outcome => (
                                                                            <>
                                                                                <label>
                                                                                    {participants.filter(
                                                                                        participant =>
                                                                                            participant.id ===
                                                                                            outcome
                                                                                                .outcome
                                                                                                .participantId
                                                                                    )[0]
                                                                                        ?.name ??
                                                                                        'Draw'}
                                                                                </label>
                                                                                <input
                                                                                    onChange={() => {
                                                                                        formik.setFieldValue(
                                                                                            'outcome',
                                                                                            outcome
                                                                                        )
                                                                                        handleSelectResult(
                                                                                            outcome.outcome
                                                                                        )
                                                                                    }}
                                                                                    type="radio"
                                                                                    name="outcome"
                                                                                    // checked={result.some(out => out === outcome.outcome)}
                                                                                    value={JSON.stringify(
                                                                                        outcome.outcome
                                                                                    )}
                                                                                />
                                                                            </>
                                                                        )
                                                                    )
                                                                )}
                                                            </div>
                                                        ) : null}
                                                        {twowayOutcomes.length >
                                                        0 ? (
                                                            <div
                                                                style={{
                                                                    display:
                                                                        'flex',
                                                                    gap: '10px'
                                                                }}
                                                            >
                                                                {React.Children.toArray(
                                                                    twowayOutcomes.map(
                                                                        outcome => (
                                                                            <>
                                                                                <label>
                                                                                    {
                                                                                        participants.filter(
                                                                                            participant =>
                                                                                                participant.id ===
                                                                                                outcome
                                                                                                    .outcome
                                                                                                    .participantId
                                                                                        )[0]
                                                                                            ?.name
                                                                                    }
                                                                                </label>
                                                                                <input
                                                                                    type="radio"
                                                                                    onChange={() => {
                                                                                        formik.setFieldValue(
                                                                                            'outcome',
                                                                                            outcome
                                                                                        )
                                                                                        handleSelectResult(
                                                                                            outcome.outcome
                                                                                        )
                                                                                    }}
                                                                                    name="outcome"
                                                                                    value={JSON.stringify(
                                                                                        outcome
                                                                                    )}
                                                                                />
                                                                            </>
                                                                        )
                                                                    )
                                                                )}
                                                            </div>
                                                        ) : null}
                                                        {overUnderOutcomes.length >
                                                        0 ? (
                                                            <div
                                                                style={{
                                                                    display:
                                                                        'flex',
                                                                    flexDirection:
                                                                        'column'
                                                                }}
                                                            >
                                                                <strong>
                                                                    Over Under
                                                                    Odds
                                                                </strong>
                                                                <div
                                                                    style={{
                                                                        display:
                                                                            'flex',
                                                                        gap: '10px'
                                                                    }}
                                                                >
                                                                    {React.Children.toArray(
                                                                        overUnderOutcomes.map(
                                                                            outcome => (
                                                                                <>
                                                                                    <label>
                                                                                        {
                                                                                            outcome
                                                                                                .outcome
                                                                                                .subtype
                                                                                        }{' '}
                                                                                        {
                                                                                            outcome
                                                                                                .outcome
                                                                                                .type_
                                                                                                .value
                                                                                        }
                                                                                    </label>
                                                                                    <input
                                                                                        type="radio"
                                                                                        name="overUnder"
                                                                                        onChange={() => {
                                                                                            formik.setFieldValue(
                                                                                                'overUnder',
                                                                                                outcome
                                                                                            )
                                                                                            handleEnhanceResultOutcomes(
                                                                                                outcome.outcome
                                                                                            )
                                                                                        }}
                                                                                        checked={result.some(
                                                                                            out =>
                                                                                                out ===
                                                                                                outcome.outcome
                                                                                        )}
                                                                                        value={JSON.stringify(
                                                                                            outcome
                                                                                        )}
                                                                                    />
                                                                                </>
                                                                            )
                                                                        )
                                                                    )}
                                                                </div>
                                                            </div>
                                                        ) : null}

                                                        {threeWayHandicapOutcomes.length >
                                                        0 ? (
                                                            <div
                                                                style={{
                                                                    display:
                                                                        'flex',
                                                                    flexDirection:
                                                                        'column'
                                                                }}
                                                            >
                                                                <strong>
                                                                    Three Way
                                                                    Handicap
                                                                    Odds
                                                                </strong>
                                                                <div
                                                                    style={{
                                                                        display:
                                                                            'flex',
                                                                        gap: '10px'
                                                                    }}
                                                                >
                                                                    {React.Children.toArray(
                                                                        threeWayHandicapOutcomes.map(
                                                                            outcome => (
                                                                                <>
                                                                                    <label>
                                                                                        {
                                                                                            participants.filter(
                                                                                                participant =>
                                                                                                    participant.id ===
                                                                                                    outcome
                                                                                                        .outcome
                                                                                                        .participantId
                                                                                            )[0]
                                                                                                ?.name
                                                                                        }{' '}
                                                                                        :{' '}
                                                                                        {
                                                                                            outcome
                                                                                                .outcome
                                                                                                .type_
                                                                                                .value
                                                                                        }{' '}
                                                                                        :{' '}
                                                                                        {
                                                                                            outcome
                                                                                                .outcome
                                                                                                .subtype
                                                                                        }
                                                                                    </label>
                                                                                    <input
                                                                                        type="checkbox"
                                                                                        defaultChecked={
                                                                                            false
                                                                                        }
                                                                                        checked={
                                                                                            result.length >
                                                                                                0 &&
                                                                                            result.some(
                                                                                                res =>
                                                                                                    res
                                                                                                        .type_
                                                                                                        .tag ===
                                                                                                        'ThreeWayHandicap' &&
                                                                                                    res
                                                                                                        .type_
                                                                                                        .value ===
                                                                                                        outcome
                                                                                                            .outcome
                                                                                                            .type_
                                                                                                            .value &&
                                                                                                    res.subtype ===
                                                                                                        outcome
                                                                                                            .outcome
                                                                                                            .subtype &&
                                                                                                    res.participantId ===
                                                                                                        outcome
                                                                                                            .outcome
                                                                                                            .participantId
                                                                                            )
                                                                                        }
                                                                                        name="threeWayHandicap"
                                                                                        onChange={() => {
                                                                                            formik.setFieldValue(
                                                                                                'threeWayHandicap',
                                                                                                outcome
                                                                                            )
                                                                                            handleEnhanceResultOutcomes(
                                                                                                outcome.outcome
                                                                                            )
                                                                                        }}
                                                                                        value={JSON.stringify(
                                                                                            outcome
                                                                                        )}
                                                                                    />
                                                                                </>
                                                                            )
                                                                        )
                                                                    )}
                                                                </div>
                                                            </div>
                                                        ) : null}
                                                    </>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                </form>
                            </div>
                        )}
                    </Formik>
                )}
            </div>
        </div>
    )
}
