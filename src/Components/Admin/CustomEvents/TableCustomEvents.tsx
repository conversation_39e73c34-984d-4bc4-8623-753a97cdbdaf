import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'

import BasicModal from 'Components/Modals/BasicModal'

import 'Containers/Admin/style.scss'
import { ModalBody } from './ModalBody'
import { Outcome } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'
import { generateSortedOutcomes } from './utils'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import getFormattedDate from 'Containers/Dashboard/getFormattedDate'
import { getTextByLang } from 'Components/Event/EventCard/SportTranslations.helper'

import Table from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import FilterContainer from 'Components/Table/Filter'
import FilterInput from 'Components/Table/Filter/Input'
import LoaderSpinner from 'Components/Loader'

import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable,
    ColumnFiltersState
} from '@tanstack/react-table'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import { getSearchState, handleSearch } from 'Components/Table/Filter/utils'
import { generateMarketNameForAdminTables } from 'Containers/Admin/utils/generateMarketNameForAdminTables'

interface ITableData {
    eventStartDate: string
    eventTitle: string
    market: any
    tournament: string
    geography: string
    eventStatus: string
    action: CreateEvent<EventInstrument, EventInstrument.Key, string>
}

const colFiltersInitState = [
    { id: 'eventTitle', value: '' },
    { id: 'market', value: '' },
    { id: 'geography', value: '' },
    { id: 'tournament', value: '' }
]

type EventStatusString =
    | 'NotStarted'
    | 'InProgress'
    | 'Finished'
    | 'Cancelled'
    | 'Unknown'
    | 'Interrupted'
    | 'Postponed'
    | 'Other'

//CHANGES HERE
export default function TableCustomEvents({
    contracts,
    handleClick
}: {
    contracts: readonly CreateEvent<
        EventInstrument,
        EventInstrument.Key,
        string
    >[]
    handleClick: (
        eventStatus:
            | 'NotStarted'
            | 'InProgress'
            | 'Finished'
            | 'Cancelled'
            | 'Unknown'
            | 'Interrupted'
            | 'Postponed'
            | 'Other',
        eventKey: EventInstrument.Key,
        results?: Outcome[]
    ) => any
}) {
    const [selectedEvent, setSelectedEvent] = React.useState<EventInstrument>()
    const [eventContract, setEventContract] =
        React.useState<EventInstrument.Key | null>(null)
    const [isOpenModal, setIsOpenModal] = React.useState(false)
    const [isConfirmationStep, setIsConfirmationStep] = React.useState(false)
    const [eventStatus, setSelectedEventStatus] =
        React.useState<EventStatusString>(
            selectedEvent?.details.eventStatus ?? 'NotStarted'
        )
    const [result, setResult] = React.useState<Outcome[]>([])
    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>(colFiltersInitState)
    const { sportTranslationsContracts, loadingSportTranslations } =
        useSportTranslationsContext()
    const US = React.useMemo(
        () =>
            sportTranslationsContracts.length > 0 &&
            sportTranslationsContracts[0]?.payload?.sportsMap
                ? getTextByLang(
                      'en_uk',
                      sportTranslationsContracts[0]?.payload?.sportsMap
                  )
                : [],
        [sportTranslationsContracts]
    )

    const columnHelper = createColumnHelper<ITableData>()
    const columns = React.useMemo(
        () => [
            columnHelper.accessor('eventStartDate', {
                header: 'Start Date',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('eventTitle', {
                header: 'Event Title',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('market', {
                header: 'Market',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('geography', {
                header: 'Geography',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('tournament', {
                header: 'Tournament',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('eventStatus', {
                header: 'Event Status',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('action', {
                header: 'Action',
                cell: info => {
                    return (
                        <button
                            type="button"
                            className="btn__admin"
                            onClick={() => {
                                setEventContract(info.getValue().key)
                                handleOpen(info.getValue().payload)
                                setSelectedEventStatus(
                                    info.getValue().payload.details
                                        .eventStatus as EventStatusString
                                )
                            }}
                        >
                            Update
                        </button>
                    )
                }
            })
        ],
        [columnHelper]
    )

    let data = React.useMemo(
        () =>
            contracts.map(contract => ({
                eventStartDate: getFormattedDate(
                    contract.payload.details.startDate
                ),
                eventTitle: getEventTitleFromLang(
                    contract.payload.details.eventTitle,
                    'US'
                ),
                market: generateMarketNameForAdminTables({
                    market: contract.payload.details.market,
                    usLang: US
                }),
                tournament:
                    contract.payload.details.submarkets.find(
                        data => data.tag === 'Tournament'
                    )?.value ?? '-',
                geography: contract.payload.details.geography.value,
                eventStatus: contract.payload.details.eventStatus,
                action: contract
            })),
        [US, contracts]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: false,
        state: {
            columnFilters
        },
        onColumnFiltersChange: setColumnFilters
    })

    const handleClose = () => {
        setIsOpenModal(false)
        setSelectedEvent(undefined)
        setEventContract(null)
    }
    const handleOpen = (event: EventInstrument) => {
        setSelectedEvent(event)
        setIsOpenModal(true)
    }

    const handleConfirm = async () => {
        if (selectedEvent && isConfirmationStep) {
            result.length > 0
                ? await handleClick(eventStatus, eventContract as never, result)
                : await handleClick(eventStatus, eventContract as never)
            setIsConfirmationStep(false)
            setResult([])
            handleClose()
        }
        if (selectedEvent && !isConfirmationStep) setIsConfirmationStep(true)

        void null
    }

    const handleIsDisable = () => {
        if (selectedEvent && eventStatus === 'Finished') {
            const threewayOutcomes = generateSortedOutcomes(
                selectedEvent,
                'ThreeWay'
            )
            const twowayOutcomes = generateSortedOutcomes(
                selectedEvent,
                'TwoWay'
            )
            const threeWayHandicapOutcomes = generateSortedOutcomes(
                selectedEvent,
                'ThreeWayHandicap'
            )
            const overUnderOutcomes = generateSortedOutcomes(
                selectedEvent,
                'OverUnder'
            )
            const hasThreeWay =
                threewayOutcomes.length > 0
                    ? result.some(res => res.type_.tag === 'ThreeWay')
                    : true
            const hasTwooWay =
                twowayOutcomes.length > 0
                    ? result.some(res => res.type_.tag === 'TwoWay')
                    : true
            const hasOverUnderOutcome =
                overUnderOutcomes.length > 0
                    ? result.some(res => res.type_.tag === 'OverUnder')
                    : true
            const hasThreeWayHandicap =
                threeWayHandicapOutcomes.length > 0
                    ? result.some(res => res.type_.tag === 'ThreeWayHandicap')
                    : true
            return (
                !result.length ||
                !hasThreeWay ||
                !hasTwooWay ||
                !hasOverUnderOutcome ||
                !hasThreeWayHandicap
            )
        }
        return false
    }

    if (loadingSportTranslations) {
        return <LoaderSpinner />
    }

    return (
        <>
            <FilterContainer>
                {React.Children.toArray(
                    colFiltersInitState.map(col => {
                        const title = descriptCamelCase(col.id)
                        return (
                            <FilterInput
                                label={title.toLocaleUpperCase()}
                                type="text"
                                placeholder={`Filter by ${title.toLocaleLowerCase()}`}
                                onChange={e =>
                                    handleSearch(table, e.target.value, col.id)
                                }
                                value={
                                    (getSearchState(table, col.id) as string) ??
                                    ''
                                }
                            />
                        )
                    })
                )}
            </FilterContainer>
            <Table
                tableHeader={table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                            <th key={header.id}>
                                {header.column.columnDef.header}
                            </th>
                        ))}
                    </tr>
                ))}
                tableBodyRow={table.getRowModel().rows.map(row => (
                    <tr key={row.id}>
                        {row.getVisibleCells().map(cell => (
                            <td key={cell.id}>
                                {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                )}
                            </td>
                        ))}
                    </tr>
                ))}
            />
            <Pagination table={table} />
            <BasicModal
                body={
                    <ModalBody
                        event={selectedEvent as EventInstrument}
                        confirmationStep={isConfirmationStep}
                        setEventStatus={setSelectedEventStatus}
                        setResult={setResult}
                        result={result}
                    />
                }
                isOpenModal={isOpenModal}
                handleClose={handleClose}
                shouldCloseOnOverlayClickProp={true}
                footerBody={
                    <>
                        <button
                            disabled={handleIsDisable()}
                            type="button"
                            className="btn btn__primary"
                            onClick={handleConfirm}
                        >
                            Update
                        </button>
                        <button
                            type="button"
                            className="btn btn__grey"
                            onClick={handleClose}
                        >
                            Cancel
                        </button>
                    </>
                }
            />
        </>
    )
}
