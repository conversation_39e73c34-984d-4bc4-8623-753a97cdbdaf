import React from 'react'
import BasicModal from 'Components/Modals/BasicModal'

interface ICancelEventsModal {
    isOpenModal: boolean
    handleConfirm: () => void
    handleClose: () => void
}

const CancelEventsModal = ({
    isOpenModal,
    handleClose,
    handleConfirm
}: ICancelEventsModal) => {
    return (
        <BasicModal
            body={<p>Are you sure you want to reinstate this event?</p>}
            footerBody={
                <>
                    <button
                        className="btn btn__primary"
                        onClick={handleConfirm}
                    >
                        Yes
                    </button>
                    <button className="btn btn__grey" onClick={handleClose}>
                        No
                    </button>
                </>
            }
            isOpenModal={isOpenModal}
            handleClose={handleClose}
        />
    )
}

export default CancelEventsModal
