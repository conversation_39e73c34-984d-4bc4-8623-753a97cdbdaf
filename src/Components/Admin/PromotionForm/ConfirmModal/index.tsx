import React from 'react'

import BasicModal from 'Components/Modals/BasicModal'
import { useFormikContext } from 'formik'

const ConfirmModal = ({
    isOpen,
    handleClose,
    message
}: {
    isOpen: boolean
    handleClose: () => void
    message?: string
}) => {
    const { submitForm } = useFormikContext()

    return (
        <BasicModal
            isOpenModal={isOpen}
            body={
                message ? (
                    <p>{message}</p>
                ) : (
                    <p>Are you sure you want to create this promotion?</p>
                )
            }
            footerBody={
                <>
                    <button className="btn btn__primary" onClick={submitForm}>
                        Yes
                    </button>
                    <button className="btn btn__grey" onClick={handleClose}>
                        No
                    </button>
                </>
            }
            handleClose={handleClose}
        />
    )
}

export default ConfirmModal
