import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const Amounts = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3>Promotion Amounts:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="maxAmount">Max Amount</label>
                <Field
                    className="register__input"
                    type="text"
                    name="maxAmount"
                />
                {errors?.maxAmount ? (
                    <ErrorMessage message={errors?.maxAmount} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="minAmount">Min Amount</label>
                <Field
                    className="register__input"
                    type="text"
                    name="minAmount"
                />
                {errors?.minAmount ? (
                    <ErrorMessage message={errors?.minAmount} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default Amounts
