import React from 'react'
import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const ActionOptions = ['FirstTime', 'Reload']

const PromoType = () => {
    const { values, errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <div>
                <label htmlFor="valueTag">Promotion Type:</label>
                <Field
                    className="register__input"
                    as="select"
                    name="promoTypeTag"
                >
                    {React.Children.toArray(
                        ActionOptions.map(option => (
                            <option value={option}>{option}</option>
                        ))
                    )}
                </Field>
                {errors?.promoTypeTag ? (
                    <ErrorMessage message={errors?.promoTypeTag} />
                ) : null}
            </div>
            <br />
            <div>
                {values?.promoTypeTag === 'FirstTime' ? (
                    <div>
                        <label htmlFor="valueTagValue">
                            Number of days available to use first time
                            promotion:
                        </label>
                        <Field
                            className="register__input"
                            type="text"
                            name="promoTypeValue"
                        />
                        {errors?.promoTypeValue ? (
                            <ErrorMessage message={errors?.promoTypeValue} />
                        ) : null}
                    </div>
                ) : null}
            </div>
            <br />
        </>
    )
}

export default PromoType
