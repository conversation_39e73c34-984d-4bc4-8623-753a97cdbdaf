import React from 'react'
import { useFormikContext } from 'formik'

import TitleSection from './TitleSection'
import StartDate from './StartDate'
import ShortDescription from './ShortDesc'
import LongDescription from './LongDesc'
import BaseUrl from './BaseURL'
import Banner from './Banner'
import ThumbnailUrl from './ThumbnailURL'
import Amounts from './Amounts'
import LimitedPromotion from './Limited'
import ActionTag from './ActionTag'
import ActionValue from './ActionValue'
import PercentagOrCash from './PercentageOrCash'
import PromoType from './PromoType'
import FormSubmitBtn from './FormSubmitBtn'
import FormEditBtn from './FormEditBtn'

import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const PromotionForm = ({
    handleClick,
    isEdit = false
}: {
    handleClick: () => void
    isEdit?: boolean
}) => {
    const { validateForm } = useFormikContext<IPromotionForm>()

    const validateCallBack = React.useCallback(() => {
        validateForm()
    }, [validateForm])

    React.useEffect(() => {
        validateCallBack()
    }, [validateCallBack])

    return (
        <>
            <TitleSection />
            <StartDate />
            <ShortDescription />
            <LongDescription />
            <BaseUrl />
            <Banner />
            <ThumbnailUrl />
            <Amounts />
            <LimitedPromotion />
            <ActionTag />
            <ActionValue />
            <PercentagOrCash />
            <PromoType />
            {isEdit ? (
                <FormEditBtn handleClick={handleClick} />
            ) : (
                <FormSubmitBtn handleClick={handleClick} />
            )}
        </>
    )
}

export default PromotionForm
