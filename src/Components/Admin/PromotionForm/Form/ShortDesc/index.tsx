import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const ShortDescription = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3>Short Description:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="englishShortDescription">English</label>
                <Field
                    className="register__input"
                    type="text"
                    name="englishShortDescription"
                />
                {errors?.englishShortDescription ? (
                    <ErrorMessage message={errors?.englishShortDescription} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="spanishShortDescription">Spanish</label>
                <Field
                    className="register__input"
                    type="text"
                    name="spanishShortDescription"
                />
                {errors?.spanishShortDescription ? (
                    <ErrorMessage message={errors?.spanishShortDescription} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="portugueseShortDescription">Portuguese</label>
                <Field
                    className="register__input"
                    type="text"
                    name="portugueseShortDescription"
                />
                {errors?.portugueseShortDescription ? (
                    <ErrorMessage
                        message={errors?.portugueseShortDescription}
                    />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default ShortDescription
