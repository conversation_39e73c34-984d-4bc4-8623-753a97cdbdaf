import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const TitleSection = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3 className="promotionInputContainerMargin">Promotion Titles</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="englishTitle">English</label>
                <Field
                    className="register__input"
                    type="text"
                    name="englishTitle"
                />
                {errors?.englishTitle ? (
                    <ErrorMessage message={errors?.englishTitle} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="spanishTitle">Spanish</label>
                <Field
                    className="register__input"
                    type="text"
                    name="spanishTitle"
                />
                {errors?.spanishTitle ? (
                    <ErrorMessage message={errors?.spanishTitle} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="portugueseTitle">Portuguese</label>
                <Field
                    className="register__input"
                    type="text"
                    name="portugueseTitle"
                />
                {errors?.portugueseTitle ? (
                    <ErrorMessage message={errors?.portugueseTitle} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default TitleSection
