import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const ThumbnailUrl = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3>Thumbnail URL:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="englishThumbnailUrl">English</label>
                <Field
                    className="register__input"
                    type="text"
                    name="englishThumbnailUrl"
                />
                {errors?.englishThumbnailUrl ? (
                    <ErrorMessage message={errors?.englishThumbnailUrl} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="spanishThumbnailUrl">Spanish</label>
                <Field
                    className="register__input"
                    type="text"
                    name="spanishThumbnailUrl"
                />
                {errors?.spanishThumbnailUrl ? (
                    <ErrorMessage message={errors?.spanishThumbnailUrl} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="portugueseThumbnailUrl">Portuguese</label>
                <Field
                    className="register__input"
                    type="text"
                    name="portugueseThumbnailUrl"
                />
                {errors?.portugueseThumbnailUrl ? (
                    <ErrorMessage message={errors?.portugueseThumbnailUrl} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default ThumbnailUrl
