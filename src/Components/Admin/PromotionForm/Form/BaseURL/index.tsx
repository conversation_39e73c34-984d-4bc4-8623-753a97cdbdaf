import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const BaseUrl = () => {
    const { errors } = useFormikContext<IPromotionForm>()

    return (
        <>
            <h3>Base URL:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="englishBaseURL">English</label>
                <Field
                    className="register__input"
                    type="text"
                    name="englishBaseURL"
                />
                {errors?.englishBaseURL ? (
                    <ErrorMessage message={errors?.englishBaseURL} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="spanishBaseURL">Spanish</label>
                <Field
                    className="register__input"
                    type="text"
                    name="spanishBaseURL"
                />
                {errors?.spanishBaseURL ? (
                    <ErrorMessage message={errors?.spanishBaseURL} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="portugueseBaseURL">Portuguese</label>
                <Field
                    className="register__input"
                    type="text"
                    name="portugueseBaseURL"
                />
                {errors?.portugueseBaseURL ? (
                    <ErrorMessage message={errors?.portugueseBaseURL} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default BaseUrl
