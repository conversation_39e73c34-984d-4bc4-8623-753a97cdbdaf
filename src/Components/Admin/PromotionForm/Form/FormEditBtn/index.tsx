import { useFormikContext } from 'formik'

const FormEditBtn = ({ handleClick }: { handleClick: () => void }) => {
    const { isValid } = useFormikContext()
    return (
        <button
            className="registerbtn__nextStep"
            disabled={!isValid}
            onClick={e => {
                e.preventDefault()
                handleClick()
            }}
        >
            Edit Promotion
        </button>
    )
}

export default FormEditBtn
