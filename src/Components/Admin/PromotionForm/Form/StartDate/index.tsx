import { Field, FieldProps, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const StartDate = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3>Promotion Dates</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="startDate">Start Date:</label>
                <Field name="startDate">
                    {({ field }: FieldProps) => (
                        <input
                            type="datetime-local"
                            className="register__input"
                            min={new Date().toISOString().slice(0, -8)}
                            step="1"
                            {...field}
                        />
                    )}
                </Field>
                {errors?.startDate ? (
                    <ErrorMessage message={errors?.startDate} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="endDate">End Date:</label>
                <Field name="endDate">
                    {({ field }: FieldProps) => (
                        <input
                            type="date"
                            className="register__input"
                            step="1"
                            {...field}
                        />
                    )}
                </Field>
                {errors?.endDate ? (
                    <ErrorMessage message={errors?.endDate} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default StartDate
