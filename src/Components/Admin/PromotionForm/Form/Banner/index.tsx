import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const BannerSection = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3>Banners:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="englishBannerURL">English</label>
                <Field
                    className="register__input"
                    type="text"
                    name="englishBannerURL"
                />
                {errors?.englishBannerURL ? (
                    <ErrorMessage message={errors?.englishBannerURL} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="spanishBannerURL">Spanish</label>
                <Field
                    className="register__input"
                    type="text"
                    name="spanishBannerURL"
                />
                {errors?.spanishBannerURL ? (
                    <ErrorMessage message={errors?.spanishBannerURL} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="portugueseBannerURL">Portuguese</label>
                <Field
                    className="register__input"
                    type="text"
                    name="portugueseBannerURL"
                />
                {errors?.portugueseBannerURL ? (
                    <ErrorMessage message={errors?.portugueseBannerURL} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default BannerSection
