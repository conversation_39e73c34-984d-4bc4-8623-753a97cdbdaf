import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const LimitedPromotion = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3>Limited usage:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="limitedPromotion">Number of usages</label>
                <Field
                    className="register__input"
                    type="text"
                    name="limitedPromotion"
                />
                {errors?.limitedPromotion ? (
                    <ErrorMessage message={errors?.limitedPromotion} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default LimitedPromotion
