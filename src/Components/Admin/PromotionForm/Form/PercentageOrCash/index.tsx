import React from 'react'
import { Field, useFormikContext } from 'formik'
import numeral from 'numeral'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const ActionOptions = ['Percentage', 'Cash']

const Message = ({
    isPercentage,
    valueTagValue
}: {
    isPercentage: boolean
    valueTagValue: string
}) => {
    const messageBodyPercentage =
        'Since the percentage option is selected be aware that it has a range that goes from 0.01 - equivalent to 1% -  to 1 - equivalent to 100%'
    const messageBodyCash =
        'Since the cash option is selected be aware that, for example, you put 10.50 it will apply a discount of cash of $10.50.'

    const calculatePromoValue = () => {
        let convertToNum = Number(valueTagValue)
        if (isPercentage) {
            return `${convertToNum * 100}% `
        }
        return numeral(convertToNum).format('$0,0.00')
    }
    return (
        <p className="attentionMessage">
            <strong>Attention:</strong> This field is from a decimal type.
            {isPercentage ? messageBodyPercentage : messageBodyCash}
            <strong> Current discount value: {calculatePromoValue()}</strong>
        </p>
    )
}

const PercentagOrCash = () => {
    const {
        errors,
        values: { valueTag, valueTagValue }
    } = useFormikContext<IPromotionForm>()
    return (
        <>
            <div className="promotionInputContainerMargin">
                <label htmlFor="valueTag">Percentage or Cash:</label>
                <Field className="register__input" as="select" name="valueTag">
                    {React.Children.toArray(
                        ActionOptions.map(option => (
                            <option value={option}>{option}</option>
                        ))
                    )}
                </Field>
                {errors?.valueTag ? (
                    <ErrorMessage message={errors?.valueTag} />
                ) : null}
            </div>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="valueTagValue">
                    Value of Percentage or Cash:
                </label>
                <Message
                    isPercentage={valueTag === 'Percentage'}
                    valueTagValue={valueTagValue}
                />
                <Field
                    className="register__input"
                    type="text"
                    name="valueTagValue"
                />
                {errors?.valueTagValue ? (
                    <ErrorMessage message={errors?.valueTagValue} />
                ) : null}
            </div>
            <br />
        </>
    )
}

export default PercentagOrCash
