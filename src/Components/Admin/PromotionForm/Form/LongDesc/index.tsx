import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const LongDescription = () => {
    const { errors } = useFormikContext<IPromotionForm>()

    return (
        <>
            <h3>Long Description:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="englishLongDescription">English</label>
                <Field
                    className="register__input"
                    as="textarea"
                    name="englishLongDescription"
                />
                {errors?.englishLongDescription ? (
                    <ErrorMessage message={errors?.englishLongDescription} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="spanishLongDescription">Spanish</label>
                <Field
                    className="register__input"
                    as="textarea"
                    name="spanishLongDescription"
                />
                {errors?.spanishLongDescription ? (
                    <ErrorMessage message={errors?.spanishLongDescription} />
                ) : null}
            </div>
            <div className="promotionInputContainerMargin">
                <label htmlFor="portugueseLongDescription">Portuguese</label>
                <Field
                    className="register__input"
                    as="textarea"
                    name="portugueseLongDescription"
                />
                {errors?.portugueseLongDescription ? (
                    <ErrorMessage message={errors?.portugueseLongDescription} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default LongDescription
