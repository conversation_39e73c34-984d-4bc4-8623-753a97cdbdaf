import React from 'react'
import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const ActionOptions = ['Bet', 'Deposit', 'Withdrawal']

const ActionTag = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3>Promotion Action:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="actionTag">Type</label>
                <Field className="register__input" as="select" name="actionTag">
                    {React.Children.toArray(
                        ActionOptions.map(option => (
                            <option value={option}>{option}</option>
                        ))
                    )}
                </Field>
                {errors?.actionTag ? (
                    <ErrorMessage message={errors?.actionTag} />
                ) : null}
            </div>
            <br />
            <hr />
            <br />
        </>
    )
}

export default ActionTag
