import React from 'react'
import { Field, useFormikContext } from 'formik'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IPromotionForm } from 'Containers/Admin/CreatePromotion/utils'

const ActionOptions = ['Discount', 'Bonus']

const ActionValue = () => {
    const { errors } = useFormikContext<IPromotionForm>()
    return (
        <>
            <h3>Promotion Action Type:</h3>
            <br />
            <div className="promotionInputContainerMargin">
                <label htmlFor="actionValue">Discount or bonus</label>
                <Field
                    className="register__input"
                    as="select"
                    name="actionValue"
                >
                    {React.Children.toArray(
                        ActionOptions.map(option => (
                            <option value={option}>{option}</option>
                        ))
                    )}
                </Field>
                {errors?.actionValue ? (
                    <ErrorMessage message={errors?.actionValue} />
                ) : null}
            </div>
            <br />
        </>
    )
}

export default ActionValue
