import { useFormikContext } from 'formik'

const FormSubmitBtn = ({ handleClick }: { handleClick: () => void }) => {
    const { isValid } = useFormikContext()
    return (
        <button className='registerbtn__nextStep' disabled={!isValid} onClick={(e) => {
            e.preventDefault();
            handleClick()
        }}>
            Create Promotion
        </button>
    )
}

export default FormSubmitBtn