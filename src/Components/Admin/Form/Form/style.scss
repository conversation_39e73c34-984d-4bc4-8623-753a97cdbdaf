@import '../../../../Styles/colors';

@mixin gridTemplate($colNumber, $width) {
    display: grid;
    grid-template-columns: repeat($colNumber, $width);
    gap: 10px;
    align-items: center;
    justify-content: space-between;
}

.editEvent {
    &__form {
        display: flex;
        flex-direction: column;
        margin-top: 10px;
        gap: 10px;

        label {
            font-weight: bold;
            text-transform: capitalize;
        }

        .participants {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 10px;
            align-items: center;
            justify-content: space-between;
            div {
                height: 100px;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
            }
        }

        .outcomes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            align-items: center;
            justify-content: space-between;
            div {
                height: 100px;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
            }
        }

        .btnActions {
            @include gridTemplate(4, minmax(100px, 200px));
        }

        &__error {
            color: $rederror;
            font-size: 1rem;
            padding: 5px 0 0 0;
        }
    }
}
