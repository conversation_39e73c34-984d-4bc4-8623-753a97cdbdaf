import React from 'react'
import {
    Field,
    FieldArray,
    FieldInputProps,
    FieldProps,
    useFormikContext
} from 'formik'
import {
    TEditFormData,
    IOutcomes,
    TTag
} from 'Containers/Admin/EditEvents/EditPage'
import {
    WinDrawOptions,
    OverUnderOptions
} from 'Components/Admin/Form/constants'

const valuesToPush = {
    threeWay: {
        odds: '',
        participantId: '',
        participantOrder: '',
        tag: 'ThreeWay',
        valueOfTag: {},
        subtype: 'Win',
        order: ''
    },
    twoWay: {
        odds: '',
        participantId: '',
        participantOrder: '',
        tag: 'TwoWay',
        valueOfTag: {},
        subtype: 'Win',
        order: ''
    },
    overUnder: {
        odds: '',
        participantId: '',
        participantOrder: '0',
        tag: 'OverUnder',
        valueOfTag: '',
        subtype: 'Over',
        order: ''
    },
    threeWayHandicap: {
        odds: '',
        participantId: '',
        participantOrder: '',
        tag: 'ThreeWayHandicap',
        valueOfTag: '',
        subtype: 'Win',
        order: ''
    }
}

interface IWinOrDrawSelect<T> {
    formikProps: FieldInputProps<T>
    isOverUnder: boolean
}

const WinOrDrawSelect = ({
    formikProps,
    isOverUnder
}: IWinOrDrawSelect<string>) => {
    const optionsToIterate = isOverUnder ? OverUnderOptions : WinDrawOptions
    return (
        <select
            placeholder="Odd option"
            className="register__input"
            {...formikProps}
        >
            <option value="" label="Odd option">
                Odd option
            </option>
            {React.Children.toArray(
                optionsToIterate.map(option => (
                    <option key={option} value={option} label={option}>
                        {option}
                    </option>
                ))
            )}
        </select>
    )
}

const disabler = (outcomes: IOutcomes[], tag: TTag, length: number) =>
    outcomes.filter(data => data.tag === tag).length >= length

const Outcomes = () => {
    const { values } = useFormikContext<TEditFormData>()
    const { outcomes } = values

    const hasTwoWay =
        outcomes.filter(outcome => outcome.tag === 'TwoWay').length > 0
    const hasThreeWay =
        outcomes.filter(outcome => outcome.tag === 'ThreeWay').length > 0
    const disableTwoWay = disabler(outcomes, 'TwoWay', 2) || hasThreeWay
    const disableThreeWay = disabler(outcomes, 'ThreeWay', 3) || hasTwoWay
    const disableOverUnder = disabler(outcomes, 'OverUnder', 2)
    const disableThreeWayHandicap = disabler(outcomes, 'ThreeWayHandicap', 3)

    return (
        <>
            <h2>Outcomes Data</h2>
            <FieldArray name="outcomes">
                {({ remove, push }) => (
                    <>
                        {outcomes?.length > 0 &&
                            React.Children.toArray(
                                outcomes.map((outcome, index) => (
                                    <div className="outcomes">
                                        <div>
                                            <label htmlFor="order">
                                                Order:
                                            </label>
                                            <Field
                                                className="register__input"
                                                name={`outcomes.${index}.order`}
                                                placeholder="Outcome order"
                                                type="text"
                                            />
                                        </div>
                                        {outcomes[index].tag ===
                                        'OverUnder' ? null : (
                                            <>
                                                <div>
                                                    <label htmlFor="participant id">
                                                        participant id:
                                                    </label>
                                                    <Field
                                                        className="register__input"
                                                        name={`outcomes.${index}.participantId`}
                                                        placeholder="Participant Id"
                                                        type="text"
                                                    />
                                                </div>
                                                <div>
                                                    <label htmlFor="participant order">
                                                        participant order:
                                                    </label>
                                                    <Field
                                                        className="register__input"
                                                        name={`outcomes.${index}.participantOrder`}
                                                        placeholder="Participant order"
                                                        type="text"
                                                    />
                                                </div>
                                            </>
                                        )}
                                        <div>
                                            <label htmlFor="type">tag:</label>
                                            <Field
                                                name={`outcomes.${index}.tag`}
                                            >
                                                {({
                                                    field
                                                }: FieldProps<string>) => (
                                                    <input
                                                        {...field}
                                                        disabled={true}
                                                        type="text"
                                                        className="register__input"
                                                        placeholder="tag"
                                                    />
                                                )}
                                            </Field>
                                        </div>
                                        <div>
                                            <label htmlFor="subtype">
                                                subtype:
                                            </label>
                                            <Field
                                                name={`outcomes.${index}.subtype`}
                                            >
                                                {({
                                                    field
                                                }: FieldProps<string>) => (
                                                    <WinOrDrawSelect
                                                        formikProps={field}
                                                        isOverUnder={
                                                            outcomes[index]
                                                                .tag ===
                                                            'OverUnder'
                                                        }
                                                    />
                                                )}
                                            </Field>
                                        </div>
                                        {typeof outcome.valueOfTag ===
                                        'string' ? (
                                            <div>
                                                <label htmlFor="type">
                                                    outcome value:
                                                </label>
                                                <Field
                                                    className="register__input"
                                                    name={`outcomes.${index}.valueOfTag`}
                                                    placeholder="Outcome Value"
                                                    type="text"
                                                />
                                            </div>
                                        ) : null}
                                        <div>
                                            <label htmlFor="type">
                                                Odd value
                                            </label>
                                            <Field
                                                className="register__input"
                                                name={`outcomes.${index}.odds`}
                                                placeholder="Odd Value"
                                                type="text"
                                            />
                                        </div>
                                        <div>
                                            <button
                                                onClick={e => {
                                                    e.preventDefault()
                                                    remove(index)
                                                }}
                                                className="registerbtn__nextStep"
                                            >
                                                Remove
                                            </button>
                                        </div>
                                    </div>
                                ))
                            )}
                        <div className="btnActions">
                            <button
                                onClick={e => {
                                    e.preventDefault()
                                    push(valuesToPush.threeWay)
                                }}
                                className="registerbtn__nextStep"
                                disabled={disableThreeWay}
                            >
                                Add ThreeWay
                            </button>
                            <button
                                onClick={e => {
                                    e.preventDefault()
                                    push(valuesToPush.twoWay)
                                }}
                                disabled={disableTwoWay}
                                className="registerbtn__nextStep"
                            >
                                Add TwoWay
                            </button>
                            <button
                                onClick={e => {
                                    e.preventDefault()
                                    push(valuesToPush.overUnder)
                                }}
                                className="registerbtn__nextStep"
                                disabled={disableOverUnder}
                            >
                                Add Over/Under
                            </button>
                            <button
                                onClick={e => {
                                    e.preventDefault()
                                    push(valuesToPush.threeWayHandicap)
                                }}
                                className="registerbtn__nextStep"
                                disabled={disableThreeWayHandicap}
                            >
                                Add ThreeWayHandycap
                            </button>
                        </div>
                    </>
                )}
            </FieldArray>
        </>
    )
}

export default Outcomes
