import React from 'react'
import { Field, FieldArray, useFormikContext } from 'formik'
import { TEditFormData } from 'Containers/Admin/EditEvents/EditPage'
import { initStateParticipants } from 'Components/Admin/Form/constants'

const ParticipantsFields = () => {
    const { values } = useFormikContext<TEditFormData>()
    const { eventParticipants } = values
    return (
        <>
            <h2>Participant Data</h2>
            <FieldArray name="eventParticipants">
                {({ remove, push }) => (
                    <>
                        {eventParticipants?.length > 0 &&
                            React.Children.toArray(
                                eventParticipants.map((_, index) => (
                                    <div className="participants">
                                        <div>
                                            <label htmlFor="participantId">
                                                participant id:
                                            </label>
                                            <Field
                                                className="register__input"
                                                name={`eventParticipants.${index}.id`}
                                                placeholder="Participant id"
                                                type="text"
                                            />
                                        </div>
                                        <div>
                                            <label htmlFor="participant name">
                                                participant name:
                                            </label>
                                            <Field
                                                className="register__input"
                                                name={`eventParticipants.${index}.name`}
                                                placeholder="Participant name"
                                                type="text"
                                            />
                                        </div>
                                        <div>
                                            <label htmlFor="participant order">
                                                participant order:
                                            </label>
                                            <Field
                                                className="register__input"
                                                name={`eventParticipants.${index}.order`}
                                                placeholder="Participant order"
                                                type="text"
                                            />
                                        </div>
                                        <div>
                                            <label htmlFor="participant co_op">
                                                participant co_op:
                                            </label>
                                            <Field
                                                className="register__input"
                                                name={`eventParticipants.${index}.co_op`}
                                                placeholder="Participant co_op"
                                                type="text"
                                            />
                                        </div>
                                        <div>
                                            <button
                                                onClick={e => {
                                                    e.preventDefault()
                                                    remove(index)
                                                }}
                                                className="registerbtn__nextStep"
                                            >
                                                Remove
                                            </button>
                                        </div>
                                    </div>
                                ))
                            )}
                        <button
                            onClick={e => {
                                e.preventDefault()
                                navigate(initStateParticipants)
                            }}
                            className="registerbtn__nextStep"
                        >
                            Add Participant
                        </button>
                    </>
                )}
            </FieldArray>
        </>
    );
}

export default ParticipantsFields
