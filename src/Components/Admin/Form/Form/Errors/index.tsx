import React from 'react'
import { useFormikContext } from 'formik'
import { TEditFormData } from 'Containers/Admin/EditEvents/EditPage'

const ErrorFeedback = () => {
    const { errors, values } = useFormikContext<TEditFormData>()
    const { outcomes } = values

    const threeWay = outcomes.filter(out => out.tag === 'ThreeWay')
    const twoWay = outcomes.filter(out => out.tag === 'TwoWay')
    const threeWayHandicap = outcomes.filter(
        out => out.tag === 'ThreeWayHandicap'
    )
    const OverUnder = outcomes.filter(out => out.tag === 'OverUnder')

    const hasEnoughThreeWay = threeWay.length > 0 ? threeWay.length === 3 : true
    const hasEnoughTwoWay = twoWay.length > 0 ? twoWay.length === 2 : true
    const hasEnoughThreeWayHandicap =
        threeWayHandicap.length > 0 ? threeWayHandicap.length === 3 : true
    const hasEnoughOverUnder =
        OverUnder.length > 0 ? OverUnder.length === 2 : true

    const displayTitle =
        errors.eventParticipants ||
        // errors?.eventTitle ||
        errors?.market ||
        errors?.outcomes ||
        errors?.startDate ||
        !hasEnoughThreeWay ||
        !hasEnoughTwoWay ||
        !hasEnoughOverUnder ||
        !hasEnoughThreeWayHandicap

    return (
        <>
            {displayTitle ? <h2>Errors:</h2> : null}
            {errors?.englishTitle ? (
                <>
                    <strong className="editEvent__form__error">
                        Event English Title:
                    </strong>
                    <p className="editEvent__form__error">
                        {errors.englishTitle}
                    </p>
                </>
            ) : null}
            {errors?.spanishTitle ? (
                <>
                    <strong className="editEvent__form__error">
                        Event Spanish Title:
                    </strong>
                    <p className="editEvent__form__error">
                        {errors.spanishTitle}
                    </p>
                </>
            ) : null}
            {errors?.portugueseTitle ? (
                <>
                    <strong className="editEvent__form__error">
                        Event Portuguese Title:
                    </strong>
                    <p className="editEvent__form__error">
                        {errors.portugueseTitle}
                    </p>
                </>
            ) : null}
            {errors?.description ? (
                <>
                    <strong className="editEvent__form__error">
                        Description:
                    </strong>
                    <p className="editEvent__form__error">
                        {errors.description}
                    </p>
                </>
            ) : null}
            {errors?.geography ? (
                <>
                    <strong className="editEvent__form__error">
                        Geography:
                    </strong>
                    <p className="editEvent__form__error">{errors.geography}</p>
                </>
            ) : null}
            {errors?.tournamentTitle ? (
                <>
                    <strong className="editEvent__form__error">
                        Tournament title:
                    </strong>
                    <p className="editEvent__form__error">
                        {errors.tournamentTitle}
                    </p>
                </>
            ) : null}
            {errors?.startDate ? (
                <>
                    <strong className="editEvent__form__error">
                        Start Date:
                    </strong>
                    <p className="editEvent__form__error">{errors.startDate}</p>
                </>
            ) : null}
            {errors?.eventParticipants ? (
                typeof errors?.eventParticipants === 'string' ? (
                    <>
                        <strong className="editEvent__form__error">
                            Event Participants:
                        </strong>
                        <p className="editEvent__form__error">
                            {errors.eventParticipants}
                        </p>
                    </>
                ) : (
                    <>
                        <strong className="editEvent__form__error">
                            Event Participants:
                        </strong>
                        <p className="editEvent__form__error">
                            Error in event participants, please check the name,
                            id and/or order fields
                        </p>
                    </>
                )
            ) : null}
            {errors?.outcomes ? (
                typeof errors?.outcomes === 'string' ? (
                    <>
                        <strong className="editEvent__form__error">
                            Outcomes:
                        </strong>
                        <p className="editEvent__form__error">
                            {errors.outcomes}
                        </p>
                    </>
                ) : (
                    <>
                        <strong className="editEvent__form__error">
                            Outcomes:
                        </strong>
                        <p className="editEvent__form__error">
                            Error in outcomes, please check the order,
                            participant id, participant, odd value and/or
                            outcome value fields
                        </p>
                    </>
                )
            ) : null}
            {!hasEnoughThreeWay ? (
                <>
                    <p className="editEvent__form__error">
                        You need to have 3 Three Way outcomes
                    </p>
                </>
            ) : null}
            {!hasEnoughTwoWay ? (
                <>
                    <p className="editEvent__form__error">
                        You need to have 2 Two Way outcomes
                    </p>
                </>
            ) : null}
            {!hasEnoughOverUnder ? (
                <>
                    <p className="editEvent__form__error">
                        You need to have 2 Over/Under outcomes
                    </p>
                </>
            ) : null}
            {!hasEnoughThreeWayHandicap ? (
                <>
                    <p className="editEvent__form__error">
                        You need to have 3 Three Way Handicap outcomes
                    </p>
                </>
            ) : null}
        </>
    )
}

export default ErrorFeedback
