import React from 'react'
import { useFormikContext } from 'formik'

import { MarketOptions } from 'Components/Admin/Form/constants'
import { getTextByLang } from 'Components/Event/EventCard/SportTranslations.helper'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import LoaderSpinner from 'Components/Loader'
import { TEditFormData } from 'Containers/Admin/EditEvents/EditPage'

import ParticipantsFields from './Participants'
import Outcomes from './Outcomes'
import ErrorFeedback from './Errors'

import './style.scss'
import EventTitle from './EventTitle'

//CHANGES HERE
const FormEdit = ({
    handleEdit,
    isEdit = true
}: {
    handleEdit: () => void
    isEdit?: boolean
}) => {
    const { getFieldProps, values, setFieldValue, isValid, validateForm } =
        useFormikContext<TEditFormData>()
    const { outcomes } = values
    const validateCallBack = React.useCallback(() => {
        validateForm()
    }, [validateForm])

    React.useEffect(() => {
        validateCallBack()
    }, [validateCallBack])

    const threeWay = outcomes.filter(out => out.tag === 'ThreeWay')
    const twoWay = outcomes.filter(out => out.tag === 'TwoWay')
    const threeWayHandicap = outcomes.filter(
        out => out.tag === 'ThreeWayHandicap'
    )
    const OverUnder = outcomes.filter(out => out.tag === 'OverUnder')

    const hasEnoughThreeWay = threeWay.length > 0 ? threeWay.length === 3 : true
    const hasEnoughTwoWay = twoWay.length > 0 ? twoWay.length === 2 : true
    const hasEnoughThreeWayHandicap =
        threeWayHandicap.length > 0 ? threeWayHandicap.length === 3 : true
    const hasEnoughOverUnder =
        OverUnder.length > 0 ? OverUnder.length === 2 : true

    const btnValidator =
        isValid &&
        hasEnoughThreeWay &&
        hasEnoughTwoWay &&
        hasEnoughThreeWayHandicap &&
        hasEnoughOverUnder

    const { sportTranslationsContracts, loadingSportTranslations } =
        useSportTranslationsContext()
    const US =
        !loadingSportTranslations &&
        sportTranslationsContracts.length > 0 &&
        sportTranslationsContracts[0]?.payload?.sportsMap
            ? getTextByLang(
                  'en_uk',
                  sportTranslationsContracts[0]?.payload?.sportsMap
              )
            : []

    if (loadingSportTranslations) {
        return <LoaderSpinner />
    }

    return (
        <form className="editEvent__form">
            <h2>General Data</h2>
            <label>Event Start Date</label>
            <input
                type="datetime-local"
                className="register__input"
                min={new Date().toISOString().slice(0, -8)}
                step="1"
                {...getFieldProps('startDate')}
            />
            <label>Market</label>
            <select
                id="market"
                placeholder="Please select a market option"
                className="register__input"
                value={values.market}
                onChange={e => {
                    setFieldValue('market', e.target.value, true)
                    setFieldValue('sportMarketName', '', false)
                }}
            >
                <option value="" label="Please select a market option">
                    Please select a market option
                </option>
                {MarketOptions.map(option => (
                    <option key={option} value={option} label={option}>
                        {option}
                    </option>
                ))}
            </select>
            {values.market === 'Sport' ? (
                <>
                    <label>Sport Name</label>
                    <select
                        id="sportMarketName"
                        placeholder="Please select a sport option"
                        className="register__input"
                        value={values.sportMarketName}
                        onChange={e => {
                            setFieldValue('sportMarketName', e.target.value)
                        }}
                    >
                        <option value="" label="Please select a sport option">
                            Please select a sport option
                        </option>
                        {React.Children.toArray(
                            US.map((option: Record<string, string>) => (
                                <option
                                    key={Object.keys(option)[0]}
                                    value={Object.keys(option)[0]}
                                >
                                    {Object.values(option)[0]}
                                </option>
                            ))
                        )}
                    </select>
                </>
            ) : null}
            <EventTitle />
            {!isEdit ? (
                <>
                    <label>Description</label>
                    <input
                        type="text"
                        className="register__input"
                        {...getFieldProps('description')}
                    />
                    <label>Tournament Title</label>
                    <input
                        type="text"
                        className="register__input"
                        {...getFieldProps('tournamentTitle')}
                    />
                    <label>Geography</label>
                    <input
                        type="text"
                        className="register__input"
                        {...getFieldProps('geography')}
                    />
                </>
            ) : null}
            <hr />
            <ParticipantsFields />
            <hr />
            <Outcomes />
            <ErrorFeedback />
            <button
                disabled={!btnValidator}
                className="registerbtn__nextStep"
                onClick={e => {
                    e.preventDefault()
                    handleEdit()
                }}
            >
                {isEdit ? 'Edit' : 'Create'}
            </button>
        </form>
    )
}

export default FormEdit
