import React, { useState } from 'react'
import { Field, FieldArray, Formik } from 'formik'
import { CreateEvent } from '@daml/ledger'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import ReactTooltip from 'react-tooltip'

import InputForm from 'Components/Inputs'
import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { getTextByLang } from 'Components/Event/EventCard/SportTranslations.helper'
import ConfirmEventPage from './ConfirmEventPage'

import { useSportTranslationsContext } from 'State/SportTranslationsContext'

import {
    MarketOptions,
    MainOddsOptionTypes,
    WinDrawOptions,
    OverUnderOptions,
    initStateMainOdds,
    initStateOverUnder,
    initStateParticipants,
    initStateThreeWayHandicap,
    formInitValues
} from './constants'

import './style.scss'
import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'
import LoaderSpinner from 'Components/Loader'
import { getYupSchemaValidation } from './utils'
import { getOddMinMaxValues } from 'Components/MyBets/utils'

export const RequiredStar = () => <span className="required">*</span>

const IIconForExample = ({ topic }: { topic: string }) => (
    <>
        <ReactTooltip
            id={`tooltip${topic}`}
            place="top"
            type="dark"
            effect="solid"
            clickable
            class="tooltipMaxWidthMobile"
        />
        <span data-tip={topic} data-for={`tooltip${topic}`}>
            <FontAwesomeIcon icon={faInfoCircle} />
        </span>
    </>
)

/**
 * @deprecated this component should not be used and later will be deleted
 */
export default function CreateEventForm({
    ManagerServiceContract,
    isEventManager
}: {
    ManagerServiceContract:
        | CreateEvent<EventManagerService, EventManagerService.Key, string>[]
        | CreateEvent<
              MarketingManagerService,
              MarketingManagerService.Key,
              string
          >[]
    isEventManager: boolean
}) {
    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader
    } = useGlobalGamblingConfigContext()
    const { sportTranslationsContracts, loadingSportTranslations } =
        useSportTranslationsContext()
    const US =
        !loadingSportTranslations &&
        sportTranslationsContracts.length > 0 &&
        sportTranslationsContracts[0]?.payload?.sportsMap
            ? getTextByLang(
                  'en_uk',
                  sportTranslationsContracts[0]?.payload?.sportsMap
              )
            : []
    const [isConfirmationPage, setIsConfirmationPage] = useState(false)
    const [formValues, setFormValues] =
        useState<typeof formInitValues>(formInitValues)

    if (GlobalGamblingConfigurationLoader) {
        return <LoaderSpinner />
    }

    if (isConfirmationPage) {
        return (
            <ConfirmEventPage
                formValues={formValues}
                ManagerServiceContract={ManagerServiceContract}
                isEventManager={isEventManager}
                handleCancel={() => setIsConfirmationPage(false)}
                deleteValues={() => setFormValues(formInitValues)}
            />
        )
    }

    const value = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'minOdd'
    )
    /*TODO
        @joao.freitas : in the future we can do the disabled button validation in a more elegant way, for example, a function that can receive formik as a prop and return a boolean from it -- it will become much more readable and easier to maintain.
        ex: const isButtonDisabled = (formik) => formik.values.mainOdds[0].participantId.length  && formik.values.mainOdds[1] && formik.values.mainOdds[1] ? false : true
        usage: <button disabled={isButtonDisabled(formik)} className="registerbtn__nextStep" type="submit">Create Event</button>
    */
    return (
        <>
            <Formik
                initialValues={formValues}
                validationSchema={getYupSchemaValidation(value)}
                onSubmit={values => {
                    setFormValues(values)
                    setIsConfirmationPage(true)
                }}
            >
                {formik => (
                    <div className="admin__form">
                        <form onSubmit={formik.handleSubmit}>
                            <div className="formEvent__labelContainer">
                                <label htmlFor="startDate">
                                    Start date <RequiredStar />
                                </label>
                            </div>
                            <input
                                type="datetime-local"
                                className="register__input"
                                min={new Date().toISOString().slice(0, -8)}
                                step="1"
                                {...formik.getFieldProps('startDate')}
                            />
                            <div>
                                {formik.touched.startDate &&
                                formik.errors.startDate ? (
                                    <ErrorMessage
                                        message={formik.errors.startDate}
                                    />
                                ) : null}
                            </div>
                            <div className="formEvent__labelContainer">
                                <label htmlFor="market">
                                    Market <RequiredStar />
                                </label>
                            </div>
                            <select
                                id="market"
                                placeholder="Please select a market option"
                                className="register__input"
                                value={formik.values.market}
                                onChange={e => {
                                    formik.setFieldValue(
                                        'market',
                                        e.target.value
                                    )
                                    formik.setFieldValue('sportMarketName', '')
                                }}
                            >
                                <option
                                    value=""
                                    label="Please select a market option"
                                >
                                    Please select a market option
                                </option>
                                {MarketOptions.map(option => (
                                    <option
                                        key={option}
                                        value={option}
                                        label={option}
                                    >
                                        {option}
                                    </option>
                                ))}
                            </select>
                            <div>
                                {formik.touched.market &&
                                formik.errors.market ? (
                                    <ErrorMessage
                                        message={formik.errors.market}
                                    />
                                ) : null}
                            </div>
                            {formik.values.market === 'Sport' ? (
                                <>
                                    <div className="formEvent__labelContainer">
                                        <label
                                            className="formEvent__label"
                                            htmlFor="sportMarketName"
                                        >
                                            Sport Name <RequiredStar />
                                        </label>
                                    </div>
                                    <select
                                        id="sportMarketName"
                                        placeholder="Please select a sport option"
                                        className="register__input"
                                        {...formik.getFieldProps(
                                            'sportMarketName'
                                        )}
                                    >
                                        <option
                                            value=""
                                            label="Please select a sport option"
                                        >
                                            Please select a sport option
                                        </option>
                                        {React.Children.toArray(
                                            US.map(
                                                (
                                                    option: Record<
                                                        string,
                                                        string
                                                    >
                                                ) => (
                                                    <option
                                                        key={
                                                            Object.keys(
                                                                option
                                                            )[0]
                                                        }
                                                        value={
                                                            Object.keys(
                                                                option
                                                            )[0]
                                                        }
                                                    >
                                                        {
                                                            Object.values(
                                                                option
                                                            )[0]
                                                        }
                                                    </option>
                                                )
                                            )
                                        )}
                                    </select>
                                    {formik.touched.sportMarketName &&
                                    formik.errors.sportMarketName ? (
                                        <ErrorMessage
                                            message={
                                                formik.errors.sportMarketName
                                            }
                                        />
                                    ) : null}
                                </>
                            ) : null}
                            <div className="formEvent__labelContainer">
                                <label
                                    className="formEvent__label"
                                    htmlFor="description"
                                >
                                    Description <RequiredStar />
                                </label>
                            </div>
                            <InputForm
                                id="description"
                                placeholder="Description"
                                type="text"
                                {...formik.getFieldProps('description')}
                            />
                            <div>
                                {formik.touched.description &&
                                formik.errors.description ? (
                                    <ErrorMessage
                                        message={formik.errors.description}
                                    />
                                ) : null}
                            </div>
                            <div className="formEvent__labelContainer">
                                <label
                                    className="formEvent__label"
                                    htmlFor="eventTitle"
                                >
                                    Event Title <RequiredStar />
                                </label>
                            </div>
                            <InputForm
                                id="eventTitle"
                                placeholder="Event Title"
                                type="text"
                                {...formik.getFieldProps('eventTitle')}
                            />
                            <div>
                                {formik.touched.eventTitle &&
                                formik.errors.eventTitle ? (
                                    <ErrorMessage
                                        message={formik.errors.eventTitle}
                                    />
                                ) : null}
                            </div>
                            <div className="formEvent__labelContainer">
                                <label
                                    className="formEvent__label"
                                    htmlFor="tournament"
                                >
                                    Tournament Title <RequiredStar />
                                </label>
                            </div>
                            <InputForm
                                id="tournament"
                                placeholder="Tournament Title"
                                type="text"
                                {...formik.getFieldProps('tournament')}
                            />
                            <div>
                                {formik.touched.tournament &&
                                formik.errors.tournament ? (
                                    <ErrorMessage
                                        message={formik.errors.tournament}
                                    />
                                ) : null}
                            </div>
                            <div className="formEvent__labelContainer">
                                <label
                                    className="formEvent__label"
                                    htmlFor="geography"
                                >
                                    Geography <RequiredStar />
                                </label>
                            </div>
                            <InputForm
                                id="geography"
                                placeholder="Geography"
                                type="text"
                                {...formik.getFieldProps('geography')}
                            />
                            <div>
                                {formik.touched.geography &&
                                formik.errors.geography ? (
                                    <ErrorMessage
                                        message={formik.errors.geography}
                                    />
                                ) : null}
                            </div>
                            {/* EVENT PARTICIPANTS */}
                            {formik.values.startDate.length > 2 &&
                                formik.values.geography.length > 2 && (
                                    <>
                                        <div className="formEvent__labelContainer">
                                            <label
                                                className="formEvent__label"
                                                htmlFor="eventParticipants"
                                            >
                                                Event Participants
                                            </label>
                                        </div>

                                        <FieldArray name="eventParticipants">
                                            {({ remove, push }) => (
                                                <>
                                                    {formik.values
                                                        .eventParticipants
                                                        ?.length > 0 &&
                                                        formik.values.eventParticipants.map(
                                                            (_, index) =>
                                                                React.Children.toArray(
                                                                    <div className="formEvent__fieldArrayContainer">
                                                                        <div className="formEvent__fieldArrayFields">
                                                                            <div className="formEvent__boxContainer">
                                                                                <Field
                                                                                    className="register__input"
                                                                                    name={`eventParticipants.${index}.name`}
                                                                                    placeholder="Participant name"
                                                                                    type="text"
                                                                                />
                                                                                <RequiredStar />
                                                                                <IIconForExample topic="Ex: NY Nets (the name of the participant)" />
                                                                            </div>
                                                                            <div className="formEvent__boxContainer">
                                                                                <Field
                                                                                    className="register__input"
                                                                                    name={`eventParticipants.${index}.id`}
                                                                                    placeholder="Participant id"
                                                                                    type="text"
                                                                                />
                                                                                <RequiredStar />
                                                                                <IIconForExample topic="Ex: 1 (whole number)" />
                                                                            </div>
                                                                            <div className="formEvent__boxContainer">
                                                                                <Field
                                                                                    className="register__input"
                                                                                    name={`eventParticipants.${index}.order`}
                                                                                    placeholder="Participant order"
                                                                                    type="text"
                                                                                />
                                                                                <RequiredStar />
                                                                                <IIconForExample topic="Ex: 1 (whole number)" />
                                                                            </div>
                                                                            <div className="formEvent__boxContainer">
                                                                                <Field
                                                                                    className="register__input"
                                                                                    name={`eventParticipants.${index}.co_op`}
                                                                                    placeholder="Participant co_op"
                                                                                    type="text"
                                                                                />
                                                                                <IIconForExample topic="Ex: 1 (whole number) or empty in case there are no other co-participants" />
                                                                            </div>
                                                                        </div>
                                                                        <div className="formEvent__fieldArrayAction">
                                                                            {index ===
                                                                            0 ? null : (
                                                                                <button
                                                                                    onClick={e => {
                                                                                        e.preventDefault()
                                                                                        remove(
                                                                                            index
                                                                                        )
                                                                                    }}
                                                                                    className="registerbtn__nextStep"
                                                                                >
                                                                                    Remove
                                                                                </button>
                                                                            )}
                                                                            <button
                                                                                onClick={e => {
                                                                                    e.preventDefault()
                                                                                    navigate(
                                                                                        initStateParticipants
                                                                                    )
                                                                                }}
                                                                                className="registerbtn__nextStep"
                                                                            >
                                                                                Add
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                )
                                                        )}
                                                </>
                                            )}
                                        </FieldArray>
                                    </>
                                )}
                            <div>
                                {formik.touched.eventParticipants &&
                                formik.errors.eventParticipants ? (
                                    <ErrorMessage
                                        message={
                                            'Please check the data inputed it seems not to be correct. You need to have at least 2 participants'
                                        }
                                    />
                                ) : null}
                            </div>
                            {/* MAIN ODDS */}
                            {formik.values.startDate.length > 2 &&
                                formik.values.geography.length > 2 &&
                                formik.values.eventParticipants.length >= 2 &&
                                formik.values.eventParticipants[1]?.name && (
                                    <>
                                        <div className="formEvent__labelContainer">
                                            <label className="formEvent__label">
                                                Main Odds
                                            </label>
                                        </div>
                                        <FieldArray name="mainOdds">
                                            {({ remove, push }) => (
                                                <>
                                                    {formik.values.mainOdds
                                                        ?.length > 0 &&
                                                        formik.values.mainOdds.map(
                                                            (_, index) =>
                                                                React.Children.toArray(
                                                                    <div className="formEvent__fieldArrayContainer">
                                                                        <div className="formEvent__fieldArrayFields">
                                                                            <div className="formEvent__boxContainer">
                                                                                <Field
                                                                                    className="register__input"
                                                                                    name={`mainOdds.${index}.participantId`}
                                                                                    placeholder="Participant id"
                                                                                    type="text"
                                                                                />
                                                                                <IIconForExample topic="Ex: 1 (whole number) the id of the participant associated with the odd, for draw should be left empty" />
                                                                            </div>
                                                                            <div className="formEvent__boxContainer">
                                                                                <Field
                                                                                    className="register__input"
                                                                                    name={`mainOdds.${index}.participantOrder`}
                                                                                    placeholder="Participant Order"
                                                                                    type="text"
                                                                                />
                                                                                <RequiredStar />
                                                                                <IIconForExample topic="Ex: 1 (whole number) the order of the participant associated with the odd" />
                                                                            </div>
                                                                            <div className="formEvent__boxContainer">
                                                                                <Field
                                                                                    className="register__input"
                                                                                    name={`mainOdds.${index}.order`}
                                                                                    placeholder="Odd Order"
                                                                                    type="text"
                                                                                />
                                                                                <RequiredStar />
                                                                                <IIconForExample topic="Ex: 1 (whole number) the order of the odd on the screen" />
                                                                            </div>
                                                                            <div className="formEvent__boxContainer">
                                                                                <select
                                                                                    id={`mainOdds.${index}.tag`}
                                                                                    placeholder="Odd option"
                                                                                    className="register__input"
                                                                                    {...formik.getFieldProps(
                                                                                        `mainOdds.${index}.tag`
                                                                                    )}
                                                                                >
                                                                                    <option
                                                                                        value=""
                                                                                        label="Odd option"
                                                                                    >
                                                                                        Odd
                                                                                        option
                                                                                    </option>
                                                                                    {React.Children.toArray(
                                                                                        MainOddsOptionTypes.map(
                                                                                            option => (
                                                                                                <option
                                                                                                    key={
                                                                                                        option
                                                                                                    }
                                                                                                    value={
                                                                                                        option
                                                                                                    }
                                                                                                    label={
                                                                                                        option
                                                                                                    }
                                                                                                >
                                                                                                    {
                                                                                                        option
                                                                                                    }
                                                                                                </option>
                                                                                            )
                                                                                        )
                                                                                    )}
                                                                                </select>
                                                                                <RequiredStar />
                                                                                <IIconForExample topic="Select one type TwoWay or Threeway" />
                                                                            </div>
                                                                            <div className="formEvent__boxContainer">
                                                                                <select
                                                                                    id={`mainOdds.${index}.subtype`}
                                                                                    placeholder="Please select a odd subtype"
                                                                                    className="register__input"
                                                                                    {...formik.getFieldProps(
                                                                                        `mainOdds.${index}.subtype`
                                                                                    )}
                                                                                >
                                                                                    <option
                                                                                        value=""
                                                                                        label="Odd subtype"
                                                                                    >
                                                                                        Odd
                                                                                        subtype
                                                                                    </option>
                                                                                    {React.Children.toArray(
                                                                                        WinDrawOptions.map(
                                                                                            option => (
                                                                                                <option
                                                                                                    key={
                                                                                                        option
                                                                                                    }
                                                                                                    value={
                                                                                                        option
                                                                                                    }
                                                                                                    label={
                                                                                                        option
                                                                                                    }
                                                                                                >
                                                                                                    {
                                                                                                        option
                                                                                                    }
                                                                                                </option>
                                                                                            )
                                                                                        )
                                                                                    )}
                                                                                </select>
                                                                                <RequiredStar />
                                                                                <IIconForExample topic="Select one type Win or Draw" />
                                                                            </div>
                                                                            <div className="formEvent__boxContainer">
                                                                                <Field
                                                                                    className="register__input"
                                                                                    name={`mainOdds.${index}.oddValue`}
                                                                                    placeholder="Odd Value"
                                                                                    type="text"
                                                                                />
                                                                                <RequiredStar />
                                                                                <IIconForExample topic="Ex: 2.55 (a decimal number)" />
                                                                            </div>
                                                                        </div>
                                                                        <div className="formEvent__fieldArrayAction">
                                                                            {index ===
                                                                            0 ? null : (
                                                                                <button
                                                                                    onClick={e => {
                                                                                        e.preventDefault()
                                                                                        remove(
                                                                                            index
                                                                                        )
                                                                                    }}
                                                                                    className="registerbtn__nextStep"
                                                                                >
                                                                                    Remove
                                                                                </button>
                                                                            )}
                                                                            <button
                                                                                onClick={e => {
                                                                                    e.preventDefault()
                                                                                    navigate(
                                                                                        initStateMainOdds
                                                                                    )
                                                                                }}
                                                                                className="registerbtn__nextStep"
                                                                            >
                                                                                Add
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                )
                                                        )}
                                                </>
                                            )}
                                        </FieldArray>
                                        <div>
                                            {formik.touched.mainOdds &&
                                                formik.errors.mainOdds && (
                                                    <ErrorMessage
                                                        message={
                                                            'Please check the data inputed it seems not to be correct. You need to have at least 2 odds and a max of 3, odd value must be a decimal number (ex: 2.55).'
                                                        }
                                                    />
                                                )}
                                        </div>
                                    </>
                                )}
                            {/* OVER/UNDER ODDS */}
                            {formik.values.startDate.length > 2 &&
                                formik.values.geography.length > 2 &&
                                formik.values.eventParticipants.length >= 2 &&
                                formik.values.eventParticipants[1]?.name &&
                                formik.values.mainOdds[0]?.participantId
                                    .length &&
                                formik.values.mainOdds[1]?.participantId
                                    .length && (
                                    <>
                                        <div className="formEvent__flexContainer">
                                            <label
                                                className="formEvent__label"
                                                htmlFor="hasOverUnder"
                                            >
                                                Has over under odds?
                                            </label>

                                            <input
                                                type="checkbox"
                                                checked={
                                                    formik.values.hasOverUnder
                                                }
                                                onChange={() => {
                                                    formik.setFieldValue(
                                                        'hasOverUnder',
                                                        !formik.values
                                                            .hasOverUnder
                                                    )
                                                    formik.setFieldValue(
                                                        'overUnder',
                                                        [initStateOverUnder]
                                                    )
                                                }}
                                            />
                                        </div>
                                        {formik.values.hasOverUnder ? (
                                            <>
                                                <div className="formEvent__labelContainer">
                                                    <label className="formEvent__label">
                                                        Over/Under Odds
                                                    </label>
                                                </div>
                                                <FieldArray name="overUnder">
                                                    {({
                                                        remove: removeOverUnder,
                                                        push: pushOverUnder
                                                    }) => (
                                                        <>
                                                            {formik.values
                                                                .overUnder
                                                                ?.length > 0 &&
                                                                formik.values.overUnder.map(
                                                                    (
                                                                        _,
                                                                        index
                                                                    ) =>
                                                                        React.Children.toArray(
                                                                            <div className="formEvent__fieldArrayContainer">
                                                                                <div className="formEvent__fieldArrayFields">
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <Field
                                                                                            className="register__input"
                                                                                            name={`overUnder.${index}.order`}
                                                                                            placeholder="Odd Order on screen"
                                                                                            type="text"
                                                                                        />
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: 1 (whole number) the odd order on screen" />
                                                                                    </div>
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <Field
                                                                                            className="register__input"
                                                                                            name={`overUnder.${index}.valueOfTag`}
                                                                                            placeholder="Value of under/over"
                                                                                            type="text"
                                                                                        />
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: 2.5 (decimal number) the value of the Over/Under Handicap" />
                                                                                    </div>
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <select
                                                                                            id={`overUnder.${index}.subtype`}
                                                                                            placeholder="Please select a odd subtype"
                                                                                            className="register__input"
                                                                                            {...formik.getFieldProps(
                                                                                                `overUnder.${index}.subtype`
                                                                                            )}
                                                                                        >
                                                                                            <option
                                                                                                value=""
                                                                                                label="Odd subtype"
                                                                                            >
                                                                                                Odd
                                                                                                subtype
                                                                                            </option>
                                                                                            {OverUnderOptions.map(
                                                                                                option => (
                                                                                                    <option
                                                                                                        key={
                                                                                                            option
                                                                                                        }
                                                                                                        value={
                                                                                                            option
                                                                                                        }
                                                                                                        label={
                                                                                                            option
                                                                                                        }
                                                                                                    >
                                                                                                        {
                                                                                                            option
                                                                                                        }
                                                                                                    </option>
                                                                                                )
                                                                                            )}
                                                                                        </select>
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: Over or Under, if the odd is Under an Over" />
                                                                                    </div>
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <Field
                                                                                            className="register__input"
                                                                                            name={`overUnder.${index}.oddValue`}
                                                                                            placeholder="Odd Value"
                                                                                            type="text"
                                                                                        />
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: 2.55 (decimal number), the odd value" />
                                                                                    </div>
                                                                                </div>
                                                                                <div className="formEvent__fieldArrayAction">
                                                                                    {index ===
                                                                                    0 ? null : (
                                                                                        <button
                                                                                            onClick={e => {
                                                                                                e.preventDefault()
                                                                                                removeOverUnder(
                                                                                                    index
                                                                                                )
                                                                                            }}
                                                                                            className="registerbtn__nextStep"
                                                                                        >
                                                                                            Remove
                                                                                        </button>
                                                                                    )}
                                                                                    <button
                                                                                        onClick={e => {
                                                                                            e.preventDefault()
                                                                                            pushOverUnder(
                                                                                                initStateOverUnder
                                                                                            )
                                                                                        }}
                                                                                        className="registerbtn__nextStep"
                                                                                    >
                                                                                        Add
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        )
                                                                )}
                                                        </>
                                                    )}
                                                </FieldArray>
                                            </>
                                        ) : null}
                                        <div>
                                            {formik.values.hasOverUnder &&
                                            formik.touched.overUnder &&
                                            formik.errors.overUnder ? (
                                                <ErrorMessage
                                                    message={
                                                        'Please check the data inputed it seems not to be correct. You need to have at least 2 odds, odd value and value of tag must be a decimal number (ex: 2.55).'
                                                    }
                                                />
                                            ) : null}
                                        </div>
                                        {/* THREEWAY HANDICAP */}
                                        <div className="formEvent__flexContainer">
                                            <label
                                                className="formEvent__label"
                                                htmlFor="hasThreewayHandicap"
                                            >
                                                Has Three Way Handicap odds?
                                            </label>
                                            <input
                                                type="checkbox"
                                                checked={
                                                    formik.values
                                                        .hasThreewayHandicap
                                                }
                                                onChange={() => {
                                                    formik.setFieldValue(
                                                        'hasThreewayHandicap',
                                                        !formik.values
                                                            .hasThreewayHandicap
                                                    )
                                                    formik.setFieldValue(
                                                        'threeWayHandicap',
                                                        [
                                                            initStateThreeWayHandicap
                                                        ]
                                                    )
                                                }}
                                            />
                                        </div>
                                        {formik.values.hasThreewayHandicap ? (
                                            <>
                                                <div className="formEvent__labelContainer">
                                                    <label className="formEvent__label">
                                                        Three Way Handicap Odds
                                                    </label>
                                                </div>
                                                <FieldArray name="threeWayHandicap">
                                                    {({ remove, push }) => (
                                                        <>
                                                            {formik.values
                                                                .threeWayHandicap
                                                                ?.length > 0 &&
                                                                formik.values.threeWayHandicap.map(
                                                                    (
                                                                        _,
                                                                        index
                                                                    ) =>
                                                                        React.Children.toArray(
                                                                            <div className="formEvent__fieldArrayContainer">
                                                                                <div className="formEvent__fieldArrayFields">
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <Field
                                                                                            className="register__input"
                                                                                            name={`threeWayHandicap.${index}.participantId`}
                                                                                            placeholder="Participant id"
                                                                                            type="text"
                                                                                        />
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: 1 (whole number)" />
                                                                                    </div>
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <Field
                                                                                            className="register__input"
                                                                                            name={`threeWayHandicap.${index}.participantOrder`}
                                                                                            placeholder="Participant Order"
                                                                                            type="text"
                                                                                        />
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: 1 (whole number) the participant order" />
                                                                                    </div>
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <Field
                                                                                            className="register__input"
                                                                                            name={`threeWayHandicap.${index}.order`}
                                                                                            placeholder="Odd Order"
                                                                                            type="text"
                                                                                        />
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: 1 (whole number) the odd order on screen" />
                                                                                    </div>
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <Field
                                                                                            className="register__input"
                                                                                            name={`threeWayHandicap.${index}.valueOfTag`}
                                                                                            placeholder="Handicap Value"
                                                                                            type="text"
                                                                                        />
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: -1 (a negative or posivite number) the value of the handicap" />
                                                                                    </div>
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <select
                                                                                            id={`threeWayHandicap.${index}.subtype`}
                                                                                            placeholder="Please select a odd subtype"
                                                                                            className="register__input"
                                                                                            {...formik.getFieldProps(
                                                                                                `threeWayHandicap.${index}.subtype`
                                                                                            )}
                                                                                        >
                                                                                            <option
                                                                                                value=""
                                                                                                label="Odd subtype"
                                                                                            >
                                                                                                Odd
                                                                                                subtype
                                                                                            </option>
                                                                                            {WinDrawOptions.map(
                                                                                                option => (
                                                                                                    <option
                                                                                                        key={
                                                                                                            option
                                                                                                        }
                                                                                                        value={
                                                                                                            option
                                                                                                        }
                                                                                                        label={
                                                                                                            option
                                                                                                        }
                                                                                                    >
                                                                                                        {
                                                                                                            option
                                                                                                        }
                                                                                                    </option>
                                                                                                )
                                                                                            )}
                                                                                        </select>
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: Win or Draw" />
                                                                                    </div>
                                                                                    <div className="formEvent__boxContainer">
                                                                                        <Field
                                                                                            className={`register__input`}
                                                                                            name={`threeWayHandicap.${index}.oddValue`}
                                                                                            placeholder="Odd Value"
                                                                                            type="text"
                                                                                        />
                                                                                        <RequiredStar />
                                                                                        <IIconForExample topic="Ex: 2.55 (decimal number) the value of the odd" />
                                                                                    </div>
                                                                                </div>
                                                                                <div className="formEvent__fieldArrayAction">
                                                                                    {index ===
                                                                                    0 ? null : (
                                                                                        <button
                                                                                            onClick={e => {
                                                                                                e.preventDefault()
                                                                                                remove(
                                                                                                    index
                                                                                                )
                                                                                            }}
                                                                                            className="registerbtn__nextStep"
                                                                                        >
                                                                                            Remove
                                                                                        </button>
                                                                                    )}
                                                                                    <button
                                                                                        onClick={e => {
                                                                                            e.preventDefault()
                                                                                            navigate(
                                                                                                initStateThreeWayHandicap
                                                                                            )
                                                                                        }}
                                                                                        className="registerbtn__nextStep"
                                                                                    >
                                                                                        Add
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        )
                                                                )}
                                                        </>
                                                    )}
                                                </FieldArray>
                                            </>
                                        ) : null}
                                        <div>
                                            {formik.values
                                                .hasThreewayHandicap &&
                                            formik.touched.threeWayHandicap &&
                                            formik.errors.threeWayHandicap ? (
                                                <ErrorMessage
                                                    message={
                                                        'Please check the data inputed it seems not to be correct. You need to have at least 3 odds, odd value must be a decimal number (ex: 2.55) and Handicap value should be a positive or negative number (ex: 2 or -2).'
                                                    }
                                                />
                                            ) : null}
                                        </div>
                                    </>
                                )}
                            <button
                                disabled={
                                    !formik.values.mainOdds[0].participantId
                                        .length &&
                                    !formik.values.mainOdds[1] &&
                                    !formik.values.mainOdds[1]
                                }
                                className="registerbtn__nextStep"
                                type="submit"
                            >
                                Create Event
                            </button>
                        </form>
                    </div>
                )}
            </Formik>
        </>
    );
}
