export const MarketOptions = ["Entertainment", "Politics", "Sport"]
export const MainOddsOptionTypes = ["TwoWay", "ThreeWay"]
export const WinDrawOptions = ["Win", "Draw"]
export const OverUnderOptions = ['Over', 'Under']
export const initStateParticipants = { name: '', id: '', order: '', co_op: '' }
export const initStateMainOdds = {
    participantId: '',
    participantOrder: '',
    tag: '',
    subtype: '',
    order: '',
    oddValue: ''
}
export const initStateOverUnder = {
    participantId: null,
    participantOrder: '',
    tag: 'OverUnder',
    valueOfTag: '',
    subtype: '',
    order: '',
    oddValue: ''
}
export const initStateThreeWayHandicap = {
    participantId: '',
    participantOrder: '',
    tag: 'ThreeWayHandicap',
    valueOfTag: '',
    subtype: '',
    order: '',
    oddValue: ''
}

export const formInitValues = {
    startDate: '',
    eventTitle: '',
    description: '',
    tournament: '',
    geography: '',
    market: '',
    sportMarketName: '',
    eventParticipants: [initStateParticipants],
    mainOdds: [initStateMainOdds],
    hasOverUnder: false,
    overUnder: [initStateOverUnder],
    hasThreewayHandicap: false,
    threeWayHandicap: [initStateThreeWayHandicap]
}


export const negativeDecimalRegex = /^-?\d*\.?\d{0,2}$/;
export const regexDecimal = /^\d*\.?\d{0,2}$/;
