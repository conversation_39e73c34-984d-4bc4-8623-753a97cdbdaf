import * as Yup from 'yup'
import moment from 'moment-timezone'

import { OutcomeType } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'
import { InputOutcomeOdd } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { OutcomeSubType } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'

import { regexDecimal, negativeDecimalRegex } from "./constants"

export const generateAssetLabel = () => Math.floor(Math.random() * 1000000000)

export const marketGenerator = (market: string, sportMarketName: string): { tag: 'Sport'; value: string }
    | { tag: 'Politics'; value: {} }
    | { tag: 'Entertainment'; value: {} } => {
    if (market === "Politics") {
        return {
            tag: "Politics",
            value: {}
        }
    }
    if (market === "Entertainment") {
        return {
            tag: "Entertainment",
            value: {}
        }
    }
    return {
        tag: "Sport",
        value: sportMarketName
    }
}

export const generateParticipants = (eventParticipants: { name: string, id: string, order: string, co_op: string }[]) => {
    return eventParticipants.map(({ name, id, co_op, order }) =>
        ({ name, id, order, co_op: co_op.length > 0 ? [co_op] : null }))
}

export const generateType_ = (tag: string, value: string): OutcomeType => {
    if (tag === "ThreeWay") {
        return { tag: 'ThreeWay', value: {} }
    }
    if (tag === "TwoWay") {
        return { tag: 'TwoWay', value: {} }
    }
    if (tag === "OverUnder") {
        return { tag: 'OverUnder', value: value }
    }
    if (tag === "ThreeWayHandicap") {
        return { tag: 'ThreeWayHandicap', value: value }
    }
    return { tag: 'ThreeWay', value: {} }
}

export const generateMainOutcomes = (mainOdds: { oddValue: string, order: string, participantId: string, participantOrder: string, subtype: string, tag: string }[]): InputOutcomeOdd[] => {
    return mainOdds.map(({ oddValue, order, participantId, participantOrder, subtype, tag }) => {
        return {
            odd: {
                tag: "Decimal",
                value: oddValue
            },
            outcome: {
                participantId: participantId.length > 0 ? participantId : null,
                participantOrder,
                order,
                subtype: subtype as OutcomeSubType,
                type_: generateType_(tag, "")
            }
        }
    })
}

export const generateOverUnderOutcomes = (hasOverUnder: boolean, overUnder: { oddValue: string, order: string, subtype: string, tag: string, valueOfTag: string }[]): InputOutcomeOdd[] => {
    if (hasOverUnder) {
        return overUnder.map(({ oddValue, order, subtype, tag, valueOfTag }) => {
            return {
                odd: {
                    tag: "Decimal",
                    value: oddValue
                },
                outcome: {
                    participantId: null,
                    participantOrder: "0",
                    order,
                    subtype: subtype as OutcomeSubType,
                    type_: generateType_(tag, valueOfTag)
                }
            }

        })
    }
    return []
}

export const generateThreeWayHandicap = (hasThreewayHandicap: boolean, threeWayHandicap: {
    participantId: string;
    participantOrder: string;
    tag: string;
    valueOfTag: string;
    subtype: string;
    order: string;
    oddValue: string;
}[]): InputOutcomeOdd[] => {
    if (hasThreewayHandicap) {
        return threeWayHandicap.map(({ oddValue, order, subtype, tag, valueOfTag, participantId, participantOrder }) => {
            return {
                odd: {
                    tag: "Decimal",
                    value: oddValue
                },
                outcome: {
                    participantId,
                    participantOrder,
                    order,
                    subtype: subtype as OutcomeSubType,
                    type_: generateType_(tag, valueOfTag)
                }
            }

        })
    }
    return []
}

export const getYupSchemaValidation = (minOddValue: string) => {
    return Yup.object({
        eventTitle: Yup.string()
            .min(1)
            .required('Event title is a required field'),
        description: Yup.string()
            .min(1)
            .required('Description is a required  field'),
        tournament: Yup.string()
            .min(1)
            .required('Tournament is a required  field'),
        geography: Yup.string()
            .min(1)
            .required('Geography is a required  field'),
        market: Yup.string()
            .min(1)
            .required('Market is a required  field'),
        sportMarketName: Yup.string().when("market", {
            is: (market: any) => market === "Sport",
            then: Yup.string().min(1)
                .required('Sport Name is a required  field'),
        }),
        startDate: Yup.date()
            .test('startDate', 'It should be an hour later', function (val) {
                return moment(val).isAfter(moment().add(1, "hours"))
            })
            .required('Start date is a required  field'),
        eventParticipants: Yup.array().of(
            Yup.object().shape({
                name: Yup.string()
                    .min(1)
                    .required('name is a required  field'),
                id: Yup.string()
                    .min(1)
                    .required('id is a required  field'),
                order: Yup.string()
                    .min(1)
                    .required('order is a required  field'),
                co_op: Yup.string(),
            })
        ).min(2, "You need to have at least 2 participants"),
        mainOdds: Yup.array().of(
            Yup.object().shape({
                participantId: Yup.string(),
                participantOrder: Yup.string(),
                tag: Yup.string(),
                subtype: Yup.string(),
                order: Yup.string(),
                oddValue: Yup.string().matches(regexDecimal, 'Please add a valid Odd').test(val => Number(val) > Number(minOddValue)).required(),
            })
        ).min(2, "You need to have at least 2 odds and odd value must be a decimal number (ex: 2.55)").max(3, "You can have only 3 odds max and odd value must be a decimal number (ex: 2.55)"),
        hasOverUnder: Yup.boolean(),
        overUnder: Yup.array().when("hasOverUnder", {
            is: true,
            then: Yup.array().of(
                Yup.object().shape({
                    participantId: Yup.string().nullable(),
                    participantOrder: Yup.number(),
                    tag: Yup.string(),
                    valueOfTag: Yup.string().matches(regexDecimal, 'Please add a valid value').required(),
                    subtype: Yup.string(),
                    order: Yup.string(),
                    oddValue: Yup.string().matches(regexDecimal, 'Please add a valid Odd').test(val => Number(val) > Number(minOddValue)).required(),
                })
            ).min(2, "You need to have at least 2 odds, odd value and value of tag must be a decimal number (ex: 2.55).")
        }),
        hasThreewayHandicap: Yup.boolean(),
        threeWayHandicap: Yup.array().when("hasThreewayHandicap", {
            is: true,
            then: Yup.array().of(
                Yup.object().shape({
                    participantId: Yup.string().nullable(),
                    participantOrder: Yup.number(),
                    tag: Yup.string(),
                    valueOfTag: Yup.string().matches(negativeDecimalRegex, 'Please add a valid value').required(),
                    subtype: Yup.string().required(),
                    order: Yup.string().required(),
                    oddValue: Yup.string().matches(regexDecimal, 'Please add a valid Odd').test(val => Number(val) > Number(minOddValue)).required(),
                })
            ).min(3, "You need to have at least 3 odds, odd value must be a decimal number (ex: 2.55) and value of tag should be a positive or negative number (ex: 2 or -2).")
        }),
    })
}

export const updateResultValidationSchema = () => Yup.object({
    eventStatus: Yup.string().required('Event status is a required field'),
    eventParticipants: Yup.array().of(
        Yup.object().shape({
            name: Yup.string()
                .min(1)
                .required('name is a required  field'),
            id: Yup.string()
                .min(1)
                .required('id is a required  field'),
            order: Yup.string()
                .min(1)
                .required('order is a required  field'),
            result: Yup.string()
                .min(1)
                .required('result is a required  field'),
        })
    ).min(2, "You need to have at least 2 participants").optional(),
})
