//FORM PAGE STYLES

.formEvent {
    &__boxContainer {
        display: flex;
        gap: 2px;
        align-items: center;
        .required {
            font-weight: bold;
            color: red;
        }
    }

    &__labelContainer {
        font-weight: bold;
        padding: 10px 0 5px 0;
        .required {
            font-weight: bold;
            color: red;
        }
    }

    &__flexContainer {
        display: flex;
        justify-content: flex-start;
        gap: 20px;
        padding: 10px 0;
    }

    &__fieldArrayContainer {
        display: flex;
        gap: 20px;
    }

    &__fieldArrayFields {
        flex: 0 1 85%;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }

    &__fieldArrayAction {
        display: flex;
        flex: 1;
        gap: 10px;
    }


}

//CONFIRMATION PAGE STYLES
.displayArrayDataContainer {
    display: flex;
    justify-content: flex-start;
    gap: 20px;
    align-items: center;
    margin: 0 0 20px 0;
    padding: 10px 0px;
    text-transform: capitalize;
}

/* .displayArrayDataContainer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    padding: 20px 0;
} */

.mainEventInformationContainer {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px 0;

}

.mainEventInformation {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
    padding: 10px 0;
}

.displayDataContainer {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    grid-template-rows: repeat(2, 1fr);
    grid-column-gap: 20px;
    grid-row-gap: 20px;

    padding: 10px 0px;

    .registerbtn__previousStep {
        margin-top: 20px;
    }

}
