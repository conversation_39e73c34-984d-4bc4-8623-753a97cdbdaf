import React from 'react'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
//import { useLedger } from '@daml/react'
import { CreateEvent } from '@daml/ledger'
//import { toast } from 'react-toastify'

import { formInitValues } from '../constants'
// import {
//     generateAssetLabel,
//     marketGenerator,
//     generateParticipants,
//     generateMainOutcomes,
//     generateOverUnderOutcomes,
//     generateThreeWayHandicap
// } from '../utils'

import LoaderSpinner from 'Components/Loader'

import '../style.scss'

//MISSING APPLYING STYLES
const DisplayArrayDataContainer = ({
    children
}: {
    children: React.ReactNode
}) => <span className="displayArrayDataContainer">{children}</span>
const DisplayDataContainer = ({ children }: { children: React.ReactNode }) => (
    <span className="displayDataContainer">{children}</span>
)

const MainEventInformationContainer = ({
    children
}: {
    children: React.ReactNode
}) => <div className="mainEventInformationContainer">{children}</div>

const MainEventInformation = ({ children }: { children: React.ReactNode }) => (
    <div className="mainEventInformation">{children}</div>
)
//CHANGES HERE
export default function ConfirmEventPage({
    formValues,
    handleCancel,
    isEventManager,
    ManagerServiceContract,
    deleteValues
}: {
    deleteValues: () => void
    formValues: typeof formInitValues
    handleCancel: () => void
    isEventManager: boolean
    ManagerServiceContract:
        | CreateEvent<EventManagerService, EventManagerService.Key, string>[]
        | CreateEvent<
              MarketingManagerService,
              MarketingManagerService.Key,
              string
          >[]
}) {
    const [isLoading] = React.useState(false)
    //const ledger = useLedger()

    // function handleAnswer() {
    //     deleteValues()
    //     setIsLoading(false)
    //     handleCancel()
    // }

    function handleConfirmation() {
        // if (formValues) {
        //     setIsLoading(true)
        //     let choice = isEventManager
        //         ? EventManagerService.RequestEventOrigination
        //         : MarketingManagerService.RequestEventOrigination
        //     ledger
        //         .exercise(choice, ManagerServiceContract[0].contractId, {
        //             eventOrigin: {
        //                 tag: 'CustomerEvent',
        //                 value: {
        //                     description: formValues.description,
        //                     assetLabel: generateAssetLabel().toString(),
        //                     market: marketGenerator(
        //                         formValues.market,
        //                         formValues.sportMarketName ?? ''
        //                     ),
        //                     submarkets: [
        //                         {
        //                             tag: 'Tournament',
        //                             value: formValues.tournament
        //                         }
        //                     ],
        //                     geography: {
        //                         tag: 'Geography',
        //                         value: formValues.geography
        //                     },
        //                     //CHANGE HERE LOGIC
        //                     eventTitle: ,
        //                     startDate: new Date(
        //                         formValues.startDate
        //                     ).toISOString(),
        //                     eventParticipants: generateParticipants(
        //                         formValues.eventParticipants
        //                     ),
        //                     outcomes: [
        //                         ...generateMainOutcomes(formValues.mainOdds),
        //                         ...generateOverUnderOutcomes(
        //                             formValues.hasOverUnder,
        //                             formValues.overUnder
        //                         ),
        //                         ...generateThreeWayHandicap(
        //                             formValues.hasThreewayHandicap,
        //                             formValues.threeWayHandicap
        //                         )
        //                     ],
        //                     eventStatus: 'NotStarted',
        //                     eventResults: []
        //                 }
        //             }
        //         })
        //         .then(() => {
        //             toast.success('Event created sucessfuly')
        //             handleAnswer()
        //         })
        //         .catch(() => {
        //             toast.error('something went wrong')
        //             handleCancel()
        //         })
        // }
        return
    }

    if (isLoading) {
        return <LoaderSpinner />
    }

    if (formValues) {
        return (
            <MainEventInformationContainer>
                <h2>Please review your data:</h2>
                <MainEventInformation>
                    <h3>Start Date:</h3>
                    <p>{formValues.startDate.replace('T', ', ')}</p>
                </MainEventInformation>
                <MainEventInformation>
                    <h3>Event title:</h3>
                    {/* <p>{formValues}</p> */}
                </MainEventInformation>
                <MainEventInformation>
                    <h3>Description:</h3>
                    <p>{formValues.description}</p>
                </MainEventInformation>
                <MainEventInformation>
                    <h3>Market:</h3>
                    <p>{formValues.market}</p>
                </MainEventInformation>
                {formValues.sportMarketName ? (
                    <MainEventInformation>
                        <h3>Sport Market Name:</h3>
                        <p>{formValues.sportMarketName}</p>
                    </MainEventInformation>
                ) : null}
                <MainEventInformation>
                    <h3>Tournament:</h3>
                    <p>{formValues.tournament}</p>
                </MainEventInformation>
                <h3>Event Participants</h3>
                {React.Children.toArray(
                    formValues.eventParticipants.map(ep => {
                        return (
                            <DisplayArrayDataContainer>
                                <p>id: {ep.id}</p>
                                <p>name: {ep.name}</p>
                                <p>order: {ep.order}</p>
                                {ep.co_op.length > 0 ? (
                                    <p>co_op: {ep.co_op}</p>
                                ) : null}
                            </DisplayArrayDataContainer>
                        )
                    })
                )}
                <h3>Main odds</h3>
                {React.Children.toArray(
                    formValues.mainOdds.map(ep => {
                        return (
                            <DisplayArrayDataContainer>
                                <p>odd value: {ep.oddValue}</p>
                                <p>participant id: {ep.participantId}</p>
                                <p>participant order: {ep.participantOrder}</p>
                                <p>subtype: {ep.subtype}</p>
                                <p>order: {ep.order}</p>
                                <p>tag: {ep.tag}</p>
                            </DisplayArrayDataContainer>
                        )
                    })
                )}
                {formValues.hasOverUnder ? (
                    <>
                        <h3>Over Under odds</h3>
                        {React.Children.toArray(
                            formValues.overUnder.map(ep => {
                                return (
                                    <DisplayArrayDataContainer>
                                        <p>odd value: {ep.oddValue}</p>
                                        <p>
                                            participant id: {ep.participantId}
                                        </p>
                                        <p>
                                            participant order:{' '}
                                            {ep.participantOrder}
                                        </p>
                                        <p>subtype: {ep.subtype}</p>
                                        <p>order: {ep.order}</p>
                                        <p>tag: {ep.tag}</p>
                                        <p>value of tag: {ep.valueOfTag}</p>
                                    </DisplayArrayDataContainer>
                                )
                            })
                        )}{' '}
                    </>
                ) : null}
                {formValues.hasThreewayHandicap ? (
                    <>
                        <h3>Threeway Handicap odds</h3>
                        {React.Children.toArray(
                            formValues.threeWayHandicap.map(ep => {
                                return (
                                    <DisplayArrayDataContainer>
                                        <p>odd value: {ep.oddValue}</p>
                                        <p>
                                            participant id: {ep.participantId}
                                        </p>
                                        <p>
                                            participant order:{' '}
                                            {ep.participantOrder}
                                        </p>
                                        <p>subtype: {ep.subtype}</p>
                                        <p>order: {ep.order}</p>
                                        <p>tag: {ep.tag}</p>
                                        <p>value of tag: {ep.valueOfTag}</p>
                                    </DisplayArrayDataContainer>
                                )
                            })
                        )}{' '}
                    </>
                ) : null}
                <DisplayDataContainer>
                    <button
                        className="registerbtn__nextStep"
                        onClick={() => handleConfirmation()}
                    >
                        Submit
                    </button>
                    <button
                        className="registerbtn__previousStep width100 "
                        onClick={() => handleCancel()}
                    >
                        Cancel
                    </button>
                </DisplayDataContainer>
            </MainEventInformationContainer>
        )
    }
    return <div>no data to see</div>
}
