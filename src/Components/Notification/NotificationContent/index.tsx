import React from 'react'
import useOutsideDetector from 'Hooks/useOutsideDetector'
import { CreateEvent } from '@daml/ledger'
import {
    ActionSuccess,
    Actionable
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { useLedger } from '@daml/react'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import '../style.scss'

export default function NotificationContent({
    content,
    party,
    parentRef,
    handleClickOutside
}: {
    content: CreateEvent<ActionSuccess, ActionSuccess.Key, string | any>[] | any
    party: string
    parentRef: any
    handleClickOutside: any
}) {
    const ledger = useLedger()

    const handleNotificationClick = async (
        action: Actionable,
        actionId: string
    ) => {
        try {
            const serviceContracts = await ledger.query(Service, {
                customer: party
            })
            return await ledger.exercise(
                Service.ArchiveActionSuccessNotification,
                serviceContracts[0].contractId,
                {
                    actionList: [
                        {
                            _1: action,
                            _2: actionId
                        }
                    ]
                }
            )
        } catch (error) {
            console.error('error notification request', error)
        }
    }

    const handleAllRead = async (
        notifications: CreateEvent<
            ActionSuccess,
            ActionSuccess.Key,
            string | any
        >[]
    ) => {
        try {
            const listIds = notifications.map(notification => ({
                _1: notification.payload.action,
                _2: notification.payload.actionId
            }))
            const serviceContracts = await ledger.query(Service, {
                customer: party
            })
            return await ledger.exercise(
                Service.ArchiveActionSuccessNotification,
                serviceContracts[0].contractId,
                {
                    actionList: listIds
                }
            )
        } catch (error) {
            console.error('error all notification request', error)
        }
    }

    const popupRef = React.useRef() as React.MutableRefObject<HTMLUListElement>

    useOutsideDetector(popupRef, handleClickOutside, parentRef)

    return (
        <ul ref={popupRef} className="notification__submenu">
            {content && content.length ? (
                <>
                    <div className="notification__submenu-container">
                        {content.map(
                            (
                                notification: CreateEvent<
                                    ActionSuccess,
                                    ActionSuccess.Key,
                                    string | any
                                >
                            ) => (
                                <li
                                    className="notification__submenu-item"
                                    key={notification.contractId}
                                >
                                    <div className="notification__submenu-info">
                                        <p>{notification.payload.reason}</p>
                                    </div>
                                    <div className="notification__submenu-action">
                                        <span
                                            onClick={() =>
                                                handleNotificationClick(
                                                    notification.payload.action,
                                                    notification.payload
                                                        .actionId
                                                )
                                            }
                                        >
                                            OK
                                        </span>
                                    </div>
                                </li>
                            )
                        )}
                    </div>
                    <li>
                        <button
                            className="notification__submenu-button"
                            onClick={() => handleAllRead(content)}
                        >
                            Mark all as read
                        </button>
                    </li>
                </>
            ) : (
                <li className="notification__submenu-item">
                    <p> No notifications to show</p>
                </li>
            )}
        </ul>
    )
}
