@import '../../Styles/colors';

.notification {
    &__loader {
        max-height: 355px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    &__item {
        position: relative;
        background-color: transparent;
        border: none;
        color: $white;

        svg {
            position: relative;
        }

        &-bagde {
            height: 15px;
            width: 15px;
            text-align: center;
            font-size: 0.7rem;
            background-color: $orange;
            border-radius: 50%;
            display: inline-block;
            position: absolute;
            top: -5px;
            left: -5px;
        }
    }

    &__submenu {
        padding: 15px;
        background: $white;
        border: 1px solid $grayInpuBorder;
        font-family: OpenSans, sans-serif;
        font-size: 0.75rem;
        line-height: 25px;
        color: $darkGrey;
        border-radius: 6px;
        list-style: none;
        margin: 0;
        position: absolute;
        top: calc(100% + 30px);
        left: -50%;
        width: 300px;

        @media (max-width: 1082px) {
            left: -850%;
        }

        &-container {
            max-height: 355px;
            overflow-y: scroll;
            padding-right: 15px;

            /* width */
            &::-webkit-scrollbar {
                width: 3px;
            }

            /* Track */
            &::-webkit-scrollbar-track {
                background: #f1f1f1;
            }

            /* Handle */
            &::-webkit-scrollbar-thumb {
                background: #888;
            }

            /* Handle on hover */
            &::-webkit-scrollbar-thumb:hover {
                background: #555;
            }
        }

        &-button {
            width: 100%;
            margin-top: 10px;
            color: $white;
            cursor: pointer;
            text-decoration: none;
            padding: 2px;
            text-align: center;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-color: transparent;
            border: 1px solid transparent;
            font-size: 0.8rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            transition: color 0.15s ease-in-out,
                background-color 0.15s ease-in-out,
                border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            font-family: 'Montserrat-Bold', sans-serif;
            background: $successgreen;
            border-color: $successgreen;

            &:disabled {
                border: 1px solid #d3dce5;
                border-radius: 6px;
                background: #efefef;
                color: #8298ab;
                cursor: not-allowed;
            }

            &:hover {
                opacity: 0.8;
            }
        }

        &-item {
            border-bottom: 1px solid $grayInpuBorder;
            padding: 10px 0px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 250px;

            &:last-child {
                border-bottom: none;
            }

            &:not(:last-child) {
                margin-bottom: rem(10);
            }
        }

        &-info {
            font-size: 0.8rem;
            padding: 0px 5px;
            flex: 1 0 70%;
            max-width: 170px;
            word-wrap: break-word;
        }

        &-action {
            flex: 1 0 30%;

            span {
                background: $successgreen;
                color: $white;
                font-weight: bold;
                cursor: pointer;
                border-radius: 6px;
                padding: 4px 7px;
                font-size: 0.7rem;
            }
        }
    }
}
