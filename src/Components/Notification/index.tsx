import React from 'react'
import useMediaQuery from 'Hooks/useMediaQuery'
import { ActionSuccess } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { useParty, useStreamQueries } from '@daml/react'
import NotificationBell from 'Assets/notificationBell'
import NotificationContent from './NotificationContent'
import { useTranslation } from 'react-i18next'

import './style.scss'

export default function Notifications() {
    const { t } = useTranslation()
    const [isOpen, setIsOpen] = React.useState(false)
    const party = useParty()
    const spanNotification = React.useRef() as any

    const displayName = useMediaQuery('(max-width: 1082px)')

    const {
        contracts: cancelNotification,
        loading: isLoadingCancelNotification
    } = useStreamQueries(ActionSuccess, () => [{ customer: party }], [party])

    return (
        <button
            className="header__action notification__item"
            onClick={() => setIsOpen(!isOpen)}
            style={{ cursor: 'pointer' }}
            ref={spanNotification}
        >
            <NotificationBell />
            {!isLoadingCancelNotification && cancelNotification.length > 0 ? (
                <span className="notification__item-bagde">
                    {cancelNotification.length}
                </span>
            ) : null}
            {!displayName && t('HNotifications')}
            {isOpen ? (
                <NotificationContent
                    content={cancelNotification}
                    party={party}
                    parentRef={spanNotification}
                    handleClickOutside={setIsOpen}
                />
            ) : null}
        </button>
    )
}
