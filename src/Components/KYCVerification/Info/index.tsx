import React from 'react'
import Lock from 'Assets/headersecurity/Lock.png'
import { useTranslation } from 'react-i18next'

const Info = () => {

    const { t } = useTranslation()

    return (
        <div className="verify__info">
            <div className="verify__infoWrapper">
                <div className="verify__icon">
                    <img src={Lock} alt="100% secure" />
                    <b>100% {t("DepositSecured")}</b>
                </div>
                <h4>{t("KYCInfoHeader")}</h4>
                <p>{t("KYCInfo1")}</p>
                <p>{t("KYCInfo2")}</p>
                <p>{t("KYCInfo3")}</p>
                <p>{t("KYCInfo4")}</p>
            </div>
        </div>
    )
}

export default Info
