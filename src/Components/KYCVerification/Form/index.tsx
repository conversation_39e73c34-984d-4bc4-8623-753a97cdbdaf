import React from 'react'
import { Field, Formik, FormikProps } from 'formik'
import * as Yup from 'yup'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { TKYCForm } from 'Containers/KYCVerification'
import { useTranslation } from 'react-i18next'

const ValidationSchema = Yup.object({
    verification: Yup.string().required('Required field')
})

const isProceedButtonDisabled = (formik: FormikProps<TKYCForm>) =>
    Boolean(formik.errors.verification || formik.values.verification === '')

const KYCForm = ({
    handleVerification
}: {
    handleVerification: (data: TKYCForm) => void
}) => {
    const formValues = {
        verification: ''
    }

    const { t } = useTranslation()

    return (
        <Formik
            initialValues={formValues}
            onSubmit={values => {
                handleVerification(values)
            }}
            validationSchema={ValidationSchema}
        >
            {formik => (
                <form className="verify__form" onSubmit={formik.handleSubmit}>
                    <p id="my-radio-group">
                        {t("KYCRadioHeader")}
                    </p>
                    <div role="group" aria-labelledby="my-radio-group">
                        <label>
                            <Field
                                type="radio"
                                name="verification"
                                value="now"
                                className="verify__input"
                            />
                            {t("KYCRadioNow")}
                        </label>
                        <label>
                            <Field
                                type="radio"
                                name="verification"
                                value="later"
                                className="verify__input"
                            />
                            {t("KYCRadioLater")}
                        </label>
                    </div>
                    {formik.touched.verification &&
                        formik.errors.verification ? (
                        <ErrorMessage message={formik.errors.verification} />
                    ) : null}
                    <button
                        disabled={isProceedButtonDisabled(formik)}
                        className="registerbtn__nextStep"
                        type="submit"
                    >
                        {t("KYCBTN")}
                    </button>
                </form>
            )}
        </Formik>
    )
}

export default KYCForm
