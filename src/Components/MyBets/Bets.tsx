import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { CreateEvent } from '@daml/ledger'
import numeral from 'numeral'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import { removeAllBets, showPlaceAll, useBetsDispatch } from 'State/BetsContext'
import Bet from './Bet'

import './style.scss'
import TotalPayoutTooltip from './ToolTips/TotalPayoutTooltip'

export default function Bets({
    bets,
    isAuthenticated,
    availableBalance,
    totalProfit,
    totalLiability,
    isEnoughBalance,
    bonusAvailable,
    totalPayout,
    GlobalGamblingConfigurationContract
}: {
    bets: any[]
    isAuthenticated: boolean
    availableBalance: string | null
    totalProfit: number
    totalLiability: number
    isEnoughBalance: boolean
    bonusAvailable: string | null
    totalPayout: number
    GlobalGamblingConfigurationContract: readonly CreateEvent<
        GlobalGamblingConfiguration,
        GlobalGamblingConfiguration.Key,
        string
    >[]
}) {
    const dispatch = useBetsDispatch()
    const { t } = useTranslation()

    const isDisabled = !isAuthenticated || !bets.length

    return (
        <>
            <div className="myBets__body">
                {!bets.length && (
                    <p className="matched__message">{t('NoSelections')}</p>
                )}
                {bets.map((bet: any) => (
                    <div key={bet.betPlacementId}>
                        <Bet
                            bet={bet}
                            GlobalGamblingConfigurationContract={
                                GlobalGamblingConfigurationContract
                            }
                        />
                    </div>
                ))}
            </div>
            <div className="myBets__footer">
                {!isAuthenticated ? null : (
                    <>
                        <h5>
                            {t('YourPlayableBalanceIs')}{' '}
                            {numeral(
                                Number(availableBalance) +
                                    Number(bonusAvailable)
                            ).format('$0,0.00')}
                        </h5>
                        <Link to="/deposit">{t('AddFunds')}</Link>
                    </>
                )}

                <div className="myBets__footer__text">
                    Total {t('HBets')}:<strong>{bets.length}</strong>
                </div>
                <div className="myBets__footer__text">
                    {t('Possible')} {t('Profit')}:{' '}
                    <strong> {numeral(totalProfit).format('$0,0.00')}</strong>
                </div>
                <div className="myBets__footer__text">
                    {t('Possible')} {t('Liability')}:{' '}
                    <strong>
                        {' '}
                        {numeral(totalLiability).format('$0,0.00')}
                    </strong>
                </div>
                <div className="myBets__footer__text">
                    <span>
                        {`${t('Possible')} ${t('Payout')} `}
                        <TotalPayoutTooltip /> :
                    </span>
                    <strong> {numeral(totalPayout).format('$0,0.00')}</strong>
                </div>
                <button
                    className="btn btn__green"
                    onClick={() => showPlaceAll(dispatch)}
                    disabled={isDisabled}
                    title={
                        !isAuthenticated
                            ? 'Please log in to place your bets'
                            : !isEnoughBalance
                            ? 'You don´t have enough money to do this bet'
                            : 'Place your bets'
                    }
                >
                    {t('PlaceAll')} ({bets.length}) {t('HBets')} {t('Now')}
                </button>
                <button
                    className="btn btn__grey"
                    onClick={() => removeAllBets(dispatch)}
                    title="Remove all bets"
                >
                    {t('ClearAllBets')}
                </button>
            </div>
        </>
    )
}
