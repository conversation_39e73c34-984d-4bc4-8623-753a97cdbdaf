@import '../../Styles/colors';

.myBets {
    background: $lightGrey;
    min-width: 363px;
    max-width: 363px;
    max-height: calc(100vh - 70px);
    min-height: calc(100vh - 100px);
    overflow-y: auto;
    padding: 8px;
    transition: 0.5s;

    @media (max-width: 1011px) {
        position: absolute;
        z-index: 20;
        right: 0;
        max-height: calc(100vh - 70px);
        height: 100%;
    }
    @media (max-width: 430px) {
        width: 100%;
        min-width: 100%;
    }

    &__header {
        background: $white;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
        height: 39px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 18px;
        button {
            background: none;
            border: none;
            height: 100%;
            text-transform: capitalize;
            font-family: Montserrat, sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 1rem;
            line-height: 20px;
            text-align: center;
            padding: 9px;
            cursor: pointer;
            svg {
                width: 20px;
                height: 20px;
                path {
                    fill: $darkGrey;
                }
            }

            &.active {
                color: $purple;
                border-bottom: 2px solid $purple;
                padding-bottom: 7px;
            }
        }
    }

    &__body {
        height: calc(100vh - 39px - 370px);
        overflow-y: scroll;

        @media only screen and (max-height: 600px) and (orientation: landscape) {
            height: 100px;
            overflow-y: scroll;
        }
    }

    &__footer {
        position: absolute;
        background: $white;
        bottom: 0;
        width: 345px;
        min-height: 200px;
        border: 1px solid $grayInpuBorder;
        //box-sizing: border-box;
        border-radius: 6px;
        padding: 8px;
        color: $darkGrey;
        display: flex;
        flex-direction: column;
        font-family: OpenSans, sans-serif;
        font-size: 0.938rem;
        line-height: 20px;
        text-align: center;
        &__text {
            display: flex;
            justify-content: space-between;
        }
        a {
            color: $successgreen;
            font-weight: bold;
            text-decoration: none;
            font-size: 0.813rem;
        }
        .btn {
            width: 100%;
            font-style: normal;
            font-size: 13px;
            line-height: 25px;
            text-align: center;
            &__green {
                margin-top: 30px;
            }
        }

        @media only screen and (max-height: 600px) and (orientation: landscape) {
            min-height: 150px;
            max-height: 150px;
            overflow-y: scroll;
        }

        @media (max-width: 430px) {
            width: 95%;
            min-width: 95%;
        }
    }
}

.bets {
    margin-bottom: 20px;
    padding: 5px;

    &--back {
        border-left: 2px solid $purple;
    }

    &--lay {
        border-left: 2px solid $orange;
    }
}

.bet {
    border: 1px solid $grayInpuBorder;
    box-sizing: border-box;
    border-radius: 6px;
    padding: 8px;
    color: $darkGrey;
    font-family: Montserrat, sans-serif;
    font-size: 0.7rem;
    line-height: 18px;

    @media (max-width: 430px) {
        font-size: 1rem;
    }

    &__input {
        width: 90%;
        border: none;
        background-color: transparent;
        color: $darkGrey;
        font-family: Montserrat, sans-serif;
        font-size: 0.7rem;
        line-height: 13px;
        text-align: center;
        font-weight: bold;

        &:focus {
            outline: transparent;
        }

        @media (max-width: 430px) {
            font-size: 1rem;
        }
    }

    h5 {
        font-style: normal;
        font-weight: bold;
        font-size: 0.7rem;
        line-height: 20px;

        @media (max-width: 430px) {
            font-size: 1rem;
        }
    }
    &__wrapper {
        display: flex;
        justify-content: space-between;
        text-transform: capitalize;
    }
    &__boxWrapper {
        display: flex;
        justify-content: space-between;
        margin-top: 13px;
        align-items: center;

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* Firefox */
        input[type='number'] {
            -moz-appearance: textfield;
        }

        .closeButton {
            border: none;
            cursor: pointer;
            background: transparent;
            margin-left: 13px;

            svg {
                path {
                    fill: $darkGrey;
                }
            }

            &:hover {
                svg {
                    path {
                        fill: $rederror;
                    }
                }
            }
        }

        .confirmButton {
            border: none;
            cursor: pointer;
            background: transparent;

            svg {
                path {
                    fill: $darkGrey;
                }
            }

            &:hover {
                svg {
                    path {
                        fill: $successgreen;
                    }
                }
            }
        }

        .checkIcon {
            font-size: 2rem;
        }
        &__box {
            background: $grayBack;
            border-radius: 6px;
            width: 80px;
            height: 45px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: OpenSans, sans-serif;
            font-size: 0.7rem;
            line-height: 13px;
            text-align: center;
            color: $darkGrey;
            overflow: hidden;
            transition: filter 0.2s;

            @media (max-width: 430px) {
                font-size: 1rem;
            }

            &__item {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                padding: 4px;
                height: 100%;
                width: 100%;
            }

            &__stake {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                padding: 4px;
                height: 100%;
                width: 100%;

                &:focus-within {
                    border: 2px solid $darkGrey;
                    border-radius: 6px;
                }
            }

            .buttons__block {
                display: flex;
                flex-direction: column;
                height: 100%;
                justify-content: space-between;
                background: $darkGrey;
                width: 21px;
                button {
                    background: transparent;
                    cursor: pointer;
                    border: none;
                    color: #ffffff;
                    font-size: 0.688rem;
                    max-height: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 50%;
                    &:hover {
                        opacity: 0.7;
                    }
                }
            }
        }
    }
}

.confirmBet {
    padding: 15px;
    height: 100%;

    &__greenLink {
        color: $successgreen !important;
    }

    &__promoContainer {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;

        label {
            font-size: 0.875rem;
            flex: 3.5;
            font-weight: 700;
            gap: 10px;
        }

        select {
            font-weight: normal;
            flex: 5.5;
            width: 100%;
            font-size: 0.875rem;
            border: 1px solid $grayInpuBorder;
            border-radius: 6px;
            padding: 5px 10px;
        }
    }

    &__p {
        font-size: 0.75rem;
        margin-top: 10px;

        svg {
            margin-right: 5px;
        }

        a {
            text-decoration: none;
            color: $purple;
        }
    }

    &__title {
        color: $purple;
        font-size: 1.25rem;
        font-weight: 700;
        padding-top: 5px;
        text-transform: uppercase;
    }

    &__subtitle {
        color: $darkGrey;
        font-size: 1rem;
        font-weight: 700;
        padding: 10px 0;
    }

    &__container {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        width: 100%;
        color: #43444a;
        margin-top: 20px;
    }

    &__item {
        display: flex;
        margin-bottom: 8px;
        width: 100%;
    }

    &__itemTitle {
        font-size: 0.875rem;
        flex: 3.5;
        font-weight: 700;
        gap: 10px;
    }

    &__itemText {
        font-weight: normal;
        flex: 5.5;
        font-size: 0.875rem;
        gap: 10px;
    }

    &__action {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 20px;

        button {
            margin: 0;
        }
    }

    &__footer {
        font-size: 0.85rem;
        margin-top: 20px;
        max-width: 340px;

        a {
            color: $purple;
            text-decoration: none;
        }

        &--error {
            color: $rederror;
            margin-bottom: 10px;
            line-height: 1.5rem;
        }
    }

    &__footerInfo {
        display: flex;
        margin-bottom: 10px;
        align-items: center;
        gap: 5px;
    }
}

.matched {
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%;
    color: $darkGrey;

    &__body {
        height: calc(100vh - 150px);
        overflow-y: scroll;
    }

    &__message {
        text-align: center;
        font-weight: 400;
        font-size: 0.75rem;
    }

    &__bet {
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
        display: flex;
        background-color: $white;
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 5px;
    }

    &__betWrapper {
        flex: 1 70%;
        display: flex;
        flex-direction: column;
        color: $darkGrey;

        h5 {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        div {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        strong {
            font-size: 0.9rem;
        }
        p {
            font-size: 0.8rem;
        }
    }
}

.unmatched {
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%;

    &__body {
        height: calc(100vh - 150px);
        overflow-y: scroll;
    }

    &__message {
        text-align: center;
        font-weight: 400;
        font-size: 0.75rem;
    }

    &__bet {
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
        display: flex;
        background-color: $white;
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 5px;
    }

    &__betWrapper {
        flex: 1 70%;
        display: flex;
        flex-direction: column;
        color: $darkGrey;

        h5 {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        div {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        strong {
            font-size: 0.9rem;
        }
        p {
            font-size: 0.8rem;
        }
    }

    &__button {
        flex: 1 30%;
        display: flex;
        align-items: center;
        justify-content: center;

        .closeButton {
            border: none;
            cursor: pointer;
            background: transparent;
            font-size: 1.875rem;
            svg {
                path {
                    fill: $darkGrey;
                }
            }

            &:hover {
                svg {
                    path {
                        fill: $rederror;
                    }
                }
            }
        }
    }
}

.confirmDelete {
    &__container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 120px;
        gap: 10px;
    }

    &__text {
        color: $darkGrey;
        font-size: 0.9rem;
        line-height: 1rem;
        text-align: center;
    }

    &__subtext {
        font-size: 0.7rem;
        line-height: 1rem;
        text-align: center;
    }

    &__btn {
        width: 100px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
.small {
    button {
        font-size: 0.7rem;
    }
}
