import React from 'react'

import { convertOddValue, getOddMinMaxValues, getValidOddValue, oddConfigs } from '../utils';
import { useOddType } from 'State/OddTypeContext';
import { useBetsDispatch, updateBet } from 'State/BetsContext';
import roundNumber from 'Utils/roundNumber';

const regMoneyline = /^[-]?\d*$/;
const regexDecimal = /^\d*\.?\d{0,2}$/;

export default function InputOdds(
    {
        bet,
        GlobalGamblingConfigurationContract
    }: { 
        bet:any,
        GlobalGamblingConfigurationContract:any
    }) {

    const [value, setValue] = React.useState(bet.odd.value);   
    const [isEdit, setIsEdit] = React.useState(false)

    const { oddState } = useOddType()
    const dispatch = useBetsDispatch()
    const minOddDecimal = getOddMinMaxValues(GlobalGamblingConfigurationContract,"minOdd")
    const minOddMoneyline = convertOddValue(
        Number(minOddDecimal),
        'Moneyline',
        'Decimal'
    )

    React.useEffect(() => {
        setValue(bet.odd.value)
    },[bet.odd.value])

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = event.target.value;
        const regexValidator = oddState === "Decimal" ? regexDecimal : regMoneyline
        if (inputValue === '' || regexValidator.test(inputValue) ) {
            const matchedValue = inputValue.match(regexDecimal);
            let endRes =  matchedValue ? matchedValue[0] :  inputValue;
            setValue(endRes);
        } 
    };

    const handleInputBlur = () => {
        let validatedValue = oddState === "Decimal" ? roundNumber(value,2) : parseInt(value, 10)

        if (isNaN(validatedValue) && oddState === "Decimal") {
            validatedValue = Number(minOddDecimal);
        } 

        if (isNaN(validatedValue) && oddState === "Moneyline") {
            validatedValue = Number(minOddMoneyline);
        } 

        let validValue = getValidOddValue(
            Number(validatedValue),
            oddState,
            GlobalGamblingConfigurationContract
        )
       
        const updatedBet = {
            ...bet,
            odd: {
                tag: oddState,
                value: validValue
            }
        }
        updateBet(dispatch, updatedBet)
        setIsEdit(false)
        setValue(validValue?.toString())
    };

    const increaseOdds = () => {
        const betValue = getValidOddValue(
            Number(bet.odd.value) + oddConfigs[oddState].step,
            oddState,
            GlobalGamblingConfigurationContract
        )
        const updatedBet = {
            ...bet,
            odd: { tag: oddState, value: betValue }
        }
        updateBet(dispatch, updatedBet)
        setValue(betValue?.toString())
    }

    const decreaseOdds = () => {
        const betValue = getValidOddValue(
            Number(bet.odd.value) - oddConfigs[oddState].step,
            oddState,
            GlobalGamblingConfigurationContract
        )
        const updatedBet = {
            ...bet,
            odd: { tag: oddState, value: betValue }
        }
        updateBet(dispatch, updatedBet)
        setValue(betValue?.toString())
    }

   const handleKeyDown = (evt:React.KeyboardEvent) => {
        if(evt.key === "ArrowDown") {
            decreaseOdds()
        }
        if(evt.key === "ArrowUp") {
            increaseOdds()
        }
   }

    return (
        isEdit ? 
            <input
                type="text"
                className="bet__input"
                value={value}
                onChange={handleInputChange}
                onBlur={handleInputBlur}
                onKeyDown={handleKeyDown}
            />  : 
            <input
                type="text"
                className="bet__input"
                value={bet.odd.value}
                onClick={() => setIsEdit(true)}
            /> 
    )
}