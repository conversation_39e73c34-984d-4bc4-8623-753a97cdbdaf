import React from 'react'

import { useOddType } from 'State/OddTypeContext';
import { useBetsDispatch, updateBet } from 'State/BetsContext';
import { convertDecimalNumberToFraction, 
    convertOddValue, 
    getOddMinMaxValues, 
    getValidOddValue, 
    oddConfigs } from '../utils';
import roundNumber from 'Utils/roundNumber';

const regexFraction = /(?:[1-9][0-9]*|0)\/[1-9][0-9]*/g
// eslint-disable-next-line no-useless-escape
const keyRegex = /^[0-9\/]*$/;

export default function InputFractional(
    {
        bet,
        GlobalGamblingConfigurationContract
    }: { 
        bet:any,
        GlobalGamblingConfigurationContract:any
    }) {

    const [value, setValue] = React.useState(convertDecimalNumberToFraction(bet.odd.value));   
    const [isEdit, setIsEdit] = React.useState(false)
    const { oddState } = useOddType()
    const dispatch = useBetsDispatch()
    const minOddDecimal = getOddMinMaxValues(GlobalGamblingConfigurationContract,"minOdd")
    const minOddFractional = convertOddValue(
        Number(minOddDecimal),
        'Fractional',
        'Decimal'
    )

    React.useEffect(() => {
        setValue(convertDecimalNumberToFraction(bet.odd.value))
    },[bet.odd.value])

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = event.target.value;
        if (inputValue === '' || keyRegex.test(inputValue) ) {
            const matchedValue = inputValue.match(regexFraction);
            let endRes =  matchedValue ? matchedValue[0] :  inputValue;
            setValue(endRes);
        } 
    };

    const handleInputBlur = () => {
        let fraction = value
        const [numerator, denominator] = fraction.split('/');
        const quotient = parseFloat(numerator) / parseFloat(denominator);
        let validatedValue = roundNumber(quotient,2);

        if (isNaN(validatedValue)) {
            validatedValue = minOddFractional;
        } 
       
        const updatedBet = {
            ...bet,
            odd: {
                tag: oddState,
                value: getValidOddValue(
                    Number(validatedValue),
                    oddState,
                    GlobalGamblingConfigurationContract
                )
            }
        }
        updateBet(dispatch, updatedBet)
        setIsEdit(false)
        setValue(convertDecimalNumberToFraction(validatedValue))
    };

    const increaseOdds = () => {
        const betValue = getValidOddValue(
            Number(bet.odd.value) + oddConfigs[oddState].step,
            oddState,
            GlobalGamblingConfigurationContract
        )
        const updatedBet = {
            ...bet,
            odd: { tag: oddState, value: betValue }
        }
        updateBet(dispatch, updatedBet)
        setValue(convertDecimalNumberToFraction(betValue))
    }

    const decreaseOdds = () => {
        const betValue = getValidOddValue(
            Number(bet.odd.value) - oddConfigs[oddState].step,
            oddState,
            GlobalGamblingConfigurationContract
        )
        const updatedBet = {
            ...bet,
            odd: { tag: oddState, value: betValue }
        }
        updateBet(dispatch, updatedBet)
        setValue(convertDecimalNumberToFraction(betValue))
    }

   const handleKeyDown = (evt:React.KeyboardEvent) => {
        if(evt.key === "ArrowDown") {
            decreaseOdds()
        }
        if(evt.key === "ArrowUp") {
            increaseOdds()
        }
   }

    return (
        isEdit ? 
            <input
                type="text"
                className="bet__input"
                onChange={handleInputChange}
                value={value}
                onBlur={handleInputBlur}
                onKeyDown={handleKeyDown}
            /> : 
            <input
                type="text"
                className="bet__input"
                value={convertDecimalNumberToFraction(bet.odd.value)}
                onClick={() => setIsEdit(true)}
            /> 
    )
}