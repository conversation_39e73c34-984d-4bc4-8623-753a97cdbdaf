import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { useLedger, useParty, useQuery } from '@daml/react'
import numeral from 'numeral'
import React from 'react'
import { Link } from 'react-router-dom'
import { toast } from 'react-toastify'
import { v4 as uuidV4 } from 'uuid'

import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
    hidePlacement,
    removeBet,
    useBetsDispatch,
    useBetsState
} from 'State/BetsContext'
import {
    calculateBetResult,
    convertDecimalNumberToFraction,
    getTitleFunc,
    isDateBeforeStart,
    promoMessageDisplay,
    translateDraw
} from './utils'

import {
    BetHistory,
    BetPlacement
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { PromotionWallet } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import PromotionsDropdown from 'Components/PromotionsDropdown'
import { haveIUsedPromo } from 'Components/PromotionsDropdown/promotion.helper'
import useIsAuthenticated from 'Hooks/useAuth'
import TagManager from 'react-gtm-module'
import { useTranslation } from 'react-i18next'
import 'react-toastify/dist/ReactToastify.css'
import { useI18LanguageContext } from 'State/LanguageState'
import { useSelectedPromotionContext } from 'State/SelectPromotion'
import { logEvent } from 'Utils/analytics'
import { generateMapFromKeyValueArray } from 'Utils/generateMapFromKeyValueArray'
import HasErrorMessage from './HasErrorMessage'

export default function ConfirmBet({
    availableBalance,
    fee,
    bonusAvailable
}: {
    availableBalance: string | null
    fee: string
    bonusAvailable: string
}) {
    const party = useParty()
    const ledger = useLedger()
    const { betToPlace: bet } = useBetsState()
    const isAuthenticated = useIsAuthenticated()
    const dispatch = useBetsDispatch()
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()

    const [selectedPromotion, setSelectedPromotion] =
        useSelectedPromotionContext()
    const [promo, setPromo] = React.useState(() =>
        selectedPromotion?.payload.config.action.tag === 'Bet'
            ? selectedPromotion
            : null
    )

    const [isClicked, setIsClicked] = React.useState(false)

    const { contracts: serviceContracts, loading: serviceLoader } = useQuery(
        Service,
        () => {
            return { customer: party }
        },
        [party]
    )

    const { contracts: placedBetsContracts, loading: placedBetsLoader } =
        useQuery(
            BetPlacement,
            () => {
                return {
                    customer: party
                }
            },
            [party]
        )
    const { contracts: historyBetsContracts, loading: historyBetsLoader } =
        useQuery(
            BetHistory,
            () => {
                return {
                    customer: party
                }
            },
            [party]
        )

    const { contracts: promotionWalletContracts, loading } = useQuery(
        PromotionWallet,
        () => {
            return {
                customer: party
            }
        },
        [party]
    )

    const calculatedAmount = calculateBetResult(
        bet.odd.value,
        bet.odd.tag,
        bet.stake
    )

    const doesUserHasMoneyToBet = () =>
        bet.sideTag === 'Lay'
            ? Number(availableBalance) + Number(bonusAvailable) >=
              calculatedAmount
            : Number(availableBalance) + Number(bonusAvailable) >= bet.stake

    const promoValidation = () => {
        const hasPromo = () => {
            const { endDate, minAmount, maxAmount } = promo?.payload?.config
            const now = Date.now()
            if (endDate && endDate <= now) return true
            if (minAmount) {
                return bet.sideTag === 'Lay'
                    ? Number(calculatedAmount) >= Number(minAmount)
                    : Number(bet.stake) >= Number(minAmount)
            }
            if (maxAmount) {
                return bet.sideTag === 'Lay'
                    ? Number(calculatedAmount) <= Number(maxAmount)
                    : Number(bet.stake) <= Number(maxAmount)
            }
            return false
        }

        return promo ? hasPromo() : true
    }

    async function confirmBet() {
        if (isAuthenticated) {
            try {
                setIsClicked(true)
                const eventMap = generateMapFromKeyValueArray(bet?.title)
                const requestConstructor = promo
                    ? {
                          promotion: promo?.key,
                          inBulk: null,
                          betDetails: {
                              eventKey: bet.eventkey,
                              eventTitle: eventMap,
                              eventStartDate: bet?.startDate,
                              betPlacementId: uuidV4(),
                              side: {
                                  tag: bet.sideTag,
                                  value: {
                                      odd: bet.odd,
                                      stake: bet.stake.toString(),
                                      outcome: bet.outcome.outcome
                                  }
                              }
                          }
                      }
                    : {
                          promotion: null,
                          inBulk: null,
                          betDetails: {
                              eventKey: bet.eventkey,
                              eventTitle: eventMap,
                              eventStartDate: bet?.startDate,
                              betPlacementId: uuidV4(),
                              side: {
                                  tag: bet.sideTag,
                                  value: {
                                      odd: bet.odd,
                                      stake: bet.stake.toString(),
                                      outcome: bet.outcome.outcome
                                  }
                              }
                          }
                      }
                const [response] = await ledger.exercise(
                    Service.RequestBetPlacement,
                    serviceContracts[0]?.contractId,
                    requestConstructor
                )
                if (response.tag === 'Right') {
                    return [
                        setIsClicked(false),
                        toast.success('Your bet has been placed. Good luck!'),
                        removeBet(dispatch, bet),
                        hidePlacement(dispatch),
                        setPromo(null),
                        setSelectedPromotion(null),
                        logEvent('bet_placed'),
                        TagManager.dataLayer({
                            dataLayer: {
                                event: 'placed_Betslip',
                                singleBet: {
                                    eventTitle: bet.title,
                                    side: {
                                        tag: bet.sideTag,
                                        value: {
                                            odd: bet.odd,
                                            stake: bet.stake.toString(),
                                            outcome: bet.outcome.outcome
                                        }
                                    }
                                }
                            }
                        })
                    ]
                } else {
                    setIsClicked(false)
                }
            } catch (error) {
                console.error('error single', error)
            }
        }
    }

    const checkAlreadyUsePromo = haveIUsedPromo(
        promotionWalletContracts[0],
        promo
    )

    //Add validation for Service here
    const shouldButtonBeDisabled =
        Number.isNaN(Number(bet.stake)) ||
        Number(bet.stake) === 0 ||
        bet.odd.value === 0 ||
        !doesUserHasMoneyToBet() ||
        !isAuthenticated ||
        isClicked ||
        !promoValidation() ||
        checkAlreadyUsePromo ||
        serviceLoader ||
        serviceContracts.length === 0 ||
        !isDateBeforeStart(bet?.startDate)

    const shouldRenderDropdown =
        !loading && !historyBetsLoader && !placedBetsLoader && isAuthenticated

    return (
        <div className="confirmBet">
            <h3 className="confirmBet__title">{t('BetConfirmReviewOrder')}</h3>
            <h5 className="confirmBet__subtitle">
                {t('BetConfirmAreYouSureSingle')}
            </h5>
            <div className="confirmBet__container">
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">
                        {t('BetEvent')}:
                    </div>
                    <div className="confirmBet__itemText">
                        {getTitleFunc(bet.title, lang)}
                    </div>
                </div>
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">
                        {t('BetSelection')}:
                    </div>
                    <div className="confirmBet__itemText">
                        {translateDraw(bet.participant, t('Draw'))}
                    </div>
                </div>
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">
                        {t('BetSide')}:{' '}
                    </div>
                    <div className="confirmBet__itemText">{t(bet.sideTag)}</div>
                </div>
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">{t('Odd')}:</div>{' '}
                    <div className="confirmBet__itemText">
                        {bet.odd.tag === 'Fractional'
                            ? convertDecimalNumberToFraction(bet.odd.value)
                            : bet.odd.value}
                        (
                        {bet.odd.tag === 'Moneyline' ? 'American' : bet.odd.tag}
                        )
                    </div>
                </div>
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">
                        {bet.sideTag === 'Back'
                            ? t('Stake')
                            : `Backer´s ${t('Stake')}`}
                        :{' '}
                    </div>
                    <div className="confirmBet__itemText">
                        {numeral(bet.stake).format('$0,0.00')}
                    </div>
                </div>
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">
                        {' '}
                        {bet.sideTag === 'Back'
                            ? t('Profit')
                            : t('Liability')}:{' '}
                    </div>
                    <div className="confirmBet__itemText">
                        {numeral(calculatedAmount).format('$0,0.00')}
                    </div>
                </div>
                {shouldRenderDropdown ? (
                    <PromotionsDropdown
                        styleClass="confirmBet__promoContainer"
                        promoFilter="Bet"
                        defaultSelected={selectedPromotion}
                        showInfoIcon
                        onValueChange={setPromo}
                        firstTime={
                            placedBetsContracts?.length === 0 &&
                            historyBetsContracts?.length === 0 &&
                            promotionWalletContracts[0]?.payload?.promotionMap?.entriesArray()
                                ?.length === 0
                        }
                        serviceContracts={serviceContracts}
                        serviceLoading={serviceLoader}
                        promotionWalletContracts={promotionWalletContracts}
                        isLoadingPromoWallet={loading}
                    />
                ) : null}
                {promo ? (
                    checkAlreadyUsePromo ? (
                        <div className="confirmBet__item">
                            <p className="confirmBet__p">
                                <FontAwesomeIcon icon={faInfoCircle} />
                                You have already used this promo
                            </p>
                        </div>
                    ) : (
                        <div className="confirmBet__item">
                            <p className="confirmBet__p">
                                <FontAwesomeIcon icon={faInfoCircle} />
                                To see more info regarding selected promotion go{' '}
                                <Link
                                    to={`/promotions/${promo?.payload.promotionId}`}
                                >
                                    here
                                </Link>
                            </p>
                        </div>
                    )
                ) : null}
                {promo ? (
                    !promoValidation() ? (
                        <div className="confirmBet__item">
                            <p className="confirmBet__p">
                                <FontAwesomeIcon icon={faInfoCircle} />
                                {promoMessageDisplay(promo)}
                            </p>
                        </div>
                    ) : null
                ) : null}
            </div>
            <div className="confirmBet__action">
                <button
                    className="btn btn__grey confirmBet__button"
                    onClick={() => hidePlacement(dispatch)}
                >
                    {t('BetConfirmCancelBtn')}
                </button>
                <button
                    className="btn btn__green confirmBet__button"
                    onClick={() => confirmBet()}
                    title={
                        !isAuthenticated
                            ? 'Please log in to place your bet'
                            : 'Place your bet'
                    }
                    disabled={shouldButtonBeDisabled}
                >
                    {t('BetConfirmPlaceBetBtn')}
                </button>
            </div>
            <div className="confirmBet__footer">
                {fee?.length && (
                    <div className="confirmBet__footerInfo">
                        <FontAwesomeIcon icon={faInfoCircle} />
                        <p>
                            {t('BetConfirmMessageFee1')} {Number(fee) * 100}%{' '}
                            {t('BetConfirmMessageFee2')}
                        </p>
                    </div>
                )}
                <HasErrorMessage
                    textMessage={t('BetConfirmMessageStake')}
                    condition={
                        Number.isNaN(Number(bet.stake)) ||
                        Number(bet.stake) === 0
                    }
                />
                <HasErrorMessage
                    textMessage={t('BetConfirmMessageUnAuth')}
                    condition={!isAuthenticated}
                />
                <HasErrorMessage
                    textMessage={t('BetConfirmMessageNoMoney')}
                    condition={!doesUserHasMoneyToBet()}
                />
                <HasErrorMessage
                    textMessage={t('BetConfirmMessageEventStarted')}
                    condition={!isDateBeforeStart(bet?.startDate)}
                />
                {isAuthenticated && (
                    <p>
                        {t('BetConfirmMessageDeposit1')}{' '}
                        <Link to="/deposit" className="confirmBet__greenLink">
                            {t('BetConfirmMessageDeposit2')}
                        </Link>
                    </p>
                )}
            </div>
        </div>
    )
}
