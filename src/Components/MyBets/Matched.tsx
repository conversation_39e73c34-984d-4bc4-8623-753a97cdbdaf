import React from 'react'
import { useParty, useStreamQueries } from '@daml/react'
import {
    BetPlacement,
    Status
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'

import PlacedBetsMarkup from './PlacedBetsMarkup'
import { useTranslation } from 'react-i18next'

import './style.scss'

export default function Matched() {
    const party = useParty()
    const { t } = useTranslation()

    const { contracts: matchedBets, loading: matchedBetsLoader } =
        useStreamQueries(
            BetPlacement,
            () => {
                return [
                    {
                        customer: party,
                        status: 'Matched' as Status
                    }
                ]
            },
            [party]
        )

    const MatchedBetsMarkup = () =>
        matchedBets.length ? (
            <>
                {matchedBets.map(bet => (
                    <PlacedBetsMarkup
                        bet={bet}
                        isUnmatched={false}
                        key={bet.contractId}
                    />
                ))}
            </>
        ) : (
            <p className="matched__message">{t('NoBetsFound')}</p>
        )

    const pageMarkup = matchedBetsLoader ? (
        <p className="matched__message">{t('Loading')}</p>
    ) : (
        <MatchedBetsMarkup />
    )

    return (
        <>
            <div className="matched__body">{pageMarkup}</div>
        </>
    )
}
