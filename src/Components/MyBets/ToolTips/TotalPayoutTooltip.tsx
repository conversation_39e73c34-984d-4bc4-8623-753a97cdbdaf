import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import ReactTooltip from 'react-tooltip'
import { useI18LanguageContext } from 'State/LanguageState'
import { handleLangValue, translationsTotalPayout } from './tooltips.util'

import './style.scss'

export default function TotalPayoutTooltip() {
    const { lang } = useI18LanguageContext()

    return (
        <>
            <FontAwesomeIcon
                icon={faInfoCircle}
                data-tip={handleLangValue(lang, translationsTotalPayout)}
                data-for="TotalPayoutTooltip"
            />
            <ReactTooltip
                id="TotalPayoutTooltip"
                place="top"
                type="dark"
                effect="solid"
                className="width200px"
                clickable
                class="tooltipMaxWidthMobile"
            />
        </>
    )
}
