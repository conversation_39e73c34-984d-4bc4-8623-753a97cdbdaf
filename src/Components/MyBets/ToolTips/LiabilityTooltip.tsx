import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import ReactTooltip from 'react-tooltip'
import { useI18LanguageContext } from 'State/LanguageState'
import { handleLangValue, translationsLiability } from './tooltips.util'

export default function LiabiltyTooltip() {
    const { lang } = useI18LanguageContext()

    return (
        <>
            <FontAwesomeIcon
                icon={faInfoCircle}
                data-for="LiabiltyTooltip"
                data-tip={handleLangValue(lang, translationsLiability)}
            />
            <ReactTooltip
                id="LiabiltyTooltip"
                place="top"
                type="dark"
                effect="solid"
                clickable
                class="tooltipMaxWidthMobile"
            />
        </>
    )
}
