import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import ReactTooltip from 'react-tooltip'
import { useI18LanguageContext } from 'State/LanguageState'
import {
    handleLangValue,
    translationsBack,
    translationsLay
} from './tooltips.util'

export default function BackLayTooltip({ isBack }: { isBack: boolean }) {
    const { lang } = useI18LanguageContext()

    return isBack ? (
        <>
            {' '}
            <FontAwesomeIcon
                icon={faInfoCircle}
                data-tip={handleLangValue(lang, translationsBack)}
                data-for="BackTooltip"
            />
            <ReactTooltip
                id="BackTooltip"
                place="right"
                type="dark"
                effect="solid"
                clickable
                class="tooltipMaxWidthMobile"
            />
        </>
    ) : (
        <>
            <FontAwesomeIcon
                icon={faInfoCircle}
                data-tip={handleLangValue(lang, translationsLay)}
                data-for="LayTooltip"
            />
            <ReactTooltip
                id="LayTooltip"
                place="top"
                type="dark"
                effect="solid"
                clickable
                class="tooltipMaxWidthMobile"
            />
        </>
    )
}
