export const handleLangValue = (
    lang: string,
    {
        US,
        BR,
        MX
    }: {
        US: string
        BR: string
        MX: string
    }
) => (lang === 'MX' ? MX : lang === 'BR' ? BR : US)

export const translationsLiability = {
    MX: 'El Riesgo es la cantidad máxima que puede perder en una apuesta en Contra.',
    US: 'Liability is the max amount you can lose on a Lay bet.',
    BR: 'Risco é o valor máximo que você pode perder em uma aposta Contra.'
}

export const translationsLay = {
    MX: 'Una apuesta En Contra es cuando se apuesta a que algo NO va a suceder.',
    US: 'A Lay bet is when you bet on something NOT to happen.',
    BR: 'Uma aposta contra é quando você aposta que algo NÃO vai acontecer.'
}

export const translationsBack = {
    MX: 'Una apuesta A Favor es cuando se apuesta a que algo va a suceder.',
    US: 'A Back bet is when you bet on something to happen.',
    BR: 'Uma aposta a Favor é quando você aposta que algo vai acontecer.'
}

export const translationsTotalPayout = {
    MX: 'El pago posible es la cantidad total de dinero que obtendrá si gana su apuesta, incluida tanto su apuesta original como su ganancia, teniendo en cuenta la tarifa del 5% que Gambyl cobrará sobre la ganancia.',
    US: 'Possible payout is the total amount of money you will get if you win your bet, including both your original wager and your profit taking in account the 5% fee that will be charged by Gambyl on the profit.',
    BR: 'O pagamento possível é a quantia total de dinheiro que você receberá se ganhar sua aposta, incluindo sua aposta original e seu lucro, levando em consideração a taxa de 5% que será cobrada pelo Gambyl sobre o lucro.'
}
