import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { BetPlacement } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { CreateEvent } from '@daml/ledger'
import * as math from 'mathjs'
import numeral from 'numeral'
import roundNumber from 'Utils/roundNumber'
import moment from 'moment-timezone'
import {
    getEventTitleFromLang,
    langConverter
} from 'Utils/getEventTitleFromLang'

export const translateDraw = (value: string, replacer: string) =>
    value?.includes('Draw') ? value.replace('Draw', replacer) : value

export const oddConfigs: any = {
    Decimal: {
        min: 1.01,
        step: 0.01
    },
    Moneyline: {
        step: 1,
        min: -999900,
        max: 999900
    },
    Fractional: {
        min: 0.01,
        step: 0.01
    }
}

export const promoMessageDisplay = (promo: any) => {
    const { minAmount, maxAmount } = promo?.payload?.config
    if (minAmount) {
        return `You can't place a bet with a stake of more less ${numeral(
            minAmount
        ).format(
            '$0,0.00'
        )} with commission free bets promotion applied. You can only use this promotion with stake totalling up to a minimun of ${numeral(
            minAmount
        ).format('$0,0.00')}.`
    }
    if (maxAmount) {
        return `You can't place a bet with a stake of more than ${numeral(
            maxAmount
        ).format(
            '$0,0.00'
        )} with commission free bets promotion applied. You can only use this promotion with stake totalling up to ${numeral(
            maxAmount
        ).format('$0,0.00')}.`
    }
}

export function retrieveBetDataAccordingToTypeOfBet(bet: any) {
    if (bet?.payload.details.side.value.outcome.type_.tag === 'OverUnder') {
        return `${bet.payload.details.side.value.outcome.subtype} ${bet?.payload.details.side.value.outcome.type_.value}`
    }
    if (
        bet?.payload.details.side.value.outcome.type_.tag === 'ThreeWayHandicap'
    ) {
        return `${bet.payload.details.side.value.outcome.subtype} (${bet?.payload.details.side.value.outcome.type_.value})`
    }
    if (bet?.payload.details.side.value.outcome.type_.tag === 'ThreeWay') {
        if (bet.payload.details.side.value.outcome.participantId === null) {
            return 'Draw'
        }
        if (
            bet?.payload.details.side.value.outcome.participantOrder % 2 ===
            0
        ) {
            return getEventTitleFromLang(
                bet?.payload.details.eventTitle,
                'US'
            ).split(' vs ')[1]
        } else {
            return getEventTitleFromLang(
                bet?.payload.details.eventTitle,
                'US'
            ).split(' vs ')[0]
        }
    }
    if (bet?.payload.details.side.value.outcome.type_.tag === 'TwoWay') {
        if (
            bet?.payload.details.side.value.outcome.participantOrder % 2 ===
            0
        ) {
            return getEventTitleFromLang(
                bet?.payload.details.eventTitle,
                'US'
            ).split(' vs ')[1]
        } else {
            return getEventTitleFromLang(
                bet?.payload.details.eventTitle,
                'US'
            ).split(' vs ')[0]
        }
    }
    return 'Draw'
}

export function convertDecimalNumberToFraction(number: any) {
    return math.format(math.fraction(number))
}

export const getOddMinMaxValues = (
    GlobalGamblingConfigurationContract: readonly CreateEvent<
        GlobalGamblingConfiguration,
        GlobalGamblingConfiguration.Key,
        string
    >[],
    minOrMax: 'minOdd' | 'maxOdd'
) =>
    GlobalGamblingConfigurationContract.filter(
        val => val?.payload[minOrMax]?.tag === 'Decimal'
    )[0]?.payload[minOrMax]?.value

export function getValidOddValue(
    oddInputValue: number,
    oddType: string | undefined,
    GlobalGamblingConfigurationContract: readonly CreateEvent<
        GlobalGamblingConfiguration,
        GlobalGamblingConfiguration.Key,
        string
    >[]
) {
    let minOddDec = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'minOdd'
    )
    let maxOddDec = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'maxOdd'
    )
    if (oddType === 'Decimal') {
        if (Number(oddInputValue) < Number(minOddDec)) {
            return roundNumber(Number(minOddDec), 2)
        }
        if (Number(oddInputValue) > Number(maxOddDec)) {
            return roundNumber(maxOddDec, 2)
        }
        return roundNumber(oddInputValue, 2)
    } else if (oddType === 'Fractional') {
        let minOddFrac = convertOddValue(
            Number(minOddDec),
            'Fractional',
            'Decimal'
        )
        let maxOddFrac = convertOddValue(
            Number(maxOddDec),
            'Fractional',
            'Decimal'
        )
        if (Number(oddInputValue) < Number(minOddFrac)) {
            return roundNumber(Number(minOddFrac), 2)
        }
        if (Number(oddInputValue) > Number(maxOddFrac)) {
            return roundNumber(Number(maxOddFrac), 2)
        }
        return roundNumber(oddInputValue, 2)
    } else if (oddType === 'Moneyline') {
        let maxOddMoney = convertOddValue(
            Number(maxOddDec),
            'Moneyline',
            'Decimal'
        )
        let minOddMoney = convertOddValue(
            Number(minOddDec),
            'Moneyline',
            'Decimal'
        )
        if (Number(oddInputValue) > Number(maxOddMoney)) {
            return Number(maxOddMoney)
        }
        if (Number(oddInputValue) >= 100) {
            return Number(oddInputValue)
        }
        if (
            Number(oddInputValue) <= -101 &&
            Number(oddInputValue) >= Number(minOddMoney)
        ) {
            return Number(oddInputValue)
        }
        if (Number(oddInputValue) === 99) {
            return -101
        }
        if (Number(oddInputValue) < Number(minOddMoney)) {
            return Number(minOddMoney)
        } else {
            return 100
        }
    }
}

export function convertOddValue(
    oddValue: number,
    oddState: string,
    previousOddType: string | undefined
) {
    if (previousOddType === 'Decimal') {
        if (oddState === 'Fractional') {
            return oddValue - 1.0
        } else if (oddState === 'Moneyline') {
            if (oddValue < 2) {
                return roundNumber(-(100.0 * (1.0 / (oddValue - 1.0))), 0)
            } else {
                return roundNumber(100.0 * (oddValue - 1.0), 0)
            }
        }
    } else if (previousOddType === 'Fractional') {
        if (oddState === 'Decimal') {
            return oddValue + 1.0
        } else if (oddState === 'Moneyline') {
            if (oddValue < 1) {
                return roundNumber(-(100.0 * (1.0 / oddValue)), 0)
            } else {
                return roundNumber(100.0 * oddValue, 0)
            }
        }
    } else if (previousOddType === 'Moneyline') {
        if (oddState === 'Fractional') {
            if (oddValue < -100.0) {
                return roundNumber(-(100.0 / oddValue), 2)
            } else {
                return oddValue / 100.0
            }
        } else if (oddState === 'Decimal') {
            if (oddValue < -100.0) {
                return roundNumber(-(100.0 / oddValue) + 1.0, 2)
            } else {
                return roundNumber(oddValue / 100.0 + 1.0, 2)
            }
        }
    }
    return oddValue
}

const convertedOddForStakeCalculation = (
    oddValue: number,
    oddState: string,
    stake: string | number
) => {
    let convertedOdd = convertOddValue(oddValue, 'Fractional', oddState)
    if (convertedOdd > 0) {
        return roundNumber(Number(stake) * convertedOdd, 2)
    } else {
        return roundNumber(Number(stake) * (convertedOdd - 1), 2)
    }
}

export function calculateBetResult(
    oddValue: number,
    oddState: string,
    stake: string | number
) {
    switch (oddState) {
        case 'Decimal':
            return convertedOddForStakeCalculation(oddValue, oddState, stake)
        case 'Fractional':
            if (oddValue > 0) {
                return roundNumber(Number(stake) * oddValue, 2)
            } else {
                return roundNumber(Number(stake) * (oddValue - 1), 2)
            }
        case 'Moneyline':
            return convertedOddForStakeCalculation(oddValue, oddState, stake)
        default:
            return roundNumber((oddValue - 1) * roundNumber(stake, 2), 2)
    }
}

export function isDateBeforeStart(betStartDate: string) {
    return moment(new Date()).isBefore(
        moment.tz(betStartDate, moment.tz.guess())
    )
}

export function prepareOddForDisplay(
    bet: CreateEvent<BetPlacement, BetPlacement.Key, string>,
    oddState: string
) {
    //MATCHED ODD
    //NOTE: matchedODD IS AND OPTIONAL PROP AND IS ALWAYS A DECIMAL ODD
    //IF ODDSTATE IS NOT DECIMAL
    //CONVERT IT INTO USER SELECT ODD
    //ELSE JUST RETURN matchedOdd
    if (bet?.payload?.matchedOdd) {
        if (oddState !== 'Decimal') {
            let convertedOdd = convertOddValue(
                Number(bet?.payload?.matchedOdd),
                oddState,
                'Decimal'
            )
            if (oddState === 'Fractional') {
                return convertDecimalNumberToFraction(Number(convertedOdd))
            }
            return convertedOdd
        }
        return bet?.payload?.matchedOdd
    }
    //NON-MATCHED ODD
    //IF BET TAG IS DIFFERENT FROM ODDSTATE
    //IF IS AND IS FRACTIONAL CONVERT ODD AND TRANSFORM IT INTO A FRACTION
    //IF NOT FRACTIONAL JUST  CONVERT ODD TO THE ONE SELECTED BY USER
    //ELSE IT MEANS BET TAG EQUALS THE ODDSTATE SI RETURN ODD AS IS
    //IN CASE IS FRACTIONAL CONVERT IT INTO A FRACTION
    if (bet?.payload.details.side.value.odd.tag !== oddState) {
        let convertedOdd = convertOddValue(
            Number(bet?.payload.details.side.value.odd.value),
            oddState,
            bet?.payload.details.side.value.odd.tag
        )
        if (oddState === 'Fractional') {
            return convertDecimalNumberToFraction(convertedOdd)
        }
        return convertedOdd
    }
    return bet?.payload.details.side.value.odd.tag === 'Fractional'
        ? convertDecimalNumberToFraction(
              bet?.payload.details.side.value.odd.value
          )
        : bet?.payload.details.side.value.odd.value
}

export const getTitleFunc = (titlekvs: string[][], lang: string) => {
    let langConverted = langConverter(lang)
    return titlekvs.filter((elem: any) => elem[0] === langConverted)[0]?.length
        ? titlekvs.filter((elem: any) => elem[0] === langConverted)[0][1]
        : titlekvs.filter((elem: any) => elem[0] === 'en-US')[0][1]
}
