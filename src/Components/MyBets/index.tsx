import numeral from 'numeral'
import React, { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

import {
    handleActiveTab,
    hideBets,
    showBets as dispatchShowBets,
    useBetsDispatch,
    useBetsState
} from 'State/BetsContext'

import useMediaQuery from 'Hooks/useMediaQuery'
import Bets from './Bets'
import ConfirmAllBets from './ConfirmAllBets'
import ConfirmBet from './ConfirmBet'
import Matched from './Matched'
import Unmatched from './Unmatched'

import CloseX from 'Assets/closeX'
import { useTranslation } from 'react-i18next'
import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'
import { useI18LanguageContext } from 'State/LanguageState'
import { useOddType } from 'State/OddTypeContext'
import './style.scss'
import { UnAuthMessage } from './UnAuthMessage'
import { calculateBetResult } from './utils'
import useIsAuthenticated from 'Hooks/useAuth'
import { useManagerLoginState } from 'State/ManagerLoginContext'
import { useAccountContex } from 'State/AccountContext'

export default function MyBets() {
    const isMobile = useMediaQuery('(max-width: 930px)')
    const isSmallerDesktop = useMediaQuery('(max-width: 1310px)')
    const { pathname } = useLocation()
    const { oddState } = useOddType()
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()
    const { accountContracts: balanceContracts } = useAccountContex()

    const isAuthenticated = useIsAuthenticated()
    let { showBets, bets, showPlacement, showPlaceAllBets, activeTab } =
        useBetsState()
    const dispatch = useBetsDispatch()

    const { isAuthenticated: isManagerLoggedIn } = useManagerLoginState()

    useEffect(() => {
        if (isMobile && showBets) {
            hideBets(dispatch)
        }
        if (isSmallerDesktop && showBets) {
            hideBets(dispatch)
        }
        if (pathname === '/deposit' && showBets) {
            hideBets(dispatch)
        }
        if (pathname === '/first_deposit' && showBets) {
            hideBets(dispatch)
        }
        if (pathname === '/intro' && showBets) {
            hideBets(dispatch)
        }
        if (!isSmallerDesktop && !showBets && pathname !== '/deposit') {
            dispatchShowBets(dispatch)
        }
        if (isManagerLoggedIn && showBets) {
            hideBets(dispatch)
        }
        //eslint-disable-next-line
    }, [isMobile, pathname, isSmallerDesktop, isManagerLoggedIn])

    const totalLiability = bets.reduce(function (
        previousValue: number,
        currentValue: any
    ) {
        if (currentValue.sideTag === 'Lay' && Boolean(currentValue.stake)) {
            return (
                previousValue +
                calculateBetResult(
                    currentValue.odd.value,
                    oddState,
                    currentValue.stake
                )
            )
        } else {
            return previousValue
        }
    },
    0)

    const totalProfit = bets.reduce(function (
        previousValue: number,
        currentValue: any
    ) {
        if (currentValue.sideTag === 'Back' && Boolean(currentValue.stake)) {
            return (
                previousValue +
                calculateBetResult(
                    currentValue.odd.value,
                    oddState,
                    currentValue.stake
                )
            )
        } else {
            return previousValue
        }
    },
    0)

    const totalStake = bets.reduce(function (
        previousValue: number,
        currentValue: any
    ) {
        if (Boolean(currentValue.stake) && currentValue.sideTag === 'Back') {
            return previousValue + currentValue.stake
        } else {
            return previousValue
        }
    },
    0)

    const isEnoughBalance =
        balanceContracts.length > 0 &&
        balanceContracts[0]?.payload.totalMainBalance &&
        balanceContracts[0]?.payload?.totalBonusBalance
            ? Number(balanceContracts[0]?.payload.totalMainBalance) +
                  Number(balanceContracts[0]?.payload?.totalBonusBalance) >=
              Number(totalStake) + Number(totalLiability)
            : false

    const [fee, setFee] = React.useState('')

    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader
    } = useGlobalGamblingConfigContext()

    React.useEffect(() => {
        if (!GlobalGamblingConfigurationLoader)
            setFee(GlobalGamblingConfigurationContract[0]?.payload?.betFee)
    }, [GlobalGamblingConfigurationContract, GlobalGamblingConfigurationLoader])

    const totalPayout = bets.reduce(function (
        previousValue: number,
        currentValue: any
    ) {
        // back bets => stake + (profit - profit´s 5%)
        // lay bets => (stake - stake´s 5%) + liability
        if (currentValue.sideTag === 'Lay' && Boolean(currentValue.stake)) {
            return (
                previousValue +
                (Number(currentValue.stake) +
                    calculateBetResult(
                        currentValue.odd.value,
                        oddState,
                        currentValue.stake
                    ) -
                    Number(currentValue.stake) * Number(fee))
            )
        } else if (
            currentValue.sideTag === 'Back' &&
            Boolean(currentValue.stake)
        ) {
            return (
                previousValue +
                calculateBetResult(
                    currentValue.odd.value,
                    oddState,
                    currentValue.stake
                ) +
                Number(currentValue.stake) -
                calculateBetResult(
                    currentValue.odd.value,
                    oddState,
                    currentValue.stake
                ) *
                    Number(fee)
            )
        } else {
            return previousValue
        }
    },
    0)

    return (
        <>
            {showBets &&
                GlobalGamblingConfigurationContract?.length > 0 &&
                !GlobalGamblingConfigurationLoader && (
                    <div className="myBets">
                        <div
                            className={`myBets__header ${
                                lang === 'BR' ? 'small' : ''
                            }`}
                        >
                            <button
                                className={
                                    activeTab === 'betslip' ? 'active' : ''
                                }
                                onClick={() =>
                                    handleActiveTab(dispatch, 'betslip')
                                }
                            >
                                {t('Betslip')}
                            </button>
                            <button
                                className={
                                    activeTab === 'matched' ? 'active' : ''
                                }
                                onClick={() =>
                                    handleActiveTab(dispatch, 'matched')
                                }
                            >
                                {t('BetMatched')}
                            </button>
                            <button
                                className={
                                    activeTab === 'unmatched' ? 'active' : ''
                                }
                                onClick={() =>
                                    handleActiveTab(dispatch, 'unmatched')
                                }
                            >
                                {t('BetUnmatched')}
                            </button>
                            <button onClick={() => hideBets(dispatch)}>
                                <CloseX />
                            </button>
                        </div>
                        {showPlacement ? (
                            <ConfirmBet
                                fee={fee}
                                availableBalance={
                                    balanceContracts[0]?.payload
                                        .totalMainBalance
                                }
                                bonusAvailable={
                                    balanceContracts[0]?.payload
                                        ?.totalBonusBalance
                                }
                            />
                        ) : showPlaceAllBets ? (
                            <ConfirmAllBets
                                fee={fee}
                                betAmount={bets.length}
                                liabilityAmout={numeral(totalLiability).format(
                                    '$0,0.00'
                                )}
                                profitAmount={numeral(totalProfit).format(
                                    '$0,0.00'
                                )}
                                isEnoughBalance={isEnoughBalance}
                            />
                        ) : (
                            <>
                                {activeTab === 'betslip' ? (
                                    <Bets
                                        bets={bets}
                                        totalLiability={totalLiability}
                                        totalProfit={totalProfit}
                                        isAuthenticated={isAuthenticated}
                                        isEnoughBalance={isEnoughBalance}
                                        availableBalance={
                                            balanceContracts[0]?.payload
                                                .totalMainBalance
                                        }
                                        bonusAvailable={
                                            balanceContracts[0]?.payload
                                                .totalBonusBalance
                                        }
                                        totalPayout={totalPayout}
                                        GlobalGamblingConfigurationContract={
                                            GlobalGamblingConfigurationContract
                                        }
                                    />
                                ) : activeTab === 'matched' ? (
                                    <UnAuthMessage
                                        condition={isAuthenticated}
                                        component={<Matched />}
                                    />
                                ) : (
                                    <UnAuthMessage
                                        condition={isAuthenticated}
                                        component={<Unmatched />}
                                    />
                                )}
                            </>
                        )}
                    </div>
                )}
        </>
    )
}
