import {
    faCheckCircle,
    faMinus,
    faPlus,
    faTimesCircle
} from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import numeral from 'numeral'
import React from 'react'
import CurrencyInput from 'react-currency-input-field'
import { useTranslation } from 'react-i18next'
import {
    removeBet,
    showPlacement,
    updateBet,
    useBetsDispatch
} from 'State/BetsContext'
import { useI18LanguageContext } from 'State/LanguageState'
import { useOddType } from 'State/OddTypeContext'
import roundNumber from 'Utils/roundNumber'
import BackLayTooltip from './ToolTips/BackLayTooltip'
import LiabiltyTooltip from './ToolTips/LiabilityTooltip'
import {
    calculateBetResult,
    getTitleFunc,
    getValidOddValue,
    oddConfigs,
    translateDraw
} from './utils'
import InputFractional from './Inputs/InputFractional'
import InputOdds from './Inputs/InputOdds'
import { CreateEvent } from '@daml/ledger'
import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'

export default function Bet(prop: {
    bet: any
    GlobalGamblingConfigurationContract: readonly CreateEvent<
        GlobalGamblingConfiguration,
        GlobalGamblingConfiguration.Key,
        string
    >[]
}) {
    const { bet, GlobalGamblingConfigurationContract } = prop
    const { oddState } = useOddType()
    const dispatch = useBetsDispatch()
    const { lang } = useI18LanguageContext()

    const calculatedAmount = calculateBetResult(
        bet.odd.value,
        oddState,
        bet.stake
    )

    const { t } = useTranslation()

    const increaseOdds = () => {
        const betValue = getValidOddValue(
            Number(bet.odd.value) + oddConfigs[oddState].step,
            oddState,
            GlobalGamblingConfigurationContract
        )
        const updatedBet = {
            ...bet,
            odd: { tag: oddState, value: betValue }
        }
        updateBet(dispatch, updatedBet)
    }

    const decreaseOdds = () => {
        const betValue = getValidOddValue(
            Number(bet.odd.value) - oddConfigs[oddState].step,
            oddState,
            GlobalGamblingConfigurationContract
        )
        const updatedBet = {
            ...bet,
            odd: { tag: oddState, value: betValue }
        }
        updateBet(dispatch, updatedBet)
    }

    const [value, setValue] = React.useState<string | undefined>(() => {
        if (
            Number.isNaN(Number(bet.stake)) ||
            bet.stake === null ||
            bet.stake === undefined
        ) {
            return ''
        } else {
            return bet.stake.toString()
        }
    })

    const handleOnValueChange = (value: string | undefined): void => {
        if (!value || Number.isNaN(Number(value))) {
            setValue('')
        } else {
            setValue(value)
        }
        const updatedBet = {
            ...bet,
            stake: roundNumber(Number(value), 3)
        }
        updateBet(dispatch, updatedBet)
    }

    const handleBlurValidation = (value: string | undefined) => {
        let { minStake } = GlobalGamblingConfigurationContract[0]?.payload

        if (!value || Number.isNaN(Number(value))) {
            setValue('')
            const updatedBet = {
                ...bet,
                stake: roundNumber(Number(value), 3)
            }
            updateBet(dispatch, updatedBet)
            return
        }
        if (Number(value) <= Number(minStake)) {
            setValue(minStake)
            const updatedBet = {
                ...bet,
                stake: roundNumber(Number(minStake), 3)
            }
            updateBet(dispatch, updatedBet)
            return
        }
    }

    return (
        <>
            <div
                className={`bets ${
                    bet?.sideTag === 'Back' ? 'bets--back' : 'bets--lay'
                }`}
            >
                <h5>{getTitleFunc(bet?.title, lang)}</h5>
                <div className="bet__wrapper">
                    <div>
                        {t(bet?.sideTag)}{' '}
                        <BackLayTooltip isBack={bet?.sideTag === 'Back'} /> :{' '}
                        <strong>
                            {translateDraw(bet.participant, t('Draw'))}
                        </strong>
                    </div>
                </div>
                <div className="bet__boxWrapper">
                    <div className="bet__boxWrapper__box">
                        <div className="bet__boxWrapper__box__item">
                            <div className="title">
                                {lang === 'US'
                                    ? t('Odd')
                                    : t('Odd').substring(0, 8) + '...'}
                            </div>
                            <div>
                                {oddState === 'Fractional' ? (
                                    <InputFractional
                                        bet={bet}
                                        GlobalGamblingConfigurationContract={
                                            GlobalGamblingConfigurationContract
                                        }
                                    />
                                ) : (
                                    <InputOdds
                                        bet={bet}
                                        GlobalGamblingConfigurationContract={
                                            GlobalGamblingConfigurationContract
                                        }
                                    />
                                )}
                            </div>
                        </div>
                        <div className="buttons__block">
                            <button onClick={increaseOdds}>
                                <FontAwesomeIcon
                                    icon={faPlus}
                                    color="#ffffff"
                                />
                            </button>
                            <button onClick={decreaseOdds}>
                                <FontAwesomeIcon
                                    icon={faMinus}
                                    color="#ffffff"
                                />
                            </button>
                        </div>
                    </div>
                    <div className="bet__boxWrapper__box">
                        <div className="bet__boxWrapper__box__stake ">
                            <div className="title">{t('Stake')}</div>
                            <div>
                                <CurrencyInput
                                    intlConfig={{
                                        locale: 'en-US',
                                        currency: 'USD'
                                    }}
                                    prefix="$"
                                    className="bet__input"
                                    value={value}
                                    onValueChange={e => handleOnValueChange(e)}
                                    placeholder={t('PlacedBetsMarkupStake')}
                                    step={0.01}
                                    autoFocus
                                    allowNegativeValue={false}
                                    onBlur={() => handleBlurValidation(value)}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="bet__boxWrapper__box">
                        <div className="bet__boxWrapper__box__item">
                            <div className="title">
                                {bet.sideTag === 'Back'
                                    ? t('Profit')
                                    : t('Liability')}{' '}
                                {bet.sideTag === 'Back' ? null : (
                                    <LiabiltyTooltip />
                                )}{' '}
                            </div>
                            <div>
                                <strong>
                                    {Number.isNaN(Number(calculatedAmount))
                                        ? ' '
                                        : `${numeral(calculatedAmount).format(
                                              '$0,0.00'
                                          )}`}
                                </strong>
                            </div>
                        </div>
                    </div>
                    <button
                        className="closeButton"
                        onClick={() => removeBet(dispatch, bet)}
                        title="remove this bet"
                    >
                        <FontAwesomeIcon
                            className="checkIcon"
                            icon={faTimesCircle}
                            color="#cc0000"
                        />
                    </button>
                    <button
                        className="confirmButton"
                        onClick={() => showPlacement(dispatch, bet)}
                        title="place this bet"
                    >
                        <FontAwesomeIcon
                            className="checkIcon"
                            icon={faCheckCircle}
                            color="#00B71D"
                        />
                    </button>
                </div>
            </div>
        </>
    )
}
