import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { useLedger, useParty } from '@daml/react'
import React from 'react'
import { Link } from 'react-router-dom'
import { toast } from 'react-toastify'
import { v4 as uuidV4 } from 'uuid'

import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
    hidePlaceAll,
    removeAllBets,
    useBetsDispatch,
    useBetsState
} from 'State/BetsContext'

import TagManager from 'react-gtm-module'

import useIsAuthenticated from 'Hooks/useAuth'
import { useTranslation } from 'react-i18next'
import 'react-toastify/dist/ReactToastify.css'
import { logEvent } from 'Utils/analytics'
import { generateMapFromKeyValueArray } from 'Utils/generateMapFromKeyValueArray'
import HasErrorMessage from './HasErrorMessage'
import { isDateBeforeStart } from './utils'

export default function ConfirmAllBets({
    betAmount,
    liabilityAmout,
    profitAmount,
    isEnoughBalance,
    fee
}: {
    betAmount: number
    liabilityAmout: string
    profitAmount: string
    isEnoughBalance: boolean
    fee: string
}) {
    const party = useParty()
    const ledger = useLedger()
    const { bets: betsToPlace } = useBetsState()
    const isAuthenticated = useIsAuthenticated()
    const [isClicked, setIsClicked] = React.useState(false)
    const { t } = useTranslation()

    function generateBetArray() {
        return betsToPlace.map((bet: any) => ({
            eventKey: bet.eventkey,
            eventTitle: generateMapFromKeyValueArray(bet.title),
            eventStartDate: bet?.startDate,
            betPlacementId: uuidV4(),
            side: {
                tag: bet.sideTag,
                value: {
                    odd: bet.odd,
                    stake: bet?.stake.toString(),
                    outcome: bet.outcome.outcome
                }
            }
        }))
    }

    async function confirmBets() {
        if (isAuthenticated) {
            try {
                setIsClicked(true)
                const betDetailsList = generateBetArray()
                const bulkplacementObject = {
                    promotion: null,
                    betDetailsList
                }
                const serviceContracts = await ledger.query(Service, {
                    customer: party
                })
                const [response] = await ledger.exercise(
                    Service.RequestBulkBetPlacement,
                    serviceContracts[0]?.contractId,
                    bulkplacementObject
                )
                if (response.tag === 'Right') {
                    return [
                        setIsClicked(false),
                        toast.success('Your bets have been placed. Good luck!'),
                        removeAllBets(dispatch),
                        hidePlaceAll(dispatch),
                        logEvent('bet_multiple'),
                        TagManager.dataLayer({
                            dataLayer: {
                                event: 'multipleBetslip',
                                betDetailsList: generateBetArray()
                            }
                        })
                    ]
                } else {
                    setIsClicked(false)
                }
            } catch (error) {
                console.error('error multiple', error)
            }
        }
    }

    const dispatch = useBetsDispatch()

    function areAllBetsSameSide() {
        let objectOrganizer = betsToPlace?.reduce(
            (accumulator: any, current: any) => {
                const { title, participant, sideTag } = current
                const key = `${title}-${participant}`
                if (accumulator[key]) {
                    accumulator[key].push(sideTag)
                } else {
                    accumulator[key] = [sideTag]
                }
                return accumulator
            },
            {}
        )
        let preparedArray = Object.values(objectOrganizer)?.map((v: any) =>
            v?.every((a: any) => a === v[0])
        )
        return preparedArray?.every(p => p === true)
    }

    const allBetsSameSide = areAllBetsSameSide()

    const shouldButtonBeDisabled =
        betsToPlace.some((bet: any) => Number.isNaN(Number(bet.stake))) ||
        betsToPlace.some((bet: any) => Number(bet.stake) === 0) ||
        betsToPlace.some((bet: any) => bet.odd === 0) ||
        !isEnoughBalance ||
        !isAuthenticated ||
        isClicked ||
        betsToPlace.some((bet: any) => !isDateBeforeStart(bet?.startDate)) ||
        !allBetsSameSide

    return (
        <div className="confirmBet">
            <h3 className="confirmBet__title">{t('BetConfirmReviewOrder')}</h3>
            <h5 className="confirmBet__subtitle">
                {t('BetConfirmAreYouSure')}
            </h5>
            <div className="confirmBet__container">
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">
                        {t('NumberofEvents')}:
                    </div>
                    <div className="confirmBet__itemText">{betAmount}</div>
                </div>
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">
                        Total {t('Profit')}:
                    </div>
                    <div className="confirmBet__itemText">{profitAmount}</div>
                </div>
                <div className="confirmBet__item">
                    <div className="confirmBet__itemTitle">
                        Total {t('Liability')}:
                    </div>
                    <div className="confirmBet__itemText">{liabilityAmout}</div>
                </div>
            </div>
            <div className="confirmBet__action">
                <button
                    className="btn btn__grey confirmBet__button"
                    onClick={() => hidePlaceAll(dispatch)}
                >
                    {t('BetConfirmCancelBtn')}
                </button>
                <button
                    className="btn btn__green confirmBet__button"
                    onClick={() => confirmBets()}
                    title={
                        !isAuthenticated
                            ? 'Please log in to place your bet'
                            : 'Place your bet'
                    }
                    disabled={shouldButtonBeDisabled}
                >
                    {t('BetConfirmPlaceBetBtn')}
                </button>
            </div>
            <div className="confirmBet__footer">
                {fee?.length && (
                    <div className="confirmBet__footerInfo">
                        <FontAwesomeIcon icon={faInfoCircle} />
                        <p>
                            {t('BetConfirmMessageFee1')} {Number(fee) * 100}%{' '}
                            {t('BetConfirmMessageFee2')}
                        </p>
                    </div>
                )}
                <HasErrorMessage
                    textMessage={t('BetConfirmMessageDuplicates')}
                    condition={!allBetsSameSide}
                />
                <HasErrorMessage
                    textMessage={t('BetConfirmMessageNoMoney')}
                    condition={!isEnoughBalance}
                />
                <HasErrorMessage
                    condition={
                        betsToPlace.some((bet: any) =>
                            Number.isNaN(Number(bet.stake))
                        ) ||
                        betsToPlace.some((bet: any) => Number(bet.stake) === 0)
                    }
                    textMessage={t('BetConfirmMessageStake')}
                />
                <HasErrorMessage
                    condition={!isAuthenticated}
                    textMessage={t('BetConfirmMessageUnAuth')}
                />
                <HasErrorMessage
                    condition={betsToPlace.some(
                        (bet: any) => !isDateBeforeStart(bet?.startDate)
                    )}
                    textMessage={t('BetConfirmMessageEventStarted')}
                />
                {isAuthenticated && (
                    <p>
                        {t('BetConfirmMessageDeposit1')}{' '}
                        <Link to="/deposit" className="confirmBet__greenLink">
                            {t('BetConfirmMessageDeposit2')}
                        </Link>
                    </p>
                )}
            </div>
        </div>
    )
}
