import React from 'react'
import { toast } from 'react-toastify'
import { CreateEvent } from '@daml/ledger'
import { useParty, useLedger, useStreamQueries } from '@daml/react'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import {
    BetPlacement,
    Status
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import PlacedBetsMarkup from './PlacedBetsMarkup'
import { useTranslation } from 'react-i18next'

import './style.scss'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import { useI18LanguageContext } from 'State/LanguageState'

export default function Unmatched() {
    const party = useParty()
    const ledger = useLedger()
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()

    const { contracts: unMatchedBets, loading: unMatchedBetsLoader } =
        useStreamQueries(
            BetPlacement,
            () => {
                return [
                    {
                        customer: party,
                        status: 'Unmatched' as Status
                    }
                ]
            },
            [party]
        )

    const Undo = ({
        confirmDelete,
        title
    }: {
        confirmDelete: () => void
        title: string
    }) => {
        const handleClick = () => {
            confirmDelete()
        }

        return (
            <div className="confirmDelete__container">
                <p className="confirmDelete__text">
                    Are you sure you want to cancel <strong>{title}</strong>{' '}
                    bet?
                </p>
                <p className="confirmDelete__subtext">
                    {' '}
                    This action is not reversible
                </p>
                <button
                    className="confirmDelete__btn btn btn__green"
                    onClick={handleClick}
                >
                    confirm
                </button>
            </div>
        )
    }

    async function cancelBets(betPlacementId: string) {
        try {
            const serviceContract = await ledger.query(Service, {
                customer: party
            })
            const [cancelBetResult] = await ledger.exercise(
                Service.RequestCancelBetPlacement,
                serviceContract[0]?.contractId,
                { betPlacementId }
            )
            if (cancelBetResult.tag === 'Left') {
                toast.error('Something went wrong, please try again.')
            } else {
                toast.success('Bet cancelation has been requested.')
            }
        } catch (error) {
            console.error('error canceling bet', error)
        }
    }

    async function handleCancelButton(
        bet: CreateEvent<BetPlacement, BetPlacement.Key, string>
    ) {
        toast(
            <Undo
                confirmDelete={() =>
                    cancelBets(bet.payload.details.betPlacementId)
                }
                title={getEventTitleFromLang(
                    bet.payload.details.eventTitle,
                    lang
                )}
            />,
            {
                autoClose: false
            }
        )
    }

    const UnmatchedBetsMarkup = () =>
        unMatchedBets.length ? (
            <>
                {unMatchedBets.map(bet => (
                    <PlacedBetsMarkup
                        bet={bet}
                        isUnmatched={true}
                        key={bet.contractId}
                        handleCancelButton={handleCancelButton}
                    />
                ))}
            </>
        ) : (
            <p className="unmatched__message">{t('NoBetsFound')}</p>
        )
    const pageMarkup = unMatchedBetsLoader ? (
        <p className="unmatched__message">{t('Loading')}</p>
    ) : (
        <UnmatchedBetsMarkup />
    )

    return (
        <>
            <div className="unmatched__body">{pageMarkup}</div>
        </>
    )
}
