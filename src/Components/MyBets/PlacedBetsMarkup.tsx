import React from 'react'
import { BetPlacement } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { CreateEvent } from '@daml/ledger'
import {
    prepareOddForDisplay,
    retrieveBetDataAccordingToTypeOfBet,
    translateDraw
} from './utils'
import numeral from 'numeral'
import { useTranslation } from 'react-i18next'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTimesCircle } from '@fortawesome/free-solid-svg-icons'

import getFormattedDate from 'Containers/Dashboard/getFormattedDate'

import { useOddType } from 'State/OddTypeContext'

import './style.scss'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import { useI18LanguageContext } from 'State/LanguageState'

export default function PlacedBetsMarkup({
    bet,
    isUnmatched,
    handleCancelButton
}: {
    bet: CreateEvent<BetPlacement, BetPlacement.Key, string>
    isUnmatched: boolean
    handleCancelButton?: (
        bet: CreateEvent<BetPlacement, BetPlacement.Key, string>
    ) => void
}) {
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()
    //GET ODD STATE SELECTED BY USER
    const { oddState } = useOddType()

    const UnmatchedAction = () =>
        isUnmatched ? (
            <div className="unmatched__button">
                <button
                    className="closeButton"
                    onClick={() => handleCancelButton?.(bet)}
                    title="cancel this bet"
                >
                    <FontAwesomeIcon
                        className="checkIcon"
                        icon={faTimesCircle}
                        color="#cc0000"
                    />
                </button>
            </div>
        ) : null

    const { tag } = bet?.payload?.details?.side

    const translationForBackAndLay = () => {
        if (tag === 'Back') {
            return t('Back')
        }
        if (tag === 'Lay') {
            return t('Lay')
        }
        //left here as a fallback in
        //case some change occurs
        //in BE object and it's not covered
        return tag
    }
    return (
        <div className={`${isUnmatched ? 'unmatched' : 'matched'}__bet`}>
            <div
                className={`${
                    isUnmatched ? 'unmatched' : 'matched'
                }__betWrapper`}
            >
                <h5>
                    {getEventTitleFromLang(
                        bet.payload.details.eventTitle,
                        lang
                    )}
                </h5>
                <p>{getFormattedDate(bet.payload.details.eventStartDate)}</p>
                <div>
                    <strong>{translationForBackAndLay()}:</strong>
                    <p>
                        {translateDraw(
                            retrieveBetDataAccordingToTypeOfBet(bet),
                            t('Draw')
                        )}
                    </p>
                </div>
                <div>
                    <strong>{t('PlacedBetsMarkupStake')}:</strong>
                    <p>
                        {numeral(bet?.payload.details.side.value.stake).format(
                            '$0,0.00'
                        )}
                    </p>
                </div>
                <div>
                    <strong>{t('PlacedBetsMarkupOddValue')}:</strong>
                    <p>{prepareOddForDisplay(bet, oddState)}</p>
                </div>
            </div>
            <UnmatchedAction />
        </div>
    )
}
