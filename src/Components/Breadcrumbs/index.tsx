import React from 'react'
import B<PERSON><PERSON><PERSON>bWrapper from 'Components/Breadcrumbs/BreadcrumbWrapper'
import BreadcrumbItem from 'Components/Breadcrumbs/BreadcrumbItem'

type TBreadcrumbsData = {
    pageName: React.ReactNode
    path: string
    isActive: boolean
}[]

const Breadcrumbs = ({
    breadcrumbData
}: {
    breadcrumbData: TBreadcrumbsData
}) => {
    return (
        <BreadcrumbWrapper>
            {React.Children.toArray(
                breadcrumbData.map(data => (
                    <BreadcrumbItem path={data.path} isActive={data.isActive}>
                        {data.pageName}
                    </BreadcrumbItem>
                ))
            )}
        </BreadcrumbWrapper>
    )
}

export default Breadcrumbs
