import ReactTooltip from 'react-tooltip'

const BreadcrumbElipsis = ({ content }: { content: string }) => {
    if (content.length > 16) {
        return (
            <>
                {' '}
                <span data-for={content} data-tip={content}>
                    {`${content.substring(0, 15)}...`}
                </span>
                <ReactTooltip
                    id={content}
                    place="top"
                    clickable
                    class="tooltipTableHeader"
                />
            </>
        )
    }
    return <span>{content}</span>
}

export default BreadcrumbElipsis
