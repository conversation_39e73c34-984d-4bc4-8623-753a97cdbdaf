import React from 'react'
import { <PERSON> } from 'react-router-dom'

import './style.css'

const BreadcrumbItem = ({
    children,
    path,
    isActive = false,
    ...props
}: {
    children: React.ReactNode
    path: string
    isActive?: boolean
} & React.HTMLAttributes<HTMLLIElement>) => {
    return (
        <li
            className={`breadcrumb-item ${
                isActive ? 'breadcrumb-item--active' : ''
            }`}
            {...props}
        >
            {isActive ? (
                <span>{children}</span>
            ) : (
                <>
                    <Link to={path}>{children}</Link>
                    {' >'}
                </>
            )}
        </li>
    )
}

export default BreadcrumbItem
