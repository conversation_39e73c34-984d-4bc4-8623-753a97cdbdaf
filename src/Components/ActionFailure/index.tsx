import React from 'react'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import {
    ActionFailure,
    Actionable
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { CreateEvent } from '@daml/ledger'
import { useStreamQueries, useParty, useLedger } from '@daml/react'
import { toast } from 'react-toastify'
import './style.scss'
import CopyClipboard from 'Components/Clipboard'

export default function ActionFailureAlert() {
    const party = useParty()
    const ledger = useLedger()
    const { contracts: actionFailureContracts, loading: actionFailureLoading } =
        useStreamQueries(
            ActionFailure,
            () => [
                {
                    customer: party
                }
            ],
            [party]
        )

    const handleNotificationClick = async (
        action: Actionable,
        actionId: string
    ) => {
        try {
            ledger
                .query(Service, {
                    customer: party
                })
                .then(contract =>
                    ledger
                        .exercise(
                            Service.ArchiveActionFailureNotification,
                            contract[0].contractId,
                            {
                                actionList: [{ _1: action, _2: actionId }]
                            }
                        )
                        .then(() => toast.dismiss(actionId))
                        .catch(e => {
                            console.error(`error archiving notification: ${e}`)
                        })
                )
                .catch(e => {
                    console.error(
                        `error fetching service on notification: ${e}`
                    )
                })
        } catch (error) {
            console.error(error)
        }
    }

    React.useEffect(() => {
        if (!actionFailureLoading) {
            actionFailureContracts.length &&
                actionFailureContracts.map(
                    (
                        error: CreateEvent<
                            ActionFailure,
                            ActionFailure.Key,
                            string | any
                        >
                    ) =>
                        toast.error(
                            <span className="actionFailure__container">
                                <span className="actionFailure__content">
                                    <p>
                                        {error?.payload?.reason?.substring(
                                            0,
                                            45
                                        )}
                                        ...{' '}
                                        <CopyClipboard
                                            contenToCopy={
                                                error?.payload?.reason
                                            }
                                        />
                                    </p>
                                </span>
                                <button
                                    className="actionFailure__btn"
                                    onClick={async () =>
                                        await handleNotificationClick(
                                            error?.payload?.action,
                                            error?.payload?.actionId
                                        )
                                    }
                                >
                                    Ok
                                </button>
                            </span>,
                            {
                                autoClose: false,
                                hideProgressBar: false,
                                closeOnClick: false,
                                pauseOnHover: false,
                                draggable: false,
                                progress: undefined,
                                closeButton: false,
                                toastId: error?.payload?.actionId
                            }
                        )
                )
        }
        return () => {}
        //eslint-disable-next-line
    }, [actionFailureContracts, actionFailureLoading])

    return null
}
