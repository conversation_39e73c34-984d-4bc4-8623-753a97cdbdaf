@import '../../Styles/colors';

.tabs {
    padding: 25px 0;

    &__container {
        display: flex;
        flex-wrap: wrap;
        gap: 40px;
        list-style: none;
    }

    @media (max-width: 700px) {
        &__container {
            gap: 20px 40px;
            padding: 0 0 20px 0;
            border-bottom: 1px solid $purple;
        }
    }

    &__link {
        cursor: pointer;
        font-family: 'Montserrat-Bold', sans-serif;
        font-weight: 700;
        border-bottom: 3px solid transparent;

        &:hover {
            border-bottom: 3px solid $purple;
            color: $purple !important;
            font-weight: 700;
        }

        a:hover {
            text-decoration: none;
            color: $purple;
        }

        &--active {
            border-bottom: 3px solid $purple;
            color: $purple;

            a {
                text-decoration: none;
                color: $purple;
            }
        }

        &--unactive {
            border-bottom: 3px solid transparent;
            color: $darkGrey;

            a {
                text-decoration: none;
                color: inherit;
            }
        }
    }
}
