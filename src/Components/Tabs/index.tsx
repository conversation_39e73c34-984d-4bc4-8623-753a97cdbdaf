import React from 'react'
import { Link, useLocation, useRouteMatch } from 'react-router-dom'

import './style.scss'

interface TabRoutes {
    titleRoute: string
    urlRoute: string
}

export default function Tabs({ routes }: { routes: TabRoutes[] }) {
    const { url } = useRouteMatch()
    const { pathname } = useLocation()

    return (
        <div className="tabs">
            <ul className="tabs__container">
                {routes.map(route => (
                    <li
                        key={route.urlRoute}
                        className={`tabs__link ${
                            pathname === `${url}${route.urlRoute}`
                                ? 'tabs__link--active'
                                : 'tabs__link--unactive'
                        }`}
                    >
                        <Link to={`${url}${route.urlRoute}`}>
                            {route.titleRoute}
                        </Link>
                    </li>
                ))}
            </ul>
        </div>
    )
}
