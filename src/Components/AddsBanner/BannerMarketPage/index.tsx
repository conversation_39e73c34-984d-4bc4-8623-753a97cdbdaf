import React from 'react'
import { useNavigate } from 'react-router-dom'

import AddsBanner from '..'
import { getRandomAddUrl, getRandomMarketPromo } from './utils'

import useIsAuthenticated from 'Hooks/useAuth'
import useLoginRedirection from 'Hooks/useLoginRedirection'

export default function BannerSingleMarketPage({
    styleClass
}: {
    styleClass: string
}) {
    const { push } = useNavigate()
    const isAuthenticated = useIsAuthenticated()
    const login = useLoginRedirection()

    const handleClick = () => (isAuthenticated ? navigate('/deposit') : login())

    return (
        <>
            <span onClick={() => handleClick()}>
                <AddsBanner
                    Addkey={(Math.floor(Math.random() * 3) + 1).toString()}
                    urlBannerString={getRandomAddUrl()}
                    errorFallback={getRandomMarketPromo()}
                    className={styleClass}
                />
            </span>
        </>
    )
}
