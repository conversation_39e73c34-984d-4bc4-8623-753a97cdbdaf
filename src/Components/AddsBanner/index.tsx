import React from 'react'
import { useQuery } from 'react-query'
import { useI18LanguageContext } from 'State/LanguageState'

export default function AddsBanner({
    urlBannerString,
    className,
    Addkey,
    errorFallback
}: {
    urlBannerString: string
    Addkey: string
    className?: string
    errorFallback: any
}) {
    const { isLoading, isError, data } = useQuery(`adbanner__${Addkey}`, () =>
        fetch(urlBannerString).then(res => res.json())
    )
    const ad = data?.placements?.placement_1

    const { lang } = useI18LanguageContext()
    const langConverter = lang === 'BR' ? 'PT' : lang === 'MX' ? 'ES' : 'EN'

    let getAltText =
        !isLoading && !isError && data?.status === 'SUCCESS'
            ? data?.placements?.placement_1?.alt_text
            : null
    let imageTranslationCode = getAltText
        ? getAltText
              ?.replaceAll('[', '')
              ?.replaceAll(']', '')
              ?.replaceAll('{', '')
              ?.replaceAll('}', '')
              ?.split(',')
              ?.filter((a: string) => a.includes(langConverter))[0]
              ?.replace(`${langConverter}:`, '')
        : null

    return (
        <>
            {isLoading && null}
            {!isLoading &&
                !isError &&
                (data?.status === 'SUCCESS' ? (
                    <div
                        className={`${!className ? '' : className}`}
                        id={'_' + ad.banner_id}
                    >
                        <img
                            src={
                                imageTranslationCode
                                    ? `https://servedbyadbutler.com/getad.img/;libID=${imageTranslationCode}`
                                    : ad.image_url
                            }
                            alt={ad.alt_text}
                        />
                    </div>
                ) : (
                    <img alt={`promo ${Addkey}`} src={errorFallback} />
                ))}
            {isError && (
                <img alt={`promo ${Addkey}`} src={errorFallback} />
            )}
        </>
    )
}
