import React from 'react'
import ReactFlagsSelect from 'react-flags-select'
import { useI18LanguageContext } from 'State/LanguageState'

import './style.scss'

export default function FlagSelect() {
    const { lang, handleLang } = useI18LanguageContext()
    const onSelect = (code: string): void => handleLang(code)

    const showSelectedLabel = true
    const showOptionLabel = true

    const customLabels = {
        US: { primary: 'EN' },
        BR: { primary: 'PT' },
        MX: { primary: 'ES' }
    }

    return (
        <ReactFlagsSelect
            selected={lang}
            onSelect={onSelect}
            showSelectedLabel={showSelectedLabel}
            showOptionLabel={showOptionLabel}
            customLabels={customLabels}
            countries={['US', 'BR', 'MX']}
            className="menu-flags"
            selectButtonClassName="menu-flags__btn"
            selectedSize={14}
            optionsSize={14}
        />
    )
}
