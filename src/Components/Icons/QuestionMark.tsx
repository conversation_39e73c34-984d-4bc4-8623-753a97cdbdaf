import React from 'react'

interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: number
}

export default function QuestionMarkIcon({
    size = 16,
    className = '',
    ...props
}: IconProps) {
    const { fill } = props
    return (
        <svg
            className={className}
            width={size}
            height={size}
            viewBox="0 0 16 16"
            fill={fill ? fill : 'none'}
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M16 8C16 12.4193 12.418 16 8 16C3.58203 16 0 12.4193 0 8C0 3.58332 3.58203 0 8 0C12.418 0 16 3.58332 16 8ZM8.21468 2.64516C6.45671 2.64516 5.33548 3.38571 4.45503 4.70187C4.34097 4.87239 4.37913 5.10236 4.54261 5.22632L5.66194 6.07503C5.82984 6.20235 6.06906 6.17206 6.19952 6.00658C6.77577 5.27568 7.1709 4.85184 8.048 4.85184C8.707 4.85184 9.52213 5.27597 9.52213 5.915C9.52213 6.3981 9.12332 6.64619 8.47264 7.011C7.71381 7.43639 6.70968 7.96584 6.70968 9.29032V9.41935C6.70968 9.63313 6.883 9.80645 7.09677 9.80645H8.90323C9.117 9.80645 9.29032 9.63313 9.29032 9.41935V9.37635C9.29032 8.45823 11.9737 8.42 11.9737 5.93548C11.9737 4.06445 10.0329 2.64516 8.21468 2.64516ZM8 10.6452C7.18177 10.6452 6.51613 11.3108 6.51613 12.129C6.51613 12.9472 7.18177 13.6129 8 13.6129C8.81823 13.6129 9.48387 12.9472 9.48387 12.129C9.48387 11.3108 8.81823 10.6452 8 10.6452Z"
                fill={fill ? fill : '#A0A0A0'}
            />
        </svg>
    )
}
