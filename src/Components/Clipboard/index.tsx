import React from 'react'
import ReactTooltip from 'react-tooltip'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faClipboardCheck } from '@fortawesome/free-solid-svg-icons'

import "./style.scss"

export default function CopyClipboard({
    contenToCopy
}: {
    contenToCopy: string
}) {

    const [tooltip, showTooltip] = React.useState(true);

    const handleClick = () => {
        navigator.clipboard.writeText(contenToCopy)
    }

    const mouseLeaveEvent = () => {
        showTooltip(false);
        setTimeout(() => showTooltip(true), 50);
    }

    return (
        <>
            {tooltip && <ReactTooltip
                id={`tooltipClipboard-${contenToCopy}`}
                place="top"
                type="dark"
                effect="solid"
                class="tooltipMaxWidthMobile"
                afterShow={() => handleClick()}
            />}
            <FontAwesomeIcon
                data-for={`tooltipClipboard-${contenToCopy}`}
                data-tip={"copied"}
                data-event={"click"}
                className='clipboardCopy'
                icon={faClipboardCheck}
                onMouseLeave={() =>
                    mouseLeaveEvent()
                }
            />
        </>
    )
}

