import React from 'react'
import { Link } from 'react-router-dom'
import numeral from 'numeral'
import { useLedger, useParty, useStreamQueries } from '@daml/react'
import { GamblerUnverifiedIdentity } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { useTranslation } from 'react-i18next'

import { useOddType } from 'State/OddTypeContext'

import OddSelect from './OddSelect'
import LogoutBtn from './LogoutBtn'

import './style.scss'
import { useAccountContex } from 'State/AccountContext'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'
import Loader from 'react-loader-spinner'

const oddOptions = ['Decimal', 'Fractional', 'Moneyline']

const UserMenuOptions = ({
    closeDropdown,
    wrapperRef
}: {
    closeDropdown: () => void
    wrapperRef: any
}) => {
    const { t } = useTranslation()
    const party = useParty()
    const ledger = useLedger()
    const { oddState, updateOddState } = useOddType()

    const { gamblerIdentityContracts, loading } = useVerificationStatusContext()
    const { accountContracts } = useAccountContex()

    const {
        loading: gamblerUnverifiedIdentityLoading,
        contracts: gamblerUnverifiedIdentity
    } = useStreamQueries(
        GamblerUnverifiedIdentity,
        () => {
            return [{ customer: party }]
        },
        [party]
    )

    const isMenuLoading = gamblerUnverifiedIdentityLoading || loading

    const handleOddChange = async (value: string) => {
        updateOddState(value)
        try {
            const serviceContract = await ledger.query(Service, {
                customer: party
            })
            await ledger.exercise(
                Service.UpdatePreferences,
                serviceContract[0].contractId,
                {
                    newPreferences: [{ _1: 'Odd', _2: value }]
                }
            )
        } catch (e) {
            console.error(e)
            console.error(
                'Sorry, something went wrong. Please, try again later.'
            )
        }
    }

    const userName =
        gamblerIdentityContracts.length > 0 && !loading
            ? `${gamblerIdentityContracts[0].payload.jumioUserData.firstName} ${gamblerIdentityContracts[0].payload.jumioUserData.lastName}`
            : !gamblerUnverifiedIdentityLoading &&
              gamblerUnverifiedIdentity.length > 0
            ? `${gamblerUnverifiedIdentity[0]?.payload?.firstName} ${gamblerUnverifiedIdentity[0]?.payload?.lastName}`
            : 'Not defined'

    return (
        <div ref={wrapperRef} className="accountDropdown__content">
            <span className="accountDropdown__content__username">
                {isMenuLoading ? (
                    <Loader
                        type="ThreeDots"
                        color="#a000e1"
                        height={10}
                        width={50}
                    />
                ) : (
                    <>
                        <b>{userName}</b>
                        <Link
                            to="/account"
                            onClick={closeDropdown}
                            className="profile-link"
                        >
                            {t('AccountDropdownViewProfile')}
                        </Link>
                    </>
                )}
            </span>
            <span>
                <b>{t('AccountDropdownTotalBalance')}:</b>{' '}
                {!accountContracts.length
                    ? '-'
                    : numeral(
                          accountContracts[0]?.payload.totalMainBalance
                      ).format('$0,0.00')}
            </span>
            <span>
                <b>{t('AccountDropdownBonusAvailable')}:</b>{' '}
                {!accountContracts.length
                    ? '-'
                    : numeral(
                          accountContracts[0]?.payload?.totalBonusBalance
                      ).format('$0,0.00')}
            </span>
            <span>
                <b>{t('AccountDropdownBetBalance')}:</b>{' '}
                {!accountContracts.length
                    ? '-'
                    : numeral(
                          accountContracts[0]?.payload.totalBetBalance
                      ).format('$0,0.00')}
            </span>
            <Link to="/account/payments" onClick={closeDropdown}>
                {t('AccountDropdownPayments')}
            </Link>
            <Link to="/account/history" onClick={closeDropdown}>
                {t('AccountDropdownHistory')}
            </Link>
            <Link to="/account/rewards" onClick={closeDropdown}>
                {t('AccountDropdownRewards')}
            </Link>
            <LogoutBtn />
            <OddSelect
                value={oddState}
                options={oddOptions}
                onChange={handleOddChange}
                name="oddType"
                id="oddType"
                className="accountDropdown__selectOdd"
            />
            <Link to="/deposit" className="linkButton">
                <span className="btn btn__green" onClick={closeDropdown}>
                    {t('AccountDropdownDeposit')}
                </span>
            </Link>
            <Link to="/withdrawal" className="linkButton">
                <span className="btn btn__grey" onClick={closeDropdown}>
                    {t('AccountDropdownWithdrawal')}
                </span>
            </Link>
        </div>
    )
}

export default UserMenuOptions
