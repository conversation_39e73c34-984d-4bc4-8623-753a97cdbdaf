import React, { useState, useRef } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faUser } from '@fortawesome/free-solid-svg-icons/faUser'
import { useTranslation } from 'react-i18next'
import { faCaretDown, faCaretUp } from '@fortawesome/free-solid-svg-icons'

import useMediaQuery from 'Hooks/useMediaQuery'
import useOutsideDetector from 'Hooks/useOutsideDetector'
import DropdownMenu from './DropdownMenu'

import './style.scss'

export default function AccountDropdown() {
    const displayName = useMediaQuery('(max-width: 1082px)')
    const { t } = useTranslation()
    const [isShowMenu, setIsShowMenu] = useState(false)
    const closeDropdown = () => setIsShowMenu(false)

    const wrapperRef = useRef(null)
    const exclude = useRef(null)
    useOutsideDetector(wrapperRef, () => setIsShowMenu(false), exclude)

    return (
        <>
            <button
                className="accountDropdown"
                type="button"
                onClick={() => setIsShowMenu(!isShowMenu)}
                ref={exclude}
            >
                <FontAwesomeIcon icon={faUser} />
                {!displayName ? t('account') : ''}
                {isShowMenu ? (
                    <FontAwesomeIcon
                        className="accountDropdown__arrow"
                        icon={faCaretUp}
                    />
                ) : (
                    <FontAwesomeIcon
                        className="accountDropdown__arrow"
                        icon={faCaretDown}
                    />
                )}
            </button>
            {isShowMenu ? (
                <DropdownMenu
                    closeDropdown={closeDropdown}
                    wrapperRef={wrapperRef}
                />
            ) : null}
        </>
    )
}
