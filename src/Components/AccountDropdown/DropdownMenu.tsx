import React from 'react'
import AdminMenuOptions from './AdminMenuOptions'
import UserMenuOptions from './UserMenuOptions'
import { useManagerLoginState } from 'State/ManagerLoginContext'

import './style.scss'

const DropdownMenu = ({
    closeDropdown,
    wrapperRef
}: {
    closeDropdown: () => void
    wrapperRef: any
}) => {
    const { isAuthenticated: isAdmin } = useManagerLoginState()

    return isAdmin ? (
        <AdminMenuOptions
            closeDropdown={closeDropdown}
            wrapperRef={wrapperRef}
        />
    ) : (
        <UserMenuOptions
            closeDropdown={closeDropdown}
            wrapperRef={wrapperRef}
        />
    )
}

export default DropdownMenu
