import React from 'react'
import { useAuth0 } from '@auth0/auth0-react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

import { signOut, useUserDispatch } from 'State/UserContext'
import { isLocalDev } from 'config'
import { logoutParams } from 'Constants/Auth0Constants'
import { cache } from 'Utils/cache'

const { remove } = cache()
export default function LogoutBtn() {
    const { t } = useTranslation()
    const userDispatch = useUserDispatch()
    const navigate = useNavigate()
    const { logout } = useAuth0()

    const handleLogoutAuth0 = () => {
        logout(logoutParams)
    }

    // CHANGE HERE AUTH0
    const handleLogoutClick = isLocalDev
        ? () => {
              remove('bets')
              signOut(userDispatch, history)
          }
        : () => {
              remove('bets')
              handleLogoutAuth0()
          }

    return (
        <button
            onClick={handleLogoutClick}
            className="accountDropdown__content__logout"
        >
            {t('AccountDropdownLogout')}
        </button>
    )
}
