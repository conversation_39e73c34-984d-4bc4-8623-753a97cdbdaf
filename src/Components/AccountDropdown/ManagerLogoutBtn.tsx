import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

import { signOutManagerLogin, useManagerLoginDispatch } from 'State/ManagerLoginContext'

export default function ManagerLogoutBtn() {
    const { t } = useTranslation()
    const managerDispatch = useManagerLoginDispatch()
    const navigate = useNavigate()

    return (
        <button
            onClick={() => signOutManagerLogin(managerDispatch, history)}
            className="accountDropdown__content__logout"
        >
            {t('AccountDropdownLogout')}
        </button>
    )
}
