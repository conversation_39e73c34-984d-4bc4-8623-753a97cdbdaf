import React from 'react'
import { <PERSON> } from 'react-router-dom'
import ManagerLogoutBtn from './ManagerLogoutBtn'

import './style.scss'

const AdminMenuOptions = ({
    closeDropdown,
    wrapperRef
}: {
    closeDropdown: () => void
    wrapperRef: any
}) => {
    return (
        <div ref={wrapperRef} className="accountDropdown__content">
            <Link to="/admin" onClick={closeDropdown} className="profile-link">
                Admin panel
            </Link>
            <ManagerLogoutBtn />
        </div>
    )
}

export default AdminMenuOptions
