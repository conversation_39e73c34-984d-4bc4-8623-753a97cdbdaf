import React from 'react'
import { useTranslation } from 'react-i18next'

type OptionValue = string | number

type Props<T extends OptionValue> = {
    value: T
    onChange: (newValue: T) => void
    options: readonly T[]
    name: string
    id: string
    className?: string
}

export default function OddSelect<T extends OptionValue>({
    value,
    onChange,
    options,
    name,
    id,
    className
}: Props<T>) {
    const { t } = useTranslation()
    return (
        <select
            name={name}
            id={id}
            className={`register__input ${className ? className : ''}`}
            value={value}
            onChange={(event: React.FormEvent<HTMLSelectElement>) => {
                const selectedOption =
                    options[event.currentTarget.selectedIndex]
                onChange(selectedOption)
            }}
        >
            {options.map(item => (
                <option value={item} key={item}>
                    {item === 'Moneyline'
                        ? t('AmericanOdds')
                        : t(`${item}Odds`)}
                </option>
            ))}
        </select>
    )
}
