@import '../../Styles/colors';

.accountDropdown {
    color: $white;
    background: none;
    border: none;
    cursor: pointer;
    svg {
        margin-right: 3px;
    }
    &__content {
        background: $white;
        border: 1px solid $grayInpuBorder;
        box-sizing: border-box;
        border-radius: 0 0 5px 5px;
        width: 313px;
        position: absolute;
        top: 73px;
        right: 15px;
        display: flex;
        flex-direction: column;
        padding: 27px 23px 20px;
        font-family: OpenSans, sans-serif;
        font-size: 0.938rem;
        line-height: 25px;
        color: $darkGrey;
        &__username {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .profile-link {
                margin-bottom: 0;
                text-decoration: underline;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 350px) {
            width: 100%;
            right: 0;
        }
        &__logout {
            background: none;
            border: none;
            display: flex;
            font-size: 0.938rem;
            line-height: 25px;
            color: $darkGrey;
        }
        span {
            margin-bottom: 23px;
        }
        button {
            margin-bottom: 23px;
            cursor: pointer;
            &:hover {
                opacity: 0.8;
            }
        }
        a {
            margin-bottom: 23px;
            cursor: pointer;
            color: $darkGrey;
            text-decoration: none;
            &:focus {
                color: $darkGrey;
                text-decoration: none;
            }
            &:visited {
                color: $darkGrey;
                text-decoration: none;
            }
            &:hover {
                opacity: 0.8;
            }
        }
        .linkButton {
            margin-bottom: 15px;
            .btn {
                max-width: 260px;
                width: 100%;
                font-size: 0.8rem;
                margin: 0;
            }
        }
    }

    &__arrow {
        margin-left: 16px;
        font-size: 1rem;
        pointer-events: none;
    }

    &__selectOdd {
        margin: 0px 0px 23px;
        width: 100%;
        max-width: 260px;
        font-size: 1rem;
    }

    &__loader {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
