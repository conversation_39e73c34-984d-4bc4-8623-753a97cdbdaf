import { isRunningOnHub, damlHubEnvironment } from '@daml/hub-react'

export enum DeploymentMode {
    DEV,
    PROD_DAML_HUB,
    PROD_OTHER
}

const hubEnv = damlHubEnvironment(true)

export const deploymentMode: DeploymentMode =
    process.env.NODE_ENV === 'development'
        ? DeploymentMode.DEV
        : isRunningOnHub(true)
        ? DeploymentMode.PROD_DAML_HUB
        : DeploymentMode.PROD_OTHER

export const isHubDeployment = deploymentMode === DeploymentMode.PROD_DAML_HUB

export const wsBaseUrl =
    deploymentMode === DeploymentMode.DEV
        ? 'wss://jsonapi-dev.gambyl.bc.intellecteu.com/'
        : hubEnv?.wsURL

export const httpBaseUrl = hubEnv?.baseURL || undefined

export const isLocalDev = process.env.NODE_ENV === 'development'

export const Auth0EnvVariables = {
    domain: process.env.REACT_APP_AUTH0_DOMAIN,
    clientId: process.env.REACT_APP_AUTH0_CLIENT_ID,
    redirectUri: process.env.REACT_APP_AUTH0_CALLBACK_URL
}

export const isProd =
    process.env.REACT_APP_IS_PROD !== undefined &&
    process.env.REACT_APP_IS_PROD.toLocaleLowerCase() === 'true'
