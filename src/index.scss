@import './Styles/colors';
@import './Styles/fonts';
@import './Styles/buttons';
@import './Styles/general';
@import './Styles/documents';
@import './Styles/inputs';
@import './Styles/eventCards';

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
        'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
        'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
        monospace;
}

html {
    font-size: 16px;
    @media (max-width: 450px) {
        font-size: 12px;
    }
}
