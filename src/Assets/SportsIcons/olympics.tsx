import React from 'react'

export default function Olympics() {
    return (
        <svg
            version="1.0"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1280.000000 640.000000"
            preserveAspectRatio="xMidYMid meet"
        >
            <g
                transform="translate(0.000000,640.000000) scale(0.100000,-0.100000)"
                stroke="none"
            >
                <path
                    d="M1900 6154 c-297 -35 -522 -99 -748 -212 -212 -107 -374 -224 -542
-392 -314 -313 -509 -694 -587 -1145 -26 -152 -23 -510 5 -665 100 -545 378
-996 816 -1325 340 -255 791 -405 1214 -405 l99 0 22 -112 c123 -613 546
-1158 1116 -1438 498 -244 1079 -278 1604 -95 679 237 1199 832 1343 1534 l23
111 142 2 c79 1 159 2 179 3 l37 0 22 -111 c13 -62 39 -163 60 -225 45 -141
163 -384 244 -504 335 -500 866 -831 1460 -910 156 -21 408 -19 569 5 773 113
1420 654 1666 1391 24 74 50 163 57 198 16 75 25 81 132 81 151 1 396 49 586
115 136 48 336 148 458 231 868 589 1167 1702 709 2639 -112 228 -235 395
-433 585 -596 574 -1474 735 -2239 411 -387 -164 -703 -432 -938 -793 -117
-180 -241 -475 -276 -659 l-13 -66 -81 -7 c-45 -3 -97 -6 -116 -6 l-36 0 -16
88 c-75 389 -259 745 -538 1036 -190 197 -372 330 -605 442 -573 275 -1238
271 -1810 -11 -236 -116 -388 -228 -581 -427 -221 -230 -375 -484 -473 -778
-33 -100 -65 -225 -76 -301 l-7 -47 -106 5 c-59 3 -109 7 -111 9 -2 2 -12 47
-23 101 -77 390 -298 790 -594 1073 -311 296 -687 484 -1114 555 -91 15 -425
28 -500 19z m350 -579 c336 -43 614 -172 854 -397 233 -218 415 -553 451 -833
l7 -50 -69 -23 c-107 -37 -298 -134 -413 -209 -396 -261 -696 -647 -839 -1081
-28 -85 -56 -197 -83 -332 l-14 -65 -124 2 c-547 11 -1056 344 -1295 847 -90
189 -125 317 -146 528 -24 254 25 520 141 766 151 317 391 558 704 708 274
130 542 176 826 139z m4405 -10 c120 -20 290 -75 395 -125 255 -124 499 -347
640 -587 98 -165 181 -399 196 -553 l6 -55 -105 -48 c-601 -279 -1043 -863
-1157 -1529 -8 -47 -17 -69 -28 -72 -34 -10 -242 -18 -285 -12 l-44 7 -22 112
c-80 412 -257 754 -540 1049 -210 219 -413 361 -683 479 -62 27 -114 53 -116
58 -2 5 8 58 21 118 63 279 194 522 389 721 262 268 556 408 958 456 65 8 284
-3 375 -19z m4290 -69 c494 -76 913 -384 1130 -831 203 -419 203 -888 0 -1310
-114 -238 -321 -470 -551 -617 -187 -120 -457 -209 -681 -225 -106 -7 -93 -21
-113 127 -43 319 -199 680 -408 950 -255 327 -580 560 -972 696 -77 27 -94 36
-92 51 24 143 138 399 243 547 136 190 342 366 554 470 153 76 262 110 460
145 79 14 331 12 430 -3z m-6634 -1676 c25 0 29 -4 34 -37 88 -547 359 -1008
785 -1340 141 -110 345 -226 513 -293 l58 -23 -6 -41 c-33 -207 -112 -409
-233 -591 -223 -337 -560 -564 -960 -647 -88 -18 -140 -22 -292 -22 -180 0
-216 4 -370 41 -148 35 -337 120 -472 211 -335 228 -574 597 -637 984 l-8 48
46 16 c450 166 827 469 1073 867 71 113 160 302 202 427 37 111 86 320 86 369
0 26 4 30 33 34 17 3 52 3 76 1 24 -2 56 -4 72 -4z m4349 -29 c0 -57 41 -264
75 -375 163 -538 527 -981 1025 -1245 94 -49 316 -141 342 -141 8 0 22 -6 31
-12 15 -12 15 -18 -3 -85 -142 -523 -561 -935 -1085 -1068 -130 -33 -196 -40
-370 -39 -202 0 -291 14 -465 70 -445 143 -806 501 -955 947 -55 164 -82 327
-55 327 21 0 236 113 320 167 339 221 597 517 767 881 76 162 121 312 169 569
5 25 23 29 132 32 l72 1 0 -29z m683 -122 c337 -169 592 -445 730 -789 33 -84
82 -249 75 -255 -7 -8 -182 86 -262 139 -290 196 -514 502 -605 830 -17 60
-31 111 -31 113 0 7 14 1 93 -38z m-5868 -131 c-95 -245 -262 -470 -472 -634
-75 -59 -208 -144 -225 -144 -12 0 40 136 90 235 98 198 261 393 436 524 104
78 200 135 204 122 2 -5 -13 -51 -33 -103z m1544 50 c199 -120 417 -361 531
-588 54 -108 95 -214 78 -204 -237 139 -459 381 -577 627 -45 92 -82 187 -74
187 3 0 22 -10 42 -22z m2751 -123 c-72 -161 -189 -324 -326 -458 -78 -76
-164 -145 -164 -132 0 4 20 48 44 99 63 131 154 262 254 368 83 87 212 202
219 194 2 -2 -10 -34 -27 -71z"
                />
            </g>
        </svg>
    )
}
