import React from 'react'

export default function Wrestling() {
    return (
        <svg viewBox="0 0 16 14" xmlns="http://www.w3.org/2000/svg">
            <path d="M15.915 11.4467L14.632 8.77102C14.632 8.77102 15.1714 6.52603 15.172 6.49281C15.1751 6.3009 15.0985 6.11583 14.9584 5.9782L12.4233 3.48249C12.4975 3.5948 12.5562 3.71853 12.5873 3.85591C12.6597 4.17607 12.5863 4.49296 12.4105 4.74594L12.7884 6.2459C12.8958 6.67279 12.6445 7.11465 12.2076 7.25915C11.841 7.379 12.0788 7.30122 10.6879 7.75623C10.4368 7.88605 10.2735 8.1309 10.2607 8.40394L10.1044 11.7357C10.0841 12.1654 10.4334 12.5292 10.8842 12.5484C10.8964 12.5489 10.9089 12.5491 10.9211 12.5491C11.3557 12.5491 11.7171 12.2229 11.7368 11.8051L11.872 8.92359L13.0605 8.3385L12.9772 8.6678C12.9344 8.83712 12.9525 9.01534 13.0285 9.17378L14.4288 12.0941C14.6158 12.484 15.099 12.6581 15.5115 12.4787C15.9219 12.3 16.1025 11.8378 15.915 11.4467Z" />
            <path d="M10.8901 5.39949L10.8864 5.77007L8.3574 6.59725C8.00183 6.71335 7.81245 7.08249 7.93452 7.42166C8.03137 7.69082 8.29544 7.86017 8.57845 7.86017C8.65163 7.86017 8.72635 7.84877 8.7994 7.82467L11.7837 6.84876C12.0566 6.75951 12.2409 6.51613 12.2436 6.24112L12.2566 4.92491C12.0952 5.07979 11.888 5.19436 11.6476 5.24386L10.8901 5.39949Z" />
            <path d="M6.96054 2.39348C7.51717 2.69643 8.21051 2.54455 8.585 2.06775C8.62864 2.29135 8.73815 2.5058 8.91667 2.68171C9.39962 3.15651 10.1956 3.16869 10.6941 2.70833C11.193 2.24797 11.2054 1.49004 10.7228 1.01443C10.2401 0.539163 9.44431 0.526693 8.94562 0.987054C8.89566 1.03343 8.85385 1.08465 8.81343 1.13634C8.74831 0.801453 8.54005 0.494255 8.20572 0.312217C7.60258 -0.0155447 6.83497 0.184712 6.49136 0.75942C6.14779 1.33438 6.35733 2.06572 6.96054 2.39348Z" />
            <path d="M8.52496 8.48454C8.52364 8.48382 6.33827 7.18843 6.33692 7.18765L7.10846 5.89785L7.42656 6.34481C7.47312 6.41015 7.53584 6.46037 7.6021 6.505C7.7441 6.32624 7.94145 6.1843 8.18082 6.10599L9.41142 5.70341L9.35902 5.71435C9.23266 5.74019 9.10689 5.72497 8.99732 5.6781L7.59863 5.07882L6.5101 4.61234L9.11699 5.17158C9.21624 5.19261 9.31623 5.19208 9.4101 5.17258L11.533 4.73631C11.9006 4.66103 12.134 4.3158 12.0548 3.96519C11.9758 3.61487 11.6152 3.39176 11.2458 3.4678L9.26964 3.87391L6.80902 3.34589L7.60027 3.36139C7.55184 3.32257 7.50082 3.28607 7.44387 3.25517L7.0423 3.03666C6.79302 2.90128 6.49735 2.86553 6.22046 2.93778C5.94361 3.01003 5.7082 3.18416 5.56591 3.4217L2.3101 9.12972L0.223087 11.2358C-0.0867162 11.5483 -0.0721261 12.0412 0.256103 12.3365C0.583743 12.6321 1.10128 12.6178 1.41086 12.305L3.58266 10.1135C3.63905 10.057 3.68587 9.99234 3.72204 9.9224L4.68984 8.04474C4.69197 8.04599 4.69361 8.04777 4.69574 8.04906L7.28397 9.58299L7.32122 11.7831C7.32863 12.2087 7.69302 12.5492 8.13797 12.5492C8.1425 12.5492 8.14702 12.5492 8.15155 12.5492C8.60256 12.5421 8.96246 12.1879 8.95525 11.7577L8.91053 9.13328C8.90607 8.86862 8.76086 8.62451 8.52496 8.48454Z" />
        </svg>
    )
}
