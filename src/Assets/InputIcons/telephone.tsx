import React from 'react'

export default function TelephoneIcon() {
    return (
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M3.8334 3.06372C3.95075 3.06372 3.95075 3.08507 4.18383 3.30914C4.33066 3.45029 5.5391 4.4589 6.57225 5.49592C6.63248 5.55637 6.67724 5.59393 6.67724 5.68722C6.67724 5.78052 6.49718 5.94187 6.49718 5.94187C6.49718 5.94187 5.66845 6.73322 5.5912 6.81061C5.51394 6.888 5.07657 7.2726 4.96665 8.03448C4.85672 8.79636 5.62643 10.1105 7.7478 12.2574C9.86916 14.4043 11.031 14.9383 12.0629 14.8566C12.6939 14.8066 13.0472 14.5262 14.0781 13.4536C14.1558 13.3728 14.2894 13.2213 14.4242 13.2213C14.5591 13.2213 14.632 13.3029 14.7966 13.4748C14.8959 13.5785 15.9078 15.0252 16.88 16.0001C16.9822 16.1025 17.0578 16.1995 17.0578 16.3495C17.0578 16.4994 16.786 16.744 16.786 16.744L15.9259 17.6191C15.9259 17.6191 15.6307 17.896 15.3255 18.0117C15.0204 18.1273 14.0272 18.0304 13.6478 17.9135C10.6573 16.9922 7.84182 15.3125 6.07761 13.4536C4.34111 11.6238 2.68565 8.76533 2.13585 6.01719C2.06844 5.68025 2.01988 5.08915 2.09783 4.84335C2.17579 4.59755 2.42642 4.34361 2.42642 4.34361C2.42642 4.34361 3.41189 3.35806 3.53883 3.24301C3.66578 3.12796 3.71605 3.06372 3.8334 3.06372Z"
                fill="#F5F9FC"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10 1C14.9706 1 19 5.02944 19 10H17.5C17.5 5.85786 14.1421 2.5 10 2.5L10 1ZM10 4C13.3137 4 16 6.68629 16 10H14.5C14.5 7.51472 12.4853 5.5 10 5.5V4ZM1.51401 8.15951C1.18127 7.26104 0.94033 6.34346 1.013 5.36852C1.05889 4.76827 1.25612 4.33196 1.72435 3.83539C2.19258 3.33881 3.12412 2.45136 3.12412 2.45136C3.73604 1.84729 4.50476 1.85111 5.11668 2.45136C5.49531 2.82222 5.87011 3.20073 6.24491 3.57923C6.60824 3.94244 6.97539 4.30565 7.33872 4.67269C7.97741 5.31882 7.98123 6.072 7.34254 6.71431C6.8836 7.17311 6.66953 7.36781 6.20294 7.81895C6.08056 7.93748 6.06909 8.03688 6.1341 8.18599C6.44006 8.92006 6.8837 9.57384 7.38089 10.1856C8.38291 11.4167 9.51496 12.5101 10.865 13.3551C11.1557 13.5347 11.5793 13.7603 11.7752 13.833C11.9712 13.9056 12.0353 13.8903 12.1615 13.7603C12.6166 13.2901 12.8422 13.0954 13.3088 12.6327C13.9207 12.0287 14.6856 12.0248 15.2975 12.6327C16.0471 13.3745 16.7929 14.12 17.5348 14.8694C18.1582 15.4964 18.1544 16.2648 17.5272 16.8957C17.1027 17.3239 16.6718 17.7844 16.2575 18.1765C15.624 18.7758 14.9418 19.0405 14.1081 18.9947C12.8919 18.9297 11.7713 18.5244 10.689 18.0006C8.28719 16.8345 6.23726 15.2173 4.52006 13.1756C3.25033 11.6616 2.20242 10.0176 1.51401 8.15951ZM4.12187 3.66995C4.01398 3.66995 3.96776 3.72914 3.85104 3.83514C3.77323 3.90581 3.43231 4.24383 2.82828 4.8492C2.67465 5.00518 2.57395 5.15866 2.52617 5.30964C2.45449 5.53611 2.49914 6.08073 2.56112 6.39117C3.06662 8.9232 4.86644 11.418 6.463 13.1039C8.08503 14.8166 10.3958 16.5031 13.1454 17.352C13.4942 17.4597 14.4073 17.549 14.6879 17.4424C14.8749 17.3714 15.0589 17.2508 15.2398 17.0807L16.0306 16.2745C16.1972 16.1242 16.2805 16.0031 16.2805 15.911C16.2805 15.7728 16.2111 15.6834 16.1171 15.589C15.2232 14.6908 14.7306 14.194 14.6393 14.0984C14.488 13.94 14.421 13.8648 14.297 13.8648C14.173 13.8648 14.0502 14.0044 13.9788 14.0788C13.0309 15.0671 12.7061 15.3255 12.126 15.3715C11.1773 15.4468 9.33497 14.4202 7.38456 12.4421C5.43416 10.4641 4.49411 8.56526 4.59517 7.8633C4.69623 7.16133 5.09836 6.80698 5.16939 6.73567C5.21674 6.68814 5.4944 6.42133 6.00237 5.93524C6.11273 5.83614 6.16791 5.75793 6.16791 5.70062C6.16791 5.61466 6.12675 5.58006 6.07138 5.52436C5.12149 4.56889 4.57905 4.02613 4.44406 3.89607C4.22977 3.68962 4.22977 3.66995 4.12187 3.66995Z"
                fill="#A000E2"
            />
        </svg>
    )
}
