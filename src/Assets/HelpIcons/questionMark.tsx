import React from 'react'

export default function QuestionMark({
    color,
    height,
    width
}: {
    color?: string
    height?: string
    width?: string
}) {
    return (
        <svg
            width={width ? width : '41'}
            height={height ? height : '41'}
            viewBox="0 0 41 41"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M20.5 0.25C9.31702 0.25 0.25 9.32029 0.25 20.5C0.25 31.6862 9.31702 40.75 20.5 40.75C31.683 40.75 40.75 31.6862 40.75 20.5C40.75 9.32029 31.683 0.25 20.5 0.25ZM20.4997 36.8307C11.4744 36.8307 4.16906 29.5283 4.16906 20.5C4.16906 11.4777 11.4747 4.16936 20.4997 4.16936C29.5217 4.16936 36.8304 11.475 36.8304 20.5C36.8304 29.5251 29.528 36.8307 20.4997 36.8307ZM29.257 15.9927C29.257 21.4677 23.3436 21.552 23.3436 23.5753V24.0927C23.3436 24.6338 22.9049 25.0726 22.3638 25.0726H18.6366C18.0955 25.0726 17.6567 24.6338 17.6567 24.0927V23.3857C17.6567 20.467 19.8695 19.3002 21.5417 18.3627C22.9756 17.5588 23.8545 17.0121 23.8545 15.9475C23.8545 14.5393 22.0582 13.6046 20.606 13.6046C18.7125 13.6046 17.8384 14.5009 16.6097 16.0517C16.2784 16.4697 15.674 16.5474 15.2489 16.2251L12.9769 14.5024C12.5599 14.1862 12.4665 13.5988 12.7611 13.1663C14.6903 10.3334 17.1476 8.74191 20.9733 8.74191C24.9801 8.74191 29.257 11.8696 29.257 15.9927ZM23.9292 29.6451C23.9292 31.5361 22.3907 33.0745 20.4997 33.0745C18.6087 33.0745 17.0703 31.5361 17.0703 29.6451C17.0703 27.7541 18.6087 26.2157 20.4997 26.2157C22.3907 26.2157 23.9292 27.7541 23.9292 29.6451Z"
                fill={color ? color : '#A000E2'}
            />
        </svg>
    )
}
