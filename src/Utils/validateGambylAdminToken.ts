import { httpBaseUrl, isLocalDev } from 'config'
import jwtDecode from 'jwt-decode'
import { isTokenExpired } from './isLocalTokenExpired'

interface IResponse {
    result: {
        userId: string
        primaryParty: string
    }
    status: number
}

interface ILocalToken {
    exp: number
    ['https://daml.com/ledger-api']: {
        actAs: string[]
        applicationId: string
        ledgerId: string
        readAs: string[]
    }
    ledgerId: string
    party: string
    partyName: string
}

interface IDamlUserToken {
    aud: string
    exp: number
    iss: string
    jti: string
    sub: string
}

interface IReturnValidateFunction {
    token: string
    party: string
    isGambylAdmin: boolean
    isGambylManager: boolean
}

const expiredTokenResponse = {
    token: '',
    party: '',
    isGambylAdmin: false,
    isGambylManager: false
}

const URL_TO_QUERY = `${httpBaseUrl}/v1/user`

export const isAdminTokenExpired = (tkn: string) => {
    const decodedJwt = jwtDecode<IDamlUserToken>(tkn)
    const timeNow = new Date().getTime() / 1000
    const isExpired = decodedJwt.exp - timeNow <= 0
    return isExpired
}

const localDevValidation = (token: string) => {
    const decodedLocalToken = jwtDecode<ILocalToken>(token)

    if (isTokenExpired(token)) {
        return expiredTokenResponse
    }

    const isGambyl = decodedLocalToken.partyName === 'Gambyl'
    const isMarketingManager =
        decodedLocalToken.partyName === 'MarketingManager'
    const isEventManager = decodedLocalToken.partyName === 'EventManager'

    return {
        token,
        party: decodedLocalToken.party,
        isGambylAdmin: isGambyl,
        isGambylManager: isMarketingManager || isEventManager
    }
}

const deployedEnvsValidation = async (token: string) => {
    const decodedJwt = jwtDecode<IDamlUserToken>(token)

    if (!decodedJwt.exp) {
        return expiredTokenResponse
    }

    const isExpired = isAdminTokenExpired(token)
    if (isExpired) {
        return expiredTokenResponse
    }

    const subToLowerCase = decodedJwt.sub.toLocaleLowerCase()
    const isGambyl = subToLowerCase === 'gambyl'
    const isEventManager = subToLowerCase === 'eventmanager'
    const isMarketingManager = subToLowerCase === 'marketingmanager'

    if (isGambyl || isEventManager || isMarketingManager) {
        const data = () =>
            fetch(URL_TO_QUERY, {
                headers: new Headers({
                    Authorization: `Bearer ${token}`
                })
            })
                .then(resp => resp.json())
                .then((data: IResponse) => {
                    const party = data.result.primaryParty
                    return {
                        token,
                        party,
                        isGambylAdmin: isGambyl,
                        isGambylManager: isMarketingManager || isEventManager
                    }
                })
                .catch(err => {
                    console.error('error on parties fetch', err)
                    return expiredTokenResponse
                })
        return await data()
    } else {
        return expiredTokenResponse
    }
}

export const validateGambylAdminToken = async (
    token: string
): Promise<IReturnValidateFunction> => {
    if (isLocalDev) {
        return localDevValidation(token)
    }
    return await deployedEnvsValidation(token)
}
