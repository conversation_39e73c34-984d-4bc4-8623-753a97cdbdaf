export const prepareSubmarketPath = (submarketValue: string) => {
    submarketValue = submarketValue.replace(/[\s-]+/g, '-')
    submarketValue = submarketValue.replace(/[^a-zA-Z0-9-]/g, '')
    submarketValue = submarketValue
        .split('-')
        .map(word => {
            if (word.match(/^[IVXLCDM]+$/)) {
                return word
            }
            return word.toLowerCase()
        })
        .join('-')
    return submarketValue
}

export const prepareGeoPath = (geo: string) =>
    geo
        .toLowerCase()
        .replaceAll('.', '')
        .replaceAll(',', '')
        .replaceAll(' ', '-')

// CHANGE HERE
export const prepareEventTitle = (eventTitle: string) =>
    eventTitle.replace(' vs ', '-').replace(/[^a-zA-Z0-9-_]/g, '')

export const getGeoPath = (geo: string, sportName: string) =>
    `/events/${sportName}/${prepareGeoPath(geo)}`

export const getSubmarketPath = (
    submarketValue: string,
    sportName: string,
    geo: string
) =>
    `/events/${sportName}/${prepareGeoPath(geo)}/${prepareSubmarketPath(
        submarketValue
    )}`

export const generateURLPathSingleEvent = (
    sportName: string,
    tournament: string,
    eventName: string,
    assetLabel: string
) => {
    const tournamentURL = prepareSubmarketPath(tournament)
    const eventTitleURL = prepareEventTitle(eventName)
    const path = `/${sportName}/${tournamentURL}/${eventTitleURL}/${assetLabel}`
    return path.toLocaleLowerCase()
}
