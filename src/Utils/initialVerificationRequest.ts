import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { IdentityVerificationRequest } from '@daml.js/jumio-integration/lib/JumioIntegration/Identity'
import Ledger from '@daml/ledger'
import { Party } from '@daml/types'
import { toast } from 'react-toastify'

//make initial gambling service request
export const initialVerificationRequest = async (
    ledgerKYCFORM: Ledger,
    partyKYCFORM: Party,
    jumioParty: Party,
    callback: () => void,
    localeString: "US" | "PT" | "ES"
) => {
    try {
        const gamblingServiceQuery = await ledgerKYCFORM.query(Service, {
            customer: partyKYCFORM
        })

        if (gamblingServiceQuery.length > 0) {
            ledgerKYCFORM.exercise(
                Service.RequestIdentityVerification,
                gamblingServiceQuery[0].contractId,
                {
                    locale: localeString,
                    integrationParty: jumioParty
                }
            ).then(() => {
                const idenReq = ledgerKYCFORM.streamQueries(
                    IdentityVerificationRequest,
                    []
                )
                idenReq.on('change', async response => {
                    if (response.length > 0) {
                        const { contractId, payload: { redirectUrl } } = response[0]
                        ledgerKYCFORM.exercise(
                            Service.StartIdentityVerification,
                            gamblingServiceQuery[0].contractId,
                            {
                                idVerReqCid: contractId
                            }
                        ).then(() => {
                            window.open(redirectUrl, '_blank')
                        }).catch((e) => {
                            console.error("error on StartIdentityVerification", e)
                            toast.error("Something went wrong, please try again later")
                        })
                        idenReq.close()
                        callback()
                    }
                })
            }).catch(e => {
                console.error("error on RequestIdentityVerification", e)
                toast.error("Something went wrong, please try again later")
                callback()
            })
        }
    } catch (error) {
        console.error('error on service query', error)
        toast.error("Something went wrong, please try again later")
        callback()
    }
}
