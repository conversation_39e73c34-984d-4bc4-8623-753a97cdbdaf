/**
 *
 * @param phoneNumber type string
 * @returns returns the last for chars of a string and replaces the rest by Xs
 * if the the param is smaller than 4 chars returns the string passed as param
 */
export function maskPhoneNumber(phoneNumber: string) {
    const maskSymbol = 'X'
    const numberOfDigitsToShow = 4
    let phoneNumberLength = phoneNumber.length

    if (phoneNumberLength < numberOfDigitsToShow) {
        return phoneNumber
    }

    let numberOfMaskSymbolsToRepeat = phoneNumberLength - numberOfDigitsToShow

    return `${maskSymbol.repeat(
        numberOfMaskSymbolsToRepeat
    )}${phoneNumber.slice(-numberOfDigitsToShow)}`
}
