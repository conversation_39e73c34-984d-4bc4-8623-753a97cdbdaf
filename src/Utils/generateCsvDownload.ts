import <PERSON> from 'papaparse'

/**
 *
 * @param data the data you wish to dowload as a csv is an array of type unkown
 * @param generatedFileName a string with the name of the downloaded file
 * @summary Generates a csv file and dowloads it with the given data. The downloaded filename is composed by the string passed on the argument generatedFileName plus the current users date time.
 */
export const generateCSVDownload = (
    data: unknown[],
    generatedFileName: string
) => {
    // Convert array of objects to CSV
    const csv = Papa.unparse(data)

    // Create a blob with the CSV data
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })

    //Get current date to display on download file name
    const now = new Date()
    const dateString = now.toLocaleDateString().replace(/\//g, '-') // Format as DD-MM-YYYY
    const timeString = now.toLocaleTimeString().replace(/:/g, '-') // Format as HH-MM-SS
    const dateTimeString = `${dateString}_${timeString}`

    // Create a link element
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${generatedFileName}-${dateTimeString}.csv`)

    // Append to the document and trigger the download
    document.body.appendChild(link)
    link.click()

    // Clean up and remove the link
    document.body.removeChild(link)
}
