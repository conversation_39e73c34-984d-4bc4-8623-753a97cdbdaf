import { computeLocalCreds } from 'Utils/credentials'
import { DeploymentMode, deploymentMode } from 'config'
import { PartyInfo } from '@daml/ledger'

interface Parties {
    result: PartyInfo[]
    status: number
}

interface PublicToken {
    access_token: string
    expires_in: number
    party: string
    token_type: string
}

interface PublicPartyData {
    token: string | undefined
    party: string | undefined
}

const MOCK_TOKEN = `Bearer mock`
const EMPTY_RESPONSE = { token: undefined, party: undefined }

export const adminParties = [
    'QuickBooks',
    'Operator',
    'MoneyMatrix',
    'Jumio',
    'Gambyl',
    'Exberry',
    'EnetPulse',
    'Public',
    'EventManager',
    'MarketingManager'
]

const getHostNameToQueryParties = () => {
    const { hostname } = window?.location
    let hostnameForQuery =
        hostname === 'localhost' ? 'localhost:3000' : hostname
    let protocol = hostname === 'localhost' ? 'http' : 'https'
    const localEndpointForParties = `${protocol}://${hostnameForQuery}/v1/parties`
    return localEndpointForParties
}

export const fetchAllPartiesQuery = async (): Promise<PartyInfo[]> => {
    const queryEnpoint = getHostNameToQueryParties()
    let parties = await fetch(queryEnpoint, {
        headers: new Headers({
            Authorization: MOCK_TOKEN
        })
    })
        .then(resp => resp.json())
        .then((data: Parties) => data.result)
        .catch(err => {
            console.error('error on parties fetch', err)
            return [] as PartyInfo[]
        })
    return parties
}

const generateLocalPublicToken = async (): Promise<PublicPartyData> => {
    const localPublicToken = await fetchAllPartiesQuery()
        .then(data => {
            let user = data?.filter(party => party.displayName === 'Public')[0]
            if (user && user.displayName) {
                let credentials = computeLocalCreds(
                    user?.identifier,
                    user?.displayName
                )
                return credentials
            }
            return EMPTY_RESPONSE
        })
        .catch(err => {
            console.error('error on getting public token', err)
            return EMPTY_RESPONSE
        })
    return localPublicToken
}

const generatePublicTokenDeployedEnvs = async (): Promise<PublicPartyData> => {
    const urlToQuery = process.env.REACT_APP_PUBLIC_TOKEN ?? ''
    return fetch(urlToQuery)
        .then(res => res.json())
        .then((data: PublicToken) => {
            return { token: data.access_token, party: data.party }
        })
        .catch(e => {
            console.error('error getting public token', e)
            return EMPTY_RESPONSE
        })
}

/**
 * Fetch the public token and public party on the ledger.
 * @returns the public token and public party on the ledger.
 */
export const getPublicToken = async (): Promise<PublicPartyData> => {
    return deploymentMode === DeploymentMode.DEV
        ? await generateLocalPublicToken()
        : (await generatePublicTokenDeployedEnvs()) || EMPTY_RESPONSE
}
