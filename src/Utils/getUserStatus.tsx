import {
    GamblerIdentity,
    RejectedIdentity,
    PendingIdentity
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { CreateEvent } from '@daml/ledger'

export interface UserStatus {
    gamblerIdentity: readonly CreateEvent<GamblerIdentity, any>[]
    rejectedIdentity: readonly CreateEvent<RejectedIdentity, any>[]
    pendingIdentity: readonly CreateEvent<PendingIdentity, any>[]
    isLoading: boolean
}

export default function getUserStatus({
    gamblerIdentity,
    rejectedIdentity,
    pendingIdentity,
    isLoading
}: UserStatus): string {
    const isVerificationComplete = gamblerIdentity.length > 0
    const isVerificationPending = pendingIdentity.length > 0
    const isVerificationRejected = rejectedIdentity.length > 0

    if (isVerificationComplete) {
        return 'success'
    }

    if (isVerificationPending) {
        return 'pending'
    }

    if (isVerificationRejected) {
        return 'failed'
    }

    if (
        !isLoading &&
        !isVerificationPending &&
        !isVerificationRejected &&
        !isVerificationComplete
    ) {
        return 'notStarted'
    }

    return ''
}
