import jwtDecode from 'jwt-decode'

interface ILocalToken {
    exp: number
    ['https://daml.com/ledger-api']: {
        actAs: string[]
        applicationId: string
        ledgerId: string
        readAs: string[]
    }
    ledgerId: string
    party: string
    partyName: string
}


export const isTokenExpired = (token: string): boolean => {
    const decodedToken = jwtDecode<ILocalToken>(token)
    const tokenTimeStamp = decodedToken?.exp
    if (!tokenTimeStamp) {
        return true
    } else {
        const timeNow = new Date().getTime() / 1000
        return tokenTimeStamp - timeNow <= 0
    }
}
