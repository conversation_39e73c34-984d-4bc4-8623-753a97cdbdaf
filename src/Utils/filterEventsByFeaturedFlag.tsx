import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'

export const filterEventsByFeaturedFlag = (
    isFeatured: boolean,
    eventContracts: readonly CreateEvent<
        EventInstrument,
        EventInstrument.Key,
        string
    >[]
) => {
    if (isFeatured) {
        return eventContracts.filter(evt => evt.payload.featured === true)
    }
    return eventContracts
}
