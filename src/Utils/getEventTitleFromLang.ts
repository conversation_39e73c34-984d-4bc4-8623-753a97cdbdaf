import * as damlTypes from '@daml/types'

export const langConverter = (lang: string) =>
    lang === 'BR' ? 'pt' : lang === 'MX' ? 'es' : 'en-US'

export const getEventTitleFromLang = (
    eventTitle: damlTypes.Map<string, string>,
    lang: string
) => {
    const langConverted = langConverter(lang)
    return eventTitle.entriesArray().filter(t => t[0] === langConverted)[0]
        ?.length
        ? eventTitle.entriesArray().filter(t => t[0] === langConverted)[0][1]
        : eventTitle.entriesArray().filter(t => t[0] === 'en-US')[0][1]
}
