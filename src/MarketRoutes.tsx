import React from 'react'
import { Route } from 'react-router-dom'
import LoaderSpinner from 'Components/Loader'
import { useMarketMapContext } from 'State/MarketMapContext'

import MarketPage from './Containers/MarketPages/MarketPage'
import GeographyPage from 'Containers/MarketPages/GeographyPage'
import LeaguePage from 'Containers/MarketPages/LeaguePage'

import { getGeoPath, getSubmarketPath } from 'Utils/getPathFromSubmarket'
import { checkIfNonSportEvent } from 'Utils/checkIfNonSportEvent'


export default function MarketRoutes() {
    const { marketContracts, loadingMarkets } = useMarketMapContext()
    let marketsQuery = marketContracts[0]?.payload?.map?.entriesArray()
    return (
        <>
            {loadingMarkets && <LoaderSpinner />}
            {!loadingMarkets && marketsQuery?.length
                ? marketsQuery.map(market => React.Children.toArray(
                    <>
                        <Route
                            exact
                            path={`/events/${checkIfNonSportEvent(market[0])}`}
                        >
                            <MarketPage
                                marketInfo={market}
                                market={checkIfNonSportEvent(market[0])}
                            />
                        </Route>
                        {React.Children.toArray(market[1].entriesArray().map(([geography]) =>
                            <Route exact path={getGeoPath(geography.value, checkIfNonSportEvent(market[0]))}>
                                <GeographyPage
                                    marketInfo={market}
                                    market={checkIfNonSportEvent(market[0])}
                                    geography={geography.value} />
                            </Route>))}
                        {React.Children.toArray(market[1].entriesArray().map(([geography, tournaments]) => {
                            return React.Children.toArray(tournaments.map(league => {
                                if (league.tag !== "Tournament") return null;
                                return <Route exact path={getSubmarketPath(league.value, checkIfNonSportEvent(market[0]), geography.value.toLowerCase())}>
                                    <LeaguePage
                                        marketInfo={market}
                                        market={checkIfNonSportEvent(market[0])}
                                        geography={geography.value}
                                        league={league.value} />
                                </Route>
                            }))
                        }))}
                    </>
                ))
                : null}
        </>
    )
}
