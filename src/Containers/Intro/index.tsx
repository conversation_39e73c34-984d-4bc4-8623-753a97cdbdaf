import React from 'react'
import { useNavigate } from 'react-router-dom'

import './style.scss'
import { useTranslation } from 'react-i18next'
import { useI18LanguageContext } from 'State/LanguageState'

interface IVideoLink {
    US: string
    BR: string
    MX: string
}

//CHANGE HERE VIDEO URLS TO GAMBYL ONES
const videoLink: IVideoLink = {
    US: 'https://player.vimeo.com/video/958028565?h=77ad76f438&amp;badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479',
    BR: 'https://player.vimeo.com/video/1008586193?h=8e66778e9f&amp;badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479',
    MX: 'https://player.vimeo.com/video/1000462014?h=970f983edb&amp;badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479'
}

const Intro = () => {
    const { push } = useNavigate()
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()

    return (
        <div className="content__container intro">
            <div className="intro__container">
                <h2>{t('welcomeMessage')}</h2>
                <div>
                    <iframe
                        src={videoLink[lang as string as keyof IVideoLink]}
                        title="Introduction to Betting Exchanges"
                        allowFullScreen
                        allow="autoplay; fullscreen; picture-in-picture"
                    />
                </div>
                <button
                    className="registerbtn__nextStep"
                    onClick={() => navigate('/first_deposit')}
                >
                    {t('DepositWelcome')}
                </button>
            </div>
        </div>
    );
}

export default Intro
