@import '../../Styles/colors';

.intro {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGSignIn.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    font-family: 'Montserrat-Bold', sans-serif;
    gap: 100px;
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%;
    padding-bottom: 30px;
    padding-top: 30px;

    &__container {
        background: $white;
        border-radius: 5px;
        padding: 40px;
        max-width: 800px;
        display: flex;
        flex-direction: column;

        h2 {
            font-size: 2.188rem;
            line-height: 2.75rem;
            margin-bottom: 25px;
            text-align: center;
        }

        div {
            display: flex;
        }

        iframe {
            min-height: 300px;
            border: 0;
            width: 100%;
            flex: 1;
        }
    }
}

@media (max-width: 1270px) {
    .intro {
        &__container {
            max-width: 650px;
        }
    }
}

@media (max-width: 920px) {
    .intro {
        &__container {
            max-width: 550px;
        }
    }
}

@media (max-width: 820px) {
    .intro {
        &__container {
            max-width: 450px;
            h2 {
                font-size: 1.53rem;
                line-height: 2.75rem;
                margin-bottom: 20px;
            }
        }
    }
}

@media (max-width: 720px) {
    .intro {
        &__container {
            max-width: 400px;
            padding: 20px;
        }
    }
}

@media (max-width: 474px) {
    .intro {
        &__container {
            max-width: 350px;
            padding: 15px;

            h2 {
                font-size: 1.063rem;
                line-height: 2.75rem;
                margin-bottom: 15px;
            }
        }
    }
}

@media (max-width: 474px) {
    .intro {
        &__container {
            max-width: 290px;
        }
    }
}
