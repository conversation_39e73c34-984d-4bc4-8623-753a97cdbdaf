import { useLedger } from '@daml/react'
import React from 'react'
import { Redirect, useHistory } from 'react-router-dom'
import { toast } from 'react-toastify'

import { VerifiedIdentity } from '@daml.js/da-marketplace/lib/Marketplace/Regulator/Model'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service/index'
import { BlockedService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service/index'
import { GamblerUnverifiedIdentity } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'

import { signOut, useUserDispatch } from 'State/UserContext'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'
import {
    signOutManagerLogin,
    useManagerLoginDispatch,
    useManagerLoginState
} from 'State/ManagerLoginContext'

import { TAction, TParamsGetPathToRedirect, TStateReducer } from './models'

import LoaderSpinner from 'Components/Loader'
import { useAuth0 } from '@auth0/auth0-react'
import { isLocalDev } from 'config'
import { logoutParams } from 'Constants/Auth0Constants'
import usePartyToken from 'Hooks/usePartyToken'
import { cache } from 'Utils/cache'

const { remove } = cache()

const initState: TStateReducer = {
    blockedService: [],
    verifiedIdentity: [],
    marketingManagerIdentity: [],
    eventManagerIdentity: [],
    gamblerUnverifiedIdentity: [],
    loading: true
}

function reducer(state: TStateReducer, action: TAction) {
    switch (action.type) {
        case 'UPDATEDATA':
            const {
                blockedService,
                verifiedIdentity,
                marketingManagerIdentity,
                eventManagerIdentity,
                gamblerUnverifiedIdentity,
                loading
            } = action.payload
            return {
                blockedService,
                verifiedIdentity,
                marketingManagerIdentity,
                eventManagerIdentity,
                gamblerUnverifiedIdentity,
                loading
            }
        default:
            throw new Error('Error on reducer')
    }
}

function CheckVerify() {
    //STATE
    const [
        {
            loading,
            blockedService,
            verifiedIdentity,
            marketingManagerIdentity,
            eventManagerIdentity,
            gamblerUnverifiedIdentity
        },
        dispatch
    ] = React.useReducer(reducer, initState)

    //HOOKS
    const { partyToDAMLProvider: party } = usePartyToken()
    const ledger = useLedger()
    const history = useHistory()
    const { logout } = useAuth0()

    //CONTEXTS
    const { status, loading: loadingStatus } = useVerificationStatusContext()
    const userDispatch = useUserDispatch()
    const { isAuthenticated: isManagerAuthenticated } = useManagerLoginState()
    const managerLoginDispatch = useManagerLoginDispatch()

    //LOGOUT METHOD
    const handleLogout = () => {
        remove('bets')
        // CHANGE HERE AUTH0
        if (isManagerAuthenticated) {
            return signOutManagerLogin(managerLoginDispatch, history)
        }
        if (isLocalDev) {
            return signOut(userDispatch, history)
        }
        return logout(logoutParams)
    }

    // METHOD TO REDIRECT USER ACCORDING TO SERVICES
    const getPathToRedirect = ({
        blockedService,
        verifiedIdentity,
        marketingManagerIdentity,
        eventManagerIdentity,
        gamblerUnverifiedIdentity
    }: TParamsGetPathToRedirect) => {
        if (blockedService.length > 0) {
            signOut(userDispatch, history)
            if (blockedService[0]?.payload.reason === 'Self Exclusion') {
                return {
                    pathname: '/blocked-account',
                    state: { reason: 'Self Exclusion' }
                }
            }
            if (blockedService[0]?.payload.reason === 'Under Legal Age') {
                return {
                    pathname: '/blocked-account',
                    state: { reason: 'Under Legal Age' }
                }
            }
            return '/blocked-account'
        }
        if (marketingManagerIdentity.length > 0) {
            return '/admin/createpromotion'
        }
        if (eventManagerIdentity.length > 0) {
            return '/admin'
        }
        //IF has verified identity on gambyl proceed to app
        if (verifiedIdentity.length > 0) {
            return '/'
        }
        //IF status of verified identity pending proceed to app
        if (status === 'pending') {
            return '/'
        }
        //IF local dev go to identity setup
        if (isLocalDev && !gamblerUnverifiedIdentity.length) {
            return '/identity_setup'
        }
        //IF has no gamblerUnverifiedIdentity.length
        if (!gamblerUnverifiedIdentity.length) {
            return '/identity_setup'
        }
        //IF has no country code set from previous login go to identity setup
        if (
            gamblerUnverifiedIdentity.length > 0 &&
            gamblerUnverifiedIdentity[0]?.payload?.countryCode === '-'
        ) {
            return '/identity_setup'
        }
        return '/'
    }

    //EFFECTS
    React.useEffect(() => {
        Promise.all([
            ledger.query(VerifiedIdentity, { customer: party }),
            ledger.query(BlockedService, {
                service: { customer: party }
            }),
            ledger.query(MarketingManagerService, {
                customer: party
            }),
            ledger.query(EventManagerService, {
                customer: party
            }),
            ledger.query(GamblerUnverifiedIdentity, {
                customer: party
            })
        ])

            .then(
                ([
                    verifiedIdentityQuery,
                    blockedServiceQuery,
                    marketingManagerQuery,
                    eventManagerQuery,
                    GamblerUnverifiedIdentityQuery
                ]) => {
                    // CHANGE HERE AUTH0
                    dispatch({
                        type: 'UPDATEDATA',
                        payload: {
                            loading: false,
                            blockedService: blockedServiceQuery,
                            verifiedIdentity: verifiedIdentityQuery,
                            marketingManagerIdentity: marketingManagerQuery,
                            eventManagerIdentity: eventManagerQuery,
                            gamblerUnverifiedIdentity:
                                GamblerUnverifiedIdentityQuery
                        }
                    })
                }
            )
            .catch(e => {
                toast.error('something went wrong, please try again later')
                console.log(e)
                handleLogout()
            })
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    return (
        <>
            {loadingStatus || loading ? (
                <>
                    <div className="verify__loaderOverlay" />
                    <LoaderSpinner />
                </>
            ) : null}
            {/* CHANGE HERE AUTH0 */}
            {!loading && !loadingStatus && status?.length && (
                <Redirect
                    to={getPathToRedirect({
                        blockedService,
                        verifiedIdentity,
                        marketingManagerIdentity,
                        eventManagerIdentity,
                        gamblerUnverifiedIdentity
                    })}
                />
            )}
        </>
    )
}

export default CheckVerify
