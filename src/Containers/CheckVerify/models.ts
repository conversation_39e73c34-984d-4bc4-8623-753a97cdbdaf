import { CreateEvent } from '@daml/ledger'

import { BlockedService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { VerifiedIdentity } from '@daml.js/da-marketplace/lib/Marketplace/Regulator/Model'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service/index'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service/index'
import { GamblerUnverifiedIdentity } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'

export type TStateReducer = {
    blockedService: CreateEvent<BlockedService, BlockedService.Key, string>[]
    verifiedIdentity:
        | CreateEvent<VerifiedIdentity, VerifiedIdentity.Key, string>[]

    marketingManagerIdentity:
        | CreateEvent<
              MarketingManagerService,
              MarketingManagerService.Key,
              string
          >[]

    eventManagerIdentity:
        | CreateEvent<EventManagerService, EventManagerService.Key, string>[]
    gamblerUnverifiedIdentity: CreateEvent<
        GamblerUnverifiedIdentity,
        GamblerUnverifiedIdentity.Key,
        string
    >[]
    loading: boolean
}

export type TAction = {
    type: 'UPDATEDATA'
    payload: TStateReducer
}

export type TParamsGetPathToRedirect = Omit<TStateReducer, 'loading'>
