import React from 'react'

import AppContainer from 'Components/AppContainer'
import Header from 'Components/Header'
import SideMenu from 'Components/SideMenu'
import MainContainer from 'Components/MainContainer'
import Footer from 'Components/Footer'
import MyBets from 'Components/MyBets'
import Routes from 'routes'
import IdleTimerComponent from 'Containers/IdleTimer'

import { VerificationStatusProvider } from 'State/VerificationStatusProvider'

import useIsManagerTokenValid from 'Hooks/useIsManagerTokenValid'

export default function AppContent() {
    useIsManagerTokenValid()

    return (
        <>
            <VerificationStatusProvider>
                <Header />
                <AppContainer>
                    <SideMenu />
                    <MainContainer>
                        <Routes />
                        <Footer />
                    </MainContainer>
                    <MyBets />
                </AppContainer>
                <IdleTimerComponent />
            </VerificationStatusProvider>
        </>
    )
}
