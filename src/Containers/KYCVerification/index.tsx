import React from 'react'
import {
    GamblerIdentity,
    PendingIdentity
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { useLedger, useParty, useStreamQueries } from '@daml/react'
import TagManager from 'react-gtm-module'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'

import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'

import useMediaQuery from 'Hooks/useMediaQuery'

import KYCForm from 'Components/KYCVerification/Form'
import HeaderInfo from 'Components/KYCVerification/HeaderInfo'
import Info from 'Components/KYCVerification/Info'
import LoaderSpinner from 'Components/Loader'

import { initialVerificationRequest } from 'Utils/initialVerificationRequest'
import { useI18LanguageContext } from 'State/LanguageState'

import { generateLocaleForJumio } from 'Utils/generateLocaleForJumio'

import './style.scss'

export type TKYCForm = {
    verification: string
}

export default function KYCVerification() {
    //STATES
    const [generalLoader, setGeneralLoader] = React.useState(true)
    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader
    } = useGlobalGamblingConfigContext()

    //HOOKS
    const partyKYCFORM = useParty()
    const navigate = useNavigate()
    const ledgerKYCFORM = useLedger()
    const hideInfoContainer = useMediaQuery('(max-width: 1270px)')
    const { lang } = useI18LanguageContext()
    let locale = generateLocaleForJumio(lang)

    const { contracts: gamblerIdentity, loading: loadingGamblerIndentity } =
        useStreamQueries(GamblerIdentity, () => [{ customer: partyKYCFORM }], [
            partyKYCFORM
        ])
    const { contracts: pendingIdentity, loading: loadingPendingIdentity } =
        useStreamQueries(PendingIdentity, () => [{ customer: partyKYCFORM }], [
            partyKYCFORM
        ])

    const redirectToApp = () => {
        return navigate('/');
    }

    const requestJumioVerification = async () => {
        if (GlobalGamblingConfigurationContract?.length > 0) {
            try {
                const jumio =
                    GlobalGamblingConfigurationContract[0]?.payload?.integrationParties
                        .entriesArray()
                        .filter(a => a[0] === 'jumio')[0][1]
                await initialVerificationRequest(
                    ledgerKYCFORM,
                    partyKYCFORM,
                    jumio,
                    () => redirectToApp(),
                    locale
                )
                TagManager.dataLayer({
                    dataLayer: {
                        event: 'started_kyc'
                    }
                })
            } catch (error) {
                setGeneralLoader(false)
                toast.error('Something went wrong, please try again later')
                console.error('error on KYC', error)
            }
        } else {
            toast.error('Something went wrong, please try again later')
            setGeneralLoader(false)
        }
    }

    const handleVerification = async (values: TKYCForm) => {
        setGeneralLoader(true)
        requestJumioVerification()
    }

    const displayKYCForm = () => {
        if (gamblerIdentity.length > 0) {
            return (
                <>
                    <p>You already have your identity verified.</p>
                    <button
                        className="registerbtn__nextStep"
                        onClick={() => navigate('/')}
                    >
                        Go to app
                    </button>
                </>
            );
        }
        if (pendingIdentity.length > 0) {
            return (
                <>
                    <p>You already have a KYC in progress.</p>
                    <button
                        className="registerbtn__nextStep"
                        onClick={() =>
                            window.open(
                                pendingIdentity[0]?.payload?.redirectUrl
                            )
                        }
                    >
                        Go to KYC
                    </button>
                    <button
                        className="registerbtn__previousStep width100 marginTop20"
                        onClick={() => navigate('/')}
                    >
                        Go to app
                    </button>
                </>
            );
        }
        //CHANGE HERE KYC FORM
        return <KYCForm handleVerification={handleVerification} />
    }

    return (
        <>
            {generalLoader ||
            loadingGamblerIndentity ||
            GlobalGamblingConfigurationLoader ||
            loadingPendingIdentity ? (
                <div className="verify__loaderOverlay" />
            ) : null}
            <div className="content__container verify">
                {!generalLoader &&
                !loadingGamblerIndentity &&
                !loadingPendingIdentity ? (
                    <div className="verify__container">
                        <div className="verify__formWrapper">
                            <HeaderInfo />
                            {displayKYCForm()}
                        </div>
                        {!hideInfoContainer && <Info />}
                    </div>
                ) : (
                    <LoaderSpinner className="verify__loader" />
                )}
            </div>
        </>
    )
}
