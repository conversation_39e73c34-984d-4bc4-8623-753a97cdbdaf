@import '../../Styles/colors';

.verify {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGSignIn.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    font-family: 'Montserrat-Bold', sans-serif;
    gap: 100px;
    //min-height: 100vh;
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%;
    padding-bottom: 30px;
    padding-top: 30px;

    &__loaderOverlay {
        background-color: transparent;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
    }

    &__loader {
        display: flex;
        align-items: center;
        justify-content: center;
        background: $white;
        border-radius: 5px;
        padding: 30px;
        max-width: 370px;
    }

    &__icon {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        img {
            max-width: 100px;
            max-height: 100px;
        }
    }

    label {
        font-family: Open Sans, sans-serif;
        display: flex;
        margin-top: 40px;
        margin-bottom: 40px;
        align-items: center;
        text-align: justify;
    }

    &__input {
        margin-right: 10px;
        &[type='checkbox'] {
            accent-color: $purple;
            cursor: pointer;
            height: 15px;
            padding: 0;
            margin-top: 0;
            vertical-align: middle;
            &:focus {
                outline: 0;
                box-shadow: none;
            }
        }
        &[type='radio'] {
            accent-color: $purple;
            cursor: pointer;
            height: 15px;
            padding: 0;
            margin-top: 0;
            vertical-align: middle;
            &:focus {
                outline: 0;
                box-shadow: none;
            }
        }
    }

    &__info {
        grid-area: info;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__infoWrapper {
        text-align: center;
        h4 {
            font-family: Open Sans, sans-serif;
            font-weight: bold;
            font-size: 0.875rem;
            line-height: 1.5rem;
            margin-bottom: 20px;
        }

        p {
            font-family: Open Sans, sans-serif;
            //font-weight: lighter;
            font-size: 0.875rem;
            line-height: 1.5rem;
        }
        p:last-child {
            margin-top: 20px;
        }
    }

    &__formWrapper {
        grid-area: form;
    }

    &__container {
        background: $white;
        border-radius: 5px;
        padding: 40px;
        max-width: 1200px;
        display: grid;
        gap: 40px;
        grid-template-columns: repeat(3, 1fr);
        grid-template-areas: 'form form info';

        h2 {
            font-size: 2.188rem;
            line-height: 2.75rem;
            margin-bottom: 25px;
        }
    }

    &__subheader {
        p {
            font-family: Open Sans, sans-serif;
            font-size: 1.063rem;
            line-height: 1.5rem;
            margin-bottom: 25px;
        }
    }

    &__age {
        padding-top: 15px;
    }
}

@media (max-width: 1500px) {
    .verify {
        &__container {
            max-width: 1000px;
        }
    }
}

@media (max-width: 1270px) {
    .verify {
        &__container {
            max-width: 650px;
            gap: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        &__infoWrapper {
            display: none;
        }
    }
}

@media (max-width: 920px) {
    .verify {
        &__container {
            max-width: 550px;
        }
    }
}

@media (max-width: 820px) {
    .verify {
        &__container {
            max-width: 450px;
            h2 {
                font-size: 1.53rem;
                line-height: 2.75rem;
                margin-bottom: 20px;
            }
        }

        &__subheader {
            p {
                font-family: Open Sans, sans-serif;
                font-size: 1rem;
                line-height: 1.5rem;
                margin-bottom: 20px;
            }
        }

        label {
            font-size: 1rem;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        #my-radio-group {
            font-size: 1rem;
        }
    }
}

@media (max-width: 720px) {
    .verify {
        &__container {
            max-width: 400px;
            padding: 20px;
        }
    }
}

@media (max-width: 474px) {
    .verify {
        &__container {
            max-width: 350px;
            padding: 15px;

            h2 {
                font-size: 1.063rem;
                line-height: 2.75rem;
                margin-bottom: 15px;
            }
        }

        &__subheader {
            p {
                font-family: Open Sans, sans-serif;
                font-size: 0.813rem;
                line-height: 1.5rem;
                margin-bottom: 15px;
            }
        }

        label {
            font-size: 0.813rem;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        #my-radio-group {
            font-size: 0.813rem;
        }
    }
}

@media (max-width: 474px) {
    .verify {
        &__container {
            max-width: 290px;
        }
    }
}
