import DamlLedger, { createLedgerContext } from '@daml/react'
import React from 'react'
import { QueryClient, QueryClientProvider } from 'react-query'
import { ToastContainer } from 'react-toastify'
import { getPublicToken } from 'Utils/partiesUtils'

import { AccountContextProvider } from 'State/AccountContext'
import { AppProvider } from 'State/AppProvider'
import { I18LanguageProvider } from 'State/LanguageState'
import { OddTypeProvider } from 'State/OddTypeContext'
import { SelectedPromotionProvider } from 'State/SelectPromotion'
import { httpBaseUrl, wsBaseUrl } from '../config'
import { BetsProvider } from '../State/BetsContext'

import { useAuth0 } from '@auth0/auth0-react'
import LoaderSpinner from 'Components/Loader'
import AppOrMaintenance from 'Containers/AppOrMaintenace'
import { GamblingListingProvider } from 'State/GamblingListingContext'
import { GlobalGamblingConfigProvider } from 'State/GlobalGamblingConfigsContext'
import { MarketMapProvider } from 'State/MarketMapContext'
import { SportTranslationsProvider } from 'State/SportTranslationsContext'

import usePartyToken from 'Hooks/usePartyToken'
import 'react-toastify/dist/ReactToastify.css'

type UnifiedDamlProviderProps = {
    party: string
    token: string
    httpBaseUrl?: string
    wsBaseUrl?: string
}

export const UnifiedDamlProvider: React.FC<UnifiedDamlProviderProps> = ({
    children,
    party,
    token,
    httpBaseUrl,
    wsBaseUrl
}) => (
    <DamlLedger
        party={party}
        token={token}
        httpBaseUrl={httpBaseUrl}
        wsBaseUrl={wsBaseUrl}
    >
        {children}
    </DamlLedger>
)

//the createLedgerContext() returns us a ledgerContext with all methods we need
//like useQuery, useParty, useStreamQueries and so on
export const publicContext = createLedgerContext()

function App() {
    const { isLoading } = useAuth0()
    const { partyToDAMLProvider, tokenToDAMLProvider } = usePartyToken()
    const queryClient = new QueryClient()
    //two states for public party and public token
    //two states for public party and public token
    const [publicData, setPublicData] = React.useState<{
        party?: string
        token?: string
    }>({})
    //Get public data from the method exported on queryStreams and set variables
    //Get public data from the method exported on queryStreams and set variables
    React.useEffect(() => {
        if (window.AF && typeof window.AF === 'function') {
            window.AF('init', {
                appId: 'com.thesportsmarket.gambyl',
                devKey: 'mwDjQvom8VKjwZbJ2w9uqD'
            })
        }
        getPublicToken()
            .then(partyData => {
                setPublicData({
                    party: partyData?.party,
                    token: partyData?.token
                })
            })
            .catch(error => console.error('error fetching public party', error))
    }, [])

    // Create a PublicPartyLedger for passing the ledger as context props
    // For using this public ledger for queries we just need to wrap the app on this <PublicPartyLedger/>
    // and import the publicContext where we need it and use it like this:
    // import { publicContext } from 'Containers/App'
    // const { contracts, loading } = publicContext.useQuery(SomeTemplate)
    const PublicPartyLedger: React.FC = ({ children }) => {
        if (publicData.token && publicData.party) {
            return (
                <publicContext.DamlLedger
                    token={publicData.token}
                    party={publicData.party}
                    httpBaseUrl={httpBaseUrl}
                    wsBaseUrl={wsBaseUrl}
                >
                    {children}
                </publicContext.DamlLedger>
            )
        }
        return <LoaderSpinner />
    }

    if (isLoading) {
        return (
            <>
                <LoaderSpinner />
            </>
        )
    }

    return (
        <AppProvider>
            <ToastContainer
                position="top-center"
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss={false}
                draggable={false}
                pauseOnHover
            />
            <QueryClientProvider client={queryClient}>
                <div className="App">
                    <UnifiedDamlProvider
                        party={partyToDAMLProvider ? partyToDAMLProvider : ''}
                        token={tokenToDAMLProvider ? tokenToDAMLProvider : ''}
                        httpBaseUrl={httpBaseUrl}
                        wsBaseUrl={wsBaseUrl}
                    >
                        <PublicPartyLedger>
                            <GlobalGamblingConfigProvider>
                                <MarketMapProvider>
                                    <SportTranslationsProvider>
                                        <GamblingListingProvider>
                                            <SelectedPromotionProvider>
                                                <I18LanguageProvider>
                                                    <AccountContextProvider>
                                                        <OddTypeProvider>
                                                            <BetsProvider>
                                                                <AppOrMaintenance />
                                                            </BetsProvider>
                                                        </OddTypeProvider>
                                                    </AccountContextProvider>
                                                </I18LanguageProvider>
                                            </SelectedPromotionProvider>
                                        </GamblingListingProvider>
                                    </SportTranslationsProvider>
                                </MarketMapProvider>
                            </GlobalGamblingConfigProvider>
                        </PublicPartyLedger>
                    </UnifiedDamlProvider>
                </div>
            </QueryClientProvider>
        </AppProvider>
    )
}

export default App
