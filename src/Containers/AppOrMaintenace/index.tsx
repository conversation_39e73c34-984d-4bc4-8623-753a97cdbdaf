import React from 'react'
import { useLocation } from 'react-router-dom'
import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'

import AppContent from 'Containers/AppContent'
import MaintenancePage from 'Containers/MaintenancePage'
import GambylAdmin from 'Containers/GambylAdmin'
import SignInRedirect from 'Containers/SignInRedirect'

export default function AppOrMaintenance() {
    const { pathname } = useLocation()
    const [isOnMaintenance, setIsOnMaintenance] = React.useState(false)
    const { GlobalGamblingConfigurationContract } =
        useGlobalGamblingConfigContext()

    React.useEffect(() => {
        if (GlobalGamblingConfigurationContract.length) {
            setIsOnMaintenance(
                GlobalGamblingConfigurationContract[0]?.payload.isOnMaintenance
            )
        }
        return () => {}
    }, [GlobalGamblingConfigurationContract])

    if (pathname.includes('gambyladmin')) {
        return <GambylAdmin />
    }

    if (isOnMaintenance) {
        return <MaintenancePage />
    }

    if (pathname.includes('signinredirect')) {
        return <SignInRedirect />
    }

    return <AppContent />
}
