export default function getDescriptions(contracts: any[]) {
    return contracts
        .filter(
            (contract: { payload: { cfi: { code: string } } }) =>
                contract?.payload?.cfi?.code === 'MMMXXX'
        )
        .map(
            (contract: { payload: { description: string } }) =>
                Object.values(
                    JSON.parse(
                        JSON.parse(
                            JSON.stringify(contract?.payload?.description)
                        )
                    )
                )[0]
        )
}
