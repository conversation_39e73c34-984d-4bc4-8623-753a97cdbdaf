export interface EventParticipant {
    eventFK: string
    id: string
    n: string
    number: string
    participant: any
    participantFK: string
    result: any
    ut: string
}

export interface SportEvent {
    elapsed: any
    event_participants: { [key: string]: EventParticipant }
    gender: string
    id: string
    n: string
    name: string
    property: any
    round_typeFK: string
    sportFK: string
    sport_name: string
    startdate: string
    status_descFK: string
    status_type: string
    tournamentFK: string
    tournament_name: string
    tournament_stageFK: string
    tournament_stage_name: string
    tournament_templateFK: string
    tournament_template_name: string
    ut: string
}
