import React from 'react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { useI18LanguageContext } from 'State/LanguageState'

import Event from 'Components/Event'
import LoaderSpinner from 'Components/Loader'
import ScrollToTopBTN from '../../Components/Dashboard/ScrollToTopBtn'

import { publicContext } from 'Containers/App'
import useInfiniteScroll from 'Hooks/useInfiniteScroll'
import EventFilterTabs from 'Components/EventFilterTabs'
import useFeaturedFilteredEvents from 'Hooks/useFeaturedFilteredEvents'
import useDebounce from 'Hooks/useDebounce'
import useFilterEventByEventTitle from 'Hooks/useFilterEventByEventTitle'
import ScrolltoBottomBtn from 'Components/Dashboard/ScrollToBottomBtn'

export default function HomepageEvents() {
    const [isFeatured, setIsFeatured] = React.useState(false)
    const [debouncedValue, search, setSearch] = useDebounce<string>('', 500)

    const { lang } = useI18LanguageContext()
    const { visibleAmount, setVisibleAmount, setElement } = useInfiniteScroll(5)

    const { contracts: eventContracts, loading: eventLoading } =
        publicContext.useStreamQueries(
            EventInstrument,
            () => {
                return [
                    {
                        status: 'Active',
                        details: {
                            eventStatus: 'NotStarted'
                        }
                    } as EventInstrument
                ]
            },
            []
        )

    const filteredByFeatureEvents = useFeaturedFilteredEvents(
        eventContracts,
        isFeatured
    )

    const filterEventsByDebounceValue = useFilterEventByEventTitle(
        filteredByFeatureEvents,
        debouncedValue,
        lang
    )

    const eventsToRender = React.useMemo(() => {
        return debouncedValue
            ? filterEventsByDebounceValue
            : filteredByFeatureEvents
    }, [debouncedValue, filterEventsByDebounceValue, filteredByFeatureEvents])

    const scrollToBottom = React.useCallback(() => {
        setVisibleAmount(eventContracts.length + 1)
        setTimeout(
            () =>
                document
                    ?.querySelector('footer')
                    ?.scrollIntoView({ behavior: 'smooth' }),
            500
        )
    }, [eventContracts.length, setVisibleAmount])

    const isEventRenderReady = !eventLoading

    if (!isEventRenderReady) {
        return <LoaderSpinner />
    }

    return (
        <div className="dashboard__card">
            {/* ONLY FOR MOBILE SCREENS */}
            <ScrollToTopBTN />
            <EventFilterTabs
                isFeatured={isFeatured}
                handleFeatured={setIsFeatured}
                search={search}
                searchCallback={setSearch}
            >
                <ScrolltoBottomBtn scrollToBottom={scrollToBottom} />
            </EventFilterTabs>
            {React.Children.toArray(
                eventsToRender
                    .slice(0, visibleAmount)
                    .map(description => <Event event={description} />)
            )}
            {/* REFERENCE COMPONENT FOR INFITE SCROLL */}
            {eventsToRender.length ? (
                <span ref={setElement} style={{ background: 'transparent' }} />
            ) : null}
            {!eventsToRender.length ? (
                <span>There are no available events at the moment.</span>
            ) : null}
        </div>
    )
}
