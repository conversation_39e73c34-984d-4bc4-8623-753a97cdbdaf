.dashboard {
    &__wrapper {
        width: 100%;
        transition: all 0.3s;
        padding-right: 1rem;
        padding-left: 1rem;
    }
    &__topContainer {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        justify-items: self-start;
        gap: 20px;
    }

    &__add {
        max-width: 350px;
        max-height: 250px;

        img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
    }

    &__card {
        min-height: 400px;
    }
}

@media (max-width: 893px) {
    .dashboard {
        &__wrapper {
            padding-right: 0;
            padding-left: 0;
        }
        &__topContainer {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            justify-items: center;
        }
    }
}
