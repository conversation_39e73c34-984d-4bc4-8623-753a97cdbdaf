import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import useScrollToTop from 'Hooks/useScrollToTop'

import './style.scss'

export default function AboutUs() {
    useScrollToTop()
    const { t } = useTranslation()

    return (
        <div className="aboutUs pagePadding">
            <h1>{t('AbTitle')}</h1>
            <h2 className="aboutUs__purple">Gambyl.</h2>
            <h2>
                <i>{t('AbImark')}</i>
            </h2>
            <section>
                <h2>{t('AbSports')}</h2>
                <p>{t('AbParagraph1')}</p>
                <p>{t('AbParagraph2')}</p>
            </section>
            <section>
                <h2>{t('AbTeam')}</h2>
                <h3>Josh</h3>
                <p>{t('AbJosh')}</p>
                <h3>Marty</h3>
                <p>{t('AbMarty')} </p>
            </section>
            <section>
                <h2>{t('AbMissionTitle')}</h2>
                <p>{t('AbMission')}</p>
            </section>
            <section>
                <h2>{t('AbValuesTitle')}</h2>
                <p>{t('AbValues')}</p>
            </section>
            <section>
                <h2>{t('AbFamilyTitle')}</h2>
                <p>{t('AbFamily1')}</p>
                <p>{t('AbFamily2')} </p>
                <p>{t('AbFamily3')}</p>
                <p>Gambyl</p>
                <Link to="/" className="btn btn__primary">
                    {t('getIntoTheActionNow')}
                </Link>
            </section>
        </div>
    )
}
