import React from 'react'
import { useTranslation } from 'react-i18next'
import { useParty, useStreamQueries } from '@daml/react'
import { PendingTransaction } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'

import SelectFilter from 'Components/Select'
import InProgressPaymentsData from 'Containers/Account/Payments/InProgress/Data'
import LoaderSpinner from 'Components/Loader'

export default function PaymentsInProgress() {
    const { t } = useTranslation()
    const party = useParty()
    const { contracts, loading } = useStreamQueries(
        PendingTransaction,
        () => [
            {
                customer: party
            }
        ],
        []
    )

    const [statusFilter, setStatusFilter] = React.useState('')
    const filterOptions = [
        { label: t('PaymentsDropdown1'), value: '' },
        {
            label: t('PaymentsDropdown2'),
            value: 'withdraws'
        }
    ]

    if (loading) {
        return (
            <div>
                <LoaderSpinner />
            </div>
        )
    }

    return (
        <div>
            <SelectFilter
                selectedOption={statusFilter}
                options={filterOptions}
                setSelectedValue={setStatusFilter}
            />
            {statusFilter === '' ? (
                <InProgressPaymentsData
                    pendingTransactionContracts={contracts}
                    isDeposit={true}
                />
            ) : (
                <InProgressPaymentsData
                    pendingTransactionContracts={contracts}
                    isDeposit={false}
                />
            )}
        </div>
    )
}
