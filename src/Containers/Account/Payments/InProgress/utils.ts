import { CreateEvent } from '@daml/ledger'
import { PendingTransaction } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'

type PendingContract = readonly CreateEvent<
    PendingTransaction,
    PendingTransaction.Key,
    string
>[]

/**
 * @param requestedAmount number
 * @param appliedFee number
 * @returns a value of type number matching the amount payed of deposit fee
 */
const calculateDepositFee = (requestedAmount: number, appliedFee: number) =>
    (requestedAmount * (100 * appliedFee)) / (100 + 100 * appliedFee)

/**
 * @param requestedAmount number
 * @param appliedFee number
 * @returns a value of type number matching the amount withdrawed without fee
 */
const calculateWithdraw = (requestedAmount: number, appliedFee: number) =>
    requestedAmount - requestedAmount * appliedFee

export class generateDataToDisplay {
    pendingTransactionContract: PendingContract

    /**
     *
     * @param PendingTransactionContract array of contracts of type PendingTransaction from Gambyl/Gambling/Account/Model
     */
    constructor(PendingTransactionContract: PendingContract) {
        this.pendingTransactionContract = PendingTransactionContract
    }

    /**
     * @returns data to display pending deposits info
     */
    getDataToDisplayDeposit() {
        return this.pendingTransactionContract.map(({ payload }) => {
            const fee = calculateDepositFee(
                Number(payload?.requestedAmount),
                Number(payload?.appliedFee)
            )
            return {
                date: payload?.startedAt?.substring(0, 10),
                transactionId: payload?.transactionId,
                fee: fee,
                amount: Number(payload?.requestedAmount) - fee
            }
        })
    }

    /**
     * @returns data to display pending withdtaws info
     */
    getDataToDisplayWithdraw() {
        return this.pendingTransactionContract.map(({ payload }) => {
            const withdrawReceived = calculateWithdraw(
                Number(payload?.requestedAmount),
                Number(payload?.appliedFee)
            )
            return {
                date: payload?.startedAt?.substring(0, 10),
                transactionId: payload?.transactionId,
                fee: Number(payload?.requestedAmount) - withdrawReceived,
                amount: Number(payload?.requestedAmount)
            }
        })
    }
}
