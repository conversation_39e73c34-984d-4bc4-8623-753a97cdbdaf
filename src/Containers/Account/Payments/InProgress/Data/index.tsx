import React from 'react'
import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import { PendingTransaction } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import numeral from 'numeral'
import { useTranslation } from 'react-i18next'

import { generateDataToDisplay } from 'Containers/Account/Payments/InProgress/utils'
import GenericTable from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import { CreateEvent } from '@daml/ledger'

interface IPaymentHistoryInProgressTable {
    paymentsDate: string
    paymentsTransactionCode: string
    paymentsAmount: string
    paymentsFee: string
}

interface Props {
    isDeposit: boolean
    pendingTransactionContracts: readonly CreateEvent<
        PendingTransaction,
        PendingTransaction.Key,
        string
    >[]
}

const InProgressPaymentsData = ({
    isDeposit,
    pendingTransactionContracts
}: Props) => {
    const { t } = useTranslation()

    const dataToIterate = pendingTransactionContracts.filter(data =>
        isDeposit
            ? data.payload.transactionType === 'Deposit'
            : data.payload.transactionType === 'Withdrawal'
    )

    const titlePaymentsDate = t('paymentsDate')
    const titlePaymentsTransactionCode = t('paymentsTransactionCode')
    const titlePaymentsAmount = t('paymentsAmount')
    const titlePaymentsFee = t('paymentsFee')

    const dataToDisplay = React.useMemo(() => {
        if (dataToIterate.length > 0) {
            let data = new generateDataToDisplay(dataToIterate)
            return isDeposit
                ? data.getDataToDisplayDeposit()
                : data.getDataToDisplayWithdraw()
        }
        return []
    }, [dataToIterate, isDeposit])

    const columnHelper = createColumnHelper<IPaymentHistoryInProgressTable>()

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('paymentsDate', {
                header: titlePaymentsDate,
                cell: info => <>{info.getValue().slice(0, 10)}</>
            }),
            columnHelper.accessor('paymentsTransactionCode', {
                header: titlePaymentsTransactionCode,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('paymentsAmount', {
                header: titlePaymentsAmount,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('paymentsFee', {
                header: titlePaymentsFee,
                cell: info => info.getValue()
            })
        ],
        [
            columnHelper,
            titlePaymentsAmount,
            titlePaymentsDate,
            titlePaymentsFee,
            titlePaymentsTransactionCode
        ]
    )

    const data = React.useMemo(
        () =>
            dataToDisplay.map(data => ({
                paymentsDate: data.date,
                paymentsTransactionCode: data?.transactionId,
                paymentsAmount: numeral(data?.amount).format('$0,0.00'),
                paymentsFee: `${isDeposit ? '+' : '-'} ${numeral(
                    data?.fee
                ).format('$0,0.00')}`
            })),
        [dataToDisplay, isDeposit]
    )
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: true
    })

    return (
        <div>
            <div className="payment__theader--mt40">
                <h4>{t('Activities')}</h4>
            </div>
            {data.length > 0 ? (
                <>
                    <GenericTable
                        tableHeader={table
                            .getHeaderGroups()
                            .map(headerGroup => (
                                <tr key={headerGroup.id}>
                                    {headerGroup.headers.map(header => (
                                        <th key={header.id}>
                                            {header.column.columnDef.header}
                                        </th>
                                    ))}
                                </tr>
                            ))}
                        tableBodyRow={table.getRowModel().rows.map(row => (
                            <tr key={row.id}>
                                {row.getVisibleCells().map(cell => {
                                    if (
                                        cell.column.id === 'paymentsFee' ||
                                        cell.column.id === 'paymentsAmount'
                                    ) {
                                        return (
                                            <td
                                                key={cell.id}
                                                className={
                                                    isDeposit
                                                        ? 'success'
                                                        : 'fail'
                                                }
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </td>
                                        )
                                    }
                                    return (
                                        <td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </td>
                                    )
                                })}
                            </tr>
                        ))}
                    />
                    <Pagination table={table} />
                </>
            ) : (
                <div>
                    <p>{t('NoDataToPresent')}</p>
                </div>
            )}
        </div>
    )
}

export default InProgressPaymentsData
