import React from 'react'
import {
    createColumn<PERSON><PERSON>per,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import numeral from 'numeral'
import { useTranslation } from 'react-i18next'
import { useQuery, useParty } from '@daml/react'
import { TransactionHistory } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import useScrollToTop from 'Hooks/useScrollToTop'

import LoaderSpinner from 'Components/Loader'
import Table from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'

import '../style.scss'

interface IPaymentHistoryTable {
    paymentsDate: string
    paymentsType: string
    paymentsTransactionCode: string
    paymentsAmount: string
    paymentsFee: string
    isDeposit: boolean
}

const checkIsDeposit = (payload: TransactionHistory) =>
    payload?.transactionId.replace('transactionId', '') === 'credit' ||
    payload?.transactionType === 'Deposit'

export default function PaymentHistory() {
    const { t } = useTranslation()
    useScrollToTop()
    const party = useParty()

    const {
        loading: transactionHistoryLoader,
        contracts: transactionHistoryContracts
    } = useQuery(
        TransactionHistory,
        () => {
            return { customer: party }
        },
        [party]
    )

    const titlePaymentsDate = t('paymentsDate')
    const titlePaymentsType = t('paymentsType')
    const titlePaymentsTransactionCode = t('paymentsTransactionCode')
    const titlePaymentsAmount = t('paymentsAmount')
    const titlePaymentsFee = t('paymentsFee')
    const paymentTypeDeposit = t('paymentsTypeDeposit')
    const paymentTypeWithdraw = t('paymentsTypeWithdraw')

    const columnHelper = createColumnHelper<IPaymentHistoryTable>()

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('paymentsDate', {
                header: titlePaymentsDate,
                cell: info => <>{info.getValue().slice(0, 10)}</>
            }),
            columnHelper.accessor('paymentsType', {
                header: titlePaymentsType,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('paymentsTransactionCode', {
                header: titlePaymentsTransactionCode,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('paymentsAmount', {
                header: titlePaymentsAmount,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('paymentsFee', {
                header: titlePaymentsFee,
                cell: info => info.getValue()
            })
        ],
        [
            columnHelper,
            titlePaymentsAmount,
            titlePaymentsDate,
            titlePaymentsFee,
            titlePaymentsTransactionCode,
            titlePaymentsType
        ]
    )

    const data = React.useMemo(
        () =>
            [...transactionHistoryContracts]
                .sort((a, b) => {
                    const date1 = new Date(b?.payload?.terminatedAt)
                    const date2 = new Date(a?.payload?.terminatedAt)
                    return date1.getTime() - date2.getTime()
                })
                .filter(
                    a =>
                        a.payload.transactionType === 'Deposit' ||
                        a.payload.transactionType === 'Withdrawal'
                )
                .map(contract => {
                    const isDeposit = checkIsDeposit(contract.payload)
                    return {
                        paymentsDate: contract.payload.terminatedAt,
                        paymentsType: isDeposit
                            ? paymentTypeDeposit
                            : paymentTypeWithdraw,
                        paymentsTransactionCode:
                            contract.payload?.transactionId,
                        paymentsAmount: numeral(
                            contract.payload?.confirmedAmount
                        ).format('$0,0.00'),
                        paymentsFee: `${isDeposit ? '+' : '-'} ${numeral(
                            Number(contract.payload?.appliedFee) *
                                Number(contract.payload?.confirmedAmount)
                        ).format('$0,0.00')}`,
                        isDeposit
                    }
                }),
        [transactionHistoryContracts, paymentTypeDeposit, paymentTypeWithdraw]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: true
    })

    if (transactionHistoryLoader) {
        return <LoaderSpinner className="payment__loader" />
    }

    return (
        <div>
            <div className="payment__theader">
                <h4>{t('Activities')}</h4>
            </div>
            <>
                {transactionHistoryLoader ? (
                    <LoaderSpinner className="payment__loader" />
                ) : null}
                {transactionHistoryContracts.length > 0 ? (
                    <>
                        <Table
                            tableHeader={table
                                .getHeaderGroups()
                                .map(headerGroup => (
                                    <tr key={headerGroup.id}>
                                        {headerGroup.headers.map(header => (
                                            <th key={header.id}>
                                                {header.column.columnDef.header}
                                            </th>
                                        ))}
                                    </tr>
                                ))}
                            tableBodyRow={table.getRowModel().rows.map(row => (
                                <tr key={row.id}>
                                    {row.getVisibleCells().map(cell => {
                                        if (
                                            cell.column.id === 'paymentsType' ||
                                            cell.column.id === 'paymentsAmount'
                                        ) {
                                            return (
                                                <td
                                                    key={cell.id}
                                                    className={
                                                        cell.row.original
                                                            .isDeposit === true
                                                            ? 'success'
                                                            : 'fail'
                                                    }
                                                >
                                                    {flexRender(
                                                        cell.column.columnDef
                                                            .cell,
                                                        cell.getContext()
                                                    )}
                                                </td>
                                            )
                                        }
                                        return (
                                            <td key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </td>
                                        )
                                    })}
                                </tr>
                            ))}
                        />
                        <Pagination table={table} />
                    </>
                ) : (
                    <div>
                        <p>{t('NoDataToPresent')}</p>
                    </div>
                )}
            </>
        </div>
    )
}
