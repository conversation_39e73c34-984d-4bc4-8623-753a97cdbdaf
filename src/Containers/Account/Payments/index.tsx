import React from 'react'

import PaymentCompleted from 'Containers/Account/Payments/Completed'
import PaymentsInProgress from 'Containers/Account/Payments/InProgress'

import TabsWithoutLink from 'Components/TabsWithoutLink'
import { useTranslation } from 'react-i18next'

export default function Payments() {
    const { t } = useTranslation()
    const [selectedTab, setSelectedTab] = React.useState('inProgress')

    const tabs = [
        {
            label: t('PaymentTab1'),
            value: 'inProgress',
            onclick: () => setSelectedTab('inProgress')
        },
        {
            label: t('PaymentTab2'),
            value: 'completed',
            onclick: () => setSelectedTab('completed')
        }
    ]

    return (
        <>
            <TabsWithoutLink tabs={tabs} selectedTab={selectedTab} />
            {selectedTab === 'inProgress' ? (
                <PaymentsInProgress />
            ) : (
                <PaymentCompleted />
            )}
        </>
    )
}
