import {
    GamblerIdentity,
    GamblerUnverifiedIdentity
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import Ledger, { CreateEvent } from '@daml/ledger'
import { ContractId } from '@daml/types'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import * as Yup from 'yup'

export interface IEditProfileForm {
    city: string
    subDivision: string
    addressLine1: string
    postalCode: string
}

const strRe =
    /^(?:[A-Za-z]{1,}(?:(\.\s|'s\s|\s?-\s?|\s)?(?=[A-Za-z]+))){1,2}(?:[A-Za-z]+)?$/

export const editProfileFormSchema = Yup.object({
    city: Yup.string()
        .min(1)
        .max(30)
        .matches(strRe, 'Please add a valid city name')
        .required('Required field'),
    subDivision: Yup.string()
        .matches(strRe, 'Please add a valid state or sub division name')
        .min(1)
        .max(30)
        .required('Required field'),
    addressLine1: Yup.string()
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .min(1)
        .max(30)
        .required('Required field'),
    addressLine2: Yup.string()
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .min(1)
        .max(30),
    postalCode: Yup.string()
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .min(1)
        .max(30)
        .required('Required field')
})

export const generateInitDataForEditProfileForm = (
    gamblerIdentityContracts: readonly CreateEvent<
        GamblerIdentity,
        GamblerIdentity.Key,
        string
    >[],
    gamblerUnverifiedIdentityContracts: readonly CreateEvent<
        GamblerUnverifiedIdentity,
        GamblerUnverifiedIdentity.Key,
        string
    >[]
) => {
    if (gamblerIdentityContracts.length > 0) {
        return {
            city: gamblerIdentityContracts[0]?.payload.jumioUserData.city.trim(),
            subDivision:
                gamblerIdentityContracts[0]?.payload.jumioUserData.subDivision,
            addressLine1:
                gamblerIdentityContracts[0]?.payload.jumioUserData.addressLine1,
            postalCode:
                gamblerIdentityContracts[0]?.payload.jumioUserData.postalCode
        }
    }
    if (gamblerUnverifiedIdentityContracts.length > 0) {
        return {
            city: gamblerUnverifiedIdentityContracts[0]?.payload.city,
            subDivision:
                gamblerUnverifiedIdentityContracts[0]?.payload.subDivision,
            addressLine1:
                gamblerUnverifiedIdentityContracts[0]?.payload.addressLine1,
            postalCode:
                gamblerUnverifiedIdentityContracts[0]?.payload.postalCode
        }
    }
    return {
        city: '-',
        subDivision: '-',
        addressLine1: '-',
        postalCode: '-'
    }
}

export const submitEditFormCall = ({
    ledger,
    gamblerIdentityContracts,
    gamblerUnverifiedIdentityContracts,
    values,
    serviceContractId
}: {
    ledger: Ledger
    gamblerIdentityContracts: readonly CreateEvent<
        GamblerIdentity,
        GamblerIdentity.Key,
        string
    >[]
    gamblerUnverifiedIdentityContracts: readonly CreateEvent<
        GamblerUnverifiedIdentity,
        GamblerUnverifiedIdentity.Key,
        string
    >[]
    serviceContractId: ContractId<Service>
    values: IEditProfileForm
}) => {
    if (gamblerIdentityContracts.length > 0) {
        return ledger.exercise(Service.UpdateIdentity, serviceContractId, {
            ...values,
            addressLine2:
                gamblerIdentityContracts[0].payload.jumioUserData.addressLine2
        })
    }
    return ledger.exercise(
        Service.RequestUnverifiedIdentity,
        serviceContractId,
        {
            ...gamblerUnverifiedIdentityContracts[0].payload,
            ...values
        }
    )
}
