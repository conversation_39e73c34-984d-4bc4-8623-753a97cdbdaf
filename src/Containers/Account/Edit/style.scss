@import '../../../Styles/colors';

.edit {
    padding: 30px 0;
    .register__input {
        margin-top: 5px;
    }
    hr {
        margin-bottom: 20px;
    }
    label {
        margin-top: 5px;
        display: inline-block;
        font-size: 0.8rem;
        font-weight: bold;
    }
    &__link {
        display: flex;
        align-items: baseline;

        svg {
            height: 0.75rem;
            width: 0.75rem;
        }
        a {
            text-decoration: none;
            color: $darkGrey;
            font-size: 0.75rem;
            line-height: 1.563rem;
            display: flex;
            align-items: center;
        }
    }

    &__title {
        font-style: normal;
        font-weight: bold;
        font-size: 1.125rem;
        line-height: 1.563rem;
        margin: 15px 0;
    }

    &__form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
        gap: 20px;

        @media (max-width: 1350px) {
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        }

        @media (max-width: 840px) {
            grid-template-columns: 1fr;
        }
    }

    &__buttons {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        gap: 20px;

        button {
            width: 150px;
        }
    }
}
