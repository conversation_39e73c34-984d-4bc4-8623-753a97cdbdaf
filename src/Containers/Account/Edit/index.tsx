import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Formik } from 'formik'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { useParty, useLedger, useQuery } from '@daml/react'

import InputForm from 'Components/Inputs'
import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { toast } from 'react-toastify'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'
import { GamblerUnverifiedIdentity } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import {
    editProfileFormSchema,
    generateInitDataForEditProfileForm,
    IEditProfileForm,
    submitEditFormCall
} from './utils'
import useScrollToTop from 'Hooks/useScrollToTop'

import './style.scss'

export default function EditProfile() {
    useScrollToTop()
    const party = useParty()
    const ledger = useLedger()
    const navigate = useNavigate()
    const { t } = useTranslation()
    const { contracts: serviceContracts, loading: serviceLoading } = useQuery(
        Service,
        () => {
            return { customer: party }
        },
        []
    )
    const {
        loading: gamblerUnverifiedIdentityLoading,
        contracts: gamblerUnverifiedIdentityContracts
    } = useQuery(
        GamblerUnverifiedIdentity,
        () => {
            return { customer: party }
        },
        []
    )

    const { loading, gamblerIdentityContracts } = useVerificationStatusContext()
    const isLoading =
        serviceLoading || loading || gamblerUnverifiedIdentityLoading

    const initialData = generateInitDataForEditProfileForm(
        gamblerIdentityContracts,
        gamblerUnverifiedIdentityContracts
    )

    const submitForm = async (values: IEditProfileForm) => {
        try {
            const serviceContractId = serviceContracts[0].contractId
            await submitEditFormCall({
                ledger,
                gamblerIdentityContracts,
                gamblerUnverifiedIdentityContracts,
                serviceContractId,
                values
            })
            toast.success('Your Mailing address is successfully updated!')
            navigate('/account')
        } catch (e) {
            toast.error('Sorry, something went wrong. Please, try again later.')
        }
    }

    return (
        <div className="content__container accountProfile pagePadding">
            <div className="accountProfile__card">
                <h1>{t('editProfile')}</h1>
                <div className="accountProfile__routes">
                    <div className="edit">
                        <h3 className="edit__title">{t('InfoMailTitle')}</h3>
                        <hr />
                        {!isLoading && (
                            <div>
                                <Formik
                                    initialValues={initialData}
                                    onSubmit={values => {
                                        submitForm(values)
                                    }}
                                    validationSchema={editProfileFormSchema}
                                >
                                    {formik => (
                                        <form
                                            className="edit__form"
                                            onSubmit={formik.handleSubmit}
                                        >
                                            <div>
                                                <label htmlFor="city">
                                                    {t('UnverifiedGamblerCity')}
                                                </label>
                                                <InputForm
                                                    id="city"
                                                    placeholder="Please add your city"
                                                    type="text"
                                                    {...formik.getFieldProps(
                                                        'city'
                                                    )}
                                                />
                                                {formik.touched.city &&
                                                formik.errors.city ? (
                                                    <ErrorMessage
                                                        message={
                                                            formik.errors.city
                                                        }
                                                    />
                                                ) : null}
                                            </div>
                                            <div>
                                                <label htmlFor="subDivision">
                                                    {t(
                                                        'UnverifiedGamblerIdentityState'
                                                    )}
                                                </label>
                                                <InputForm
                                                    id="subDivision"
                                                    placeholder="Please add your State or Sub Division"
                                                    type="text"
                                                    {...formik.getFieldProps(
                                                        'subDivision'
                                                    )}
                                                />
                                                {formik.touched.subDivision &&
                                                formik.errors.subDivision ? (
                                                    <ErrorMessage
                                                        message={
                                                            formik.errors
                                                                .subDivision
                                                        }
                                                    />
                                                ) : null}
                                            </div>
                                            <div>
                                                <label htmlFor="addressLine1">
                                                    {t(
                                                        'UnverifiedGamblerIdentityAddress'
                                                    )}
                                                </label>
                                                <InputForm
                                                    id="addressLine1"
                                                    placeholder="Please add your address line 1"
                                                    type="text"
                                                    {...formik.getFieldProps(
                                                        'addressLine1'
                                                    )}
                                                />

                                                {formik.touched.addressLine1 &&
                                                formik.errors.addressLine1 ? (
                                                    <ErrorMessage
                                                        message={
                                                            formik.errors
                                                                .addressLine1
                                                        }
                                                    />
                                                ) : null}
                                            </div>
                                            <div>
                                                <label htmlFor="postalCode">
                                                    {t(
                                                        'UnverifiedGamblerIdentityPostalCode'
                                                    )}
                                                </label>
                                                <InputForm
                                                    id="postalCode"
                                                    placeholder="Please add your postal-code"
                                                    type="text"
                                                    {...formik.getFieldProps(
                                                        'postalCode'
                                                    )}
                                                />
                                                {formik.touched.postalCode &&
                                                formik.errors.postalCode ? (
                                                    <ErrorMessage
                                                        message={
                                                            formik.errors
                                                                .postalCode
                                                        }
                                                    />
                                                ) : null}
                                            </div>
                                            <div className="edit__buttons">
                                                <button
                                                    onClick={() => {
                                                        formik.resetForm()
                                                        navigate('/account')
                                                    }}
                                                    type="button"
                                                    className="registerbtn__previousStep"
                                                >
                                                    {t('cancelTranslation')}
                                                </button>
                                                <button
                                                    className="registerbtn__nextStep"
                                                    type="submit"
                                                    disabled={!formik.isValid}
                                                >
                                                    {t('saveTranslation')}
                                                </button>
                                            </div>
                                        </form>
                                    )}
                                </Formik>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
