import React from 'react'
import CopyClipboard from 'Components/Clipboard'

export default function CardInfo({
    title,
    content,
    hasClipboard = false,
    clipboardContent
}: {
    title: string
    content: string
    hasClipboard?: boolean
    clipboardContent?: string
}) {
    return (
        <span className="info__card">
            <h4>{title}</h4>
            <p>{content}
                {hasClipboard && clipboardContent ?
                    <CopyClipboard contenToCopy={clipboardContent} />
                    : null
                }</p>
        </span>
    )
}
