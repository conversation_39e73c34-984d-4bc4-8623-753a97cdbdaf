@import '../../../Styles/colors';

.info {
    color: $darkGrey;
    hr {
        border-top: 1px solid rgba(130, 152, 171, 0.5);
        border-bottom: 0;
        border-left: 0;
        border-right: 0;
    }

    &__link {
        margin-left: 10px;
        svg {
            height: 0.75rem;
            width: 0.75rem;
        }
        a {
            text-decoration: none;
            color: $darkGrey;
            font-size: 0.75rem;
            line-height: 1.563rem;
        }

        &:hover {
            a {
                color: $purple;
            }
        }
    }

    &__title {
        font-family: 'Montserrat', sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 1.125rem;
        line-height: 1.563rem;
        margin-bottom: 12px;
    }

    &__container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 10px;

        @media (max-width: 700px) {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            max-width: 200px;
            gap: 10px;
        }
    }

    &__card {
        padding: 24px 0;

        h4 {
            font-family: 'Montserrat', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 0.813rem;
            line-height: 1.25rem;
            letter-spacing: 0.063rem;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        p {
            font-family: OpenSans, sans-serif;
            font-size: 0.938rem;
            line-height: 1.563rem;
        }

        @media (max-width: 700px) {
            padding: 10px 0;

            h4 {
                margin-bottom: 5px;
            }
        }
    }

    &__banner {
        margin-top: 24px;
        border-radius: 6px;
        padding: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        a {
            text-decoration: none;
            text-align: center;
        }

        div {
            flex: 1 1 100%;
            display: flex;
            align-items: center;
            gap: 5px;

            p {
                font-family: OpenSans, sans-serif;
                font-size: 1rem;
                line-height: 1.875rem;
            }
        }

        &--failed {
            background: #fcebeb;
        }
        &--notStarted {
            background: #fcebeb;
        }
        &--pending {
            background: #edd1f9;
        }
        &--success {
            background: #d8f4dd;
        }
    }

    &__button {
        flex: 1 0 175px;

        border: none;
        border-radius: 6px;
        color: #ffff;
        font-family: 'Montserrat', sans-serif;
        font-size: 0.813rem;
        font-style: normal;
        font-weight: bold;
        line-height: 1.563rem;
        padding: 5px 25px;
        width: fit-content;
        max-height: 35px;
        cursor: pointer;
        transition: filter 0.2s;

        &:hover {
            filter: brightness(115%);
        }

        &--pending {
            background: $purple;
        }

        &--failed {
            background: #f61111;
        }
        &--notStarted {
            background: #f61111;
        }
    }
}

@media (max-width: 890px) {
    .info {
        &__banner {
            div {
                svg {
                    display: none;
                }
            }
        }
    }
}

@media (max-width: 453px) {
    .info {
        &__button {
            flex: 1 1 100px;
            width: 100px;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
