import React from 'react'
import { useParty, useLedger } from '@daml/react'

import useMediaQuery from 'Hooks/useMediaQuery'
import EnergyIcon from 'Assets/energyIcon'
import { initialVerificationRequest } from 'Utils/initialVerificationRequest'
import TagManager from 'react-gtm-module'
import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'
import { toast } from 'react-toastify'
import { useTranslation } from 'react-i18next'
import { useI18LanguageContext } from 'State/LanguageState'
import { generateLocaleForJumio } from 'Utils/generateLocaleForJumio'

export default function BannerInfo({
    status,
    verified,
    urlPending,

    setLoader
}: {
    status: string | undefined
    verified: string | undefined
    urlPending?: string
    setLoader: (val: boolean) => void
}) {
    const { t } = useTranslation()
    let isPageWide = useMediaQuery('(min-width: 453px)')
    //Get current KYC logged in user
    const partyKYCFORM = useParty()
    //Ledger of KYC Component
    const ledgerKYCFORM = useLedger()

    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader
    } = useGlobalGamblingConfigContext()
    const { lang } = useI18LanguageContext()
    let locale = generateLocaleForJumio(lang)

    const handleVerification = async () => {
        try {
            setLoader(true)
            const jumio = GlobalGamblingConfigurationContract[0]?.payload?.integrationParties
                .entriesArray()
                .filter(a => a[0] === 'jumio')[0][1]
            await initialVerificationRequest(
                ledgerKYCFORM,
                partyKYCFORM,
                jumio,
                () => setLoader(false),
                locale
            )
            TagManager.dataLayer({
                dataLayer: {
                    event: 'started_kyc'
                }
            })
        } catch (error) {
            setLoader(false)
            toast.error('Something went wrong please try again later')
            console.error('Error on kyc', error)
        }
    }

    return (
        !GlobalGamblingConfigurationLoader && GlobalGamblingConfigurationContract?.length > 0 ? <div
            id="statusBanner"
            className={`info__banner info__banner--${status}`}
        >
            <div>
                <EnergyIcon />
                <p>{verified}</p>
                {status === 'failed' &&
                    <p>Please reach out{' '}
                        <a href="mailto:<EMAIL>" title="mailto:<EMAIL>"><EMAIL></a>{' '}
                        or create a ticket{' '}<a href="https://help.gambyl.com/en/support/tickets/new">HERE</a>{' '}for support.
                    </p>}
            </div>
            {status === 'pending' && (
                <button
                    className={`info__button info__button--${status}`}
                    onClick={() => window.open(urlPending, '_blank')}
                >
                    {isPageWide ? t("InfoIdentityBTN") : 'Verify'}
                </button>
            )}
            {status === 'notStarted' && (
                <button
                    className={`info__button info__button--${status}`}
                    onClick={async () => await handleVerification()}
                >
                    {isPageWide ? t("InfoIdentityBTN") : 'Verify'}
                </button>
            )}
        </div> : null

    )
}
