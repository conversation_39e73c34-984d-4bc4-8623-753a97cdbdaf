export const displayProp = (
    valueFromGamblerIdentity: string,
    valueFromGamblerUnverifiedIdentity: string
) => {
    let endValue = '-'

    const validatorGamblerIdentity =
        valueFromGamblerIdentity?.length > 0 && valueFromGamblerIdentity !== '-'

    const validatorUnverifiedGamblerIdentity =
        valueFromGamblerUnverifiedIdentity?.length > 0 &&
        valueFromGamblerUnverifiedIdentity !== '-'

    if (validatorGamblerIdentity) {
        endValue = valueFromGamblerIdentity
        return endValue
    }
    if (validatorUnverifiedGamblerIdentity) {
        endValue = valueFromGamblerUnverifiedIdentity
        return endValue
    }
    return endValue
}
