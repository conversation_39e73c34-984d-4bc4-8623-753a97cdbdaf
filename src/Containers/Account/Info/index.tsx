import React from 'react'
import ReactTooltip from 'react-tooltip'
import { Link } from 'react-router-dom'
import { useParty, useStreamQueries } from '@daml/react'
import { GamblerUnverifiedIdentity } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'

import { useVerificationStatusContext } from 'State/VerificationStatusProvider'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
    faPencilAlt,
    faTrash,
    faInfoCircle
} from '@fortawesome/free-solid-svg-icons'

import Banner from './Banner'
import Card from './Card'
import LoaderSpinner from 'Components/Loader'

import './style.scss'
import { convertCountryCodeToName } from 'Containers/UnverifiedGamblerIdentity/utils'
import UserData from './UserData'
import LocationData from './LocationData'
import useScrollToTop from 'Hooks/useScrollToTop'
import { useTranslation } from 'react-i18next'
import { maskPhoneNumber } from 'Utils/maskUserPhoneNumber'

const Tooltip = ({ topic }: { topic: string }) => {
    const { t } = useTranslation()
    return (
        <>
            <FontAwesomeIcon
                icon={faInfoCircle}
                data-for={`tooltip${topic}`}
                data-tip={`${t('InfoTooltip1')} ${topic}${t('InfoTooltip2')}`}
            />
            <ReactTooltip
                id={`tooltip${topic}`}
                place="top"
                type="dark"
                effect="solid"
                clickable
                class="tooltipMaxWidthMobile"
            />
        </>
    )
}

const EditBox: React.FC = () => (
    <span className="info__link">
        <Link to="/identity_setup">
            {' '}
            <FontAwesomeIcon icon={faPencilAlt} /> Add Personal Data{' '}
        </Link>
    </span>
)

const EditMailingAddress: React.FC = () => {
    const { t } = useTranslation()
    return (
        <span className="info__link">
            <Link to="/edit/profile">
                {' '}
                <FontAwesomeIcon icon={faPencilAlt} /> {t('edit')}{' '}
            </Link>
        </span>
    )
}

export default function Info() {
    const { t } = useTranslation()
    useScrollToTop()
    const [isLoading, setIsLoading] = React.useState(false)
    const {
        status,
        loading,
        gamblerIdentityContracts,
        pendingIdentityContracts
    } = useVerificationStatusContext()
    const party = useParty()
    const {
        loading: gamblerUnverifiedIdentityLoading,
        contracts: gamblerUnverifiedIdentity
    } = useStreamQueries(
        GamblerUnverifiedIdentity,
        () => {
            return [{ customer: party }]
        },
        [party]
    )

    const isVerified = () => {
        if (status === 'pending') {
            return t('InfoIdentityPending')
        }
        if (status === 'success') {
            return t('InfoIdentityVery')
        }
        if (status === 'notStarted') {
            return t('InfoIdentityNotStarted')
        }
        if (status === 'failed') {
            return t('InfoIdentityRejected')
        }
    }

    const phoneNumber = gamblerUnverifiedIdentity[0]?.payload?.phoneNumber
        ? gamblerUnverifiedIdentity[0]?.payload?.phoneNumber
        : '-'
    const maskedPhoneNumber = maskPhoneNumber(phoneNumber)

    const email = gamblerUnverifiedIdentity[0]?.payload?.emailAddress
        ? gamblerUnverifiedIdentity[0]?.payload?.emailAddress
        : '-'

    const paymentCountry = gamblerUnverifiedIdentity[0]?.payload?.countryCode
        ? convertCountryCodeToName(
              gamblerUnverifiedIdentity[0]?.payload.countryCode
          )
        : '-'

    const isComponentLoading =
        loading || gamblerUnverifiedIdentityLoading || isLoading

    if (isComponentLoading) {
        return <LoaderSpinner />
    }

    return (
        <>
            <>
                <div className="info">
                    <h3 className="info__title">{t('InfoIdentification')}</h3>
                    <hr />
                    <Banner
                        urlPending={
                            pendingIdentityContracts[0]?.payload.redirectUrl
                        }
                        status={status}
                        verified={isVerified()}
                        setLoader={setIsLoading}
                    />
                </div>
                <div className="info marginTop20">
                    <h3 className="info__title">
                        {t('InfoPersonalDetails')}{' '}
                        {gamblerUnverifiedIdentity[0]?.payload ? (
                            <Tooltip topic={t('InfoTooltipTopic')} />
                        ) : (
                            <EditBox />
                        )}
                    </h3>
                    <hr />
                    <div className="info__container">
                        <UserData
                            gamblerIdentity={gamblerIdentityContracts}
                            gamblerUnverifiedIdentity={
                                gamblerUnverifiedIdentity
                            }
                        />
                    </div>
                </div>
                <div className="info">
                    <h3 className="info__title">{t('InfoEmailTitle')}</h3>
                    <hr />
                    <div className="info__container">
                        <Card title={t('InfoEmailCard')} content={email} />
                        <div className="info__container">
                            <Card
                                title={t('PartyId')}
                                content={`${party.slice(0, 8)}...`}
                                hasClipboard
                                clipboardContent={party}
                            />
                        </div>
                    </div>
                </div>
                <div className="info">
                    <h3 className="info__title">{t('InfoPaymentsTitle')}</h3>
                    <hr />
                    <div className="info__container">
                        <Card
                            title={t('InfoPaymentCountry')}
                            content={paymentCountry}
                        />
                    </div>
                </div>
                <div className="info">
                    <h3 className="info__title">{t('InfoPhoneNumberTitle')}</h3>
                    <hr />
                    <div className="info__container">
                        <Card
                            title={t('InfoPhoneNumberTitle')}
                            content={maskedPhoneNumber}
                        />
                    </div>
                </div>
                <div className="info">
                    <h3 className="info__title">
                        {t('InfoMailTitle')}{' '}
                        {gamblerUnverifiedIdentity[0]?.payload ? (
                            <EditMailingAddress />
                        ) : null}
                    </h3>
                    <hr />
                    <div className="info__container">
                        <LocationData
                            gamblerIdentity={gamblerIdentityContracts}
                            gamblerUnverifiedIdentity={
                                gamblerUnverifiedIdentity
                            }
                        />
                    </div>
                </div>
                <div className="info">
                    <h3 className="info__title">{t('InfoCloseTitle')}</h3>
                    <hr />
                    <div
                        id="statusBanner"
                        className={`info__banner info__banner--failed`}
                    >
                        <div>
                            <FontAwesomeIcon icon={faTrash} />
                            <p>{t('InfoCloseP')}</p>
                        </div>
                        <Link
                            to="/close-account"
                            className="info__button info__button--failed"
                        >
                            {t('InfoCloseBTN')}
                        </Link>
                    </div>
                </div>
            </>
        </>
    )
}
