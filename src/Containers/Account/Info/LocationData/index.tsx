import React from 'react'
import {
    GamblerIdentity,
    GamblerUnverifiedIdentity
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { CreateEvent } from '@daml/ledger'
import Card from '../Card'
import { displayProp } from '../utils'
import { useTranslation } from 'react-i18next'

export default function LocationData({
    gamblerIdentity,
    gamblerUnverifiedIdentity
}: {
    gamblerIdentity: readonly CreateEvent<
        GamblerIdentity,
        GamblerIdentity.Key,
        string
    >[]
    gamblerUnverifiedIdentity: readonly CreateEvent<
        GamblerUnverifiedIdentity,
        GamblerUnverifiedIdentity.Key,
        string
    >[]
}) {
    const { t } = useTranslation()
    return (
        <>
            <Card
                title={t('InfoMail1')}
                content={displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.country,
                    '-'
                )}
            />
            <Card
                title={t('InfoMail2')}
                content={displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.city,
                    gamblerUnverifiedIdentity[0]?.payload?.city
                )}
            />
            <Card
                title={t('InfoMail3')}
                content={displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.subDivision,
                    gamblerUnverifiedIdentity[0]?.payload?.subDivision
                )}
            />
            <Card
                title={t('InfoMail4')}
                content={displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.addressLine1,
                    gamblerUnverifiedIdentity[0]?.payload?.addressLine1
                )}
            />
            <Card
                title={t('InfoMail6')}
                content={displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.postalCode,
                    gamblerUnverifiedIdentity[0]?.payload?.postalCode
                )}
            />
        </>
    )
}
