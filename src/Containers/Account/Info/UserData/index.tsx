import React from 'react'
import {
    GamblerIdentity,
    GamblerUnverifiedIdentity
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { CreateEvent } from '@daml/ledger'
import Card from '../Card'
import { displayProp } from '../utils'
import { useTranslation } from 'react-i18next'

export default function UserData({
    gamblerIdentity,
    gamblerUnverifiedIdentity
}: {
    gamblerIdentity: readonly CreateEvent<
        GamblerIdentity,
        GamblerIdentity.Key,
        string
    >[]
    gamblerUnverifiedIdentity: readonly CreateEvent<
        GamblerUnverifiedIdentity,
        GamblerUnverifiedIdentity.Key,
        string
    >[]
}) {
    const { t } = useTranslation()
    return (
        <>
            <Card
                title={t("InfoFName")}
                content={displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.firstName,
                    gamblerUnverifiedIdentity[0]?.payload?.firstName
                )}
            />
            <Card
                title={t("InfoLName")}
                content={displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.lastName,
                    gamblerUnverifiedIdentity[0]?.payload?.lastName
                )}
            />
            <Card
                title={t("InfoDOB")}
                content={displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.birthday,
                    gamblerUnverifiedIdentity[0]?.payload?.birthday
                )}
            />
        </>
    )
}
