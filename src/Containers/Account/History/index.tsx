import React, { useState } from 'react'
import { useParty } from '@daml/react'
import DatePicker from 'react-datepicker'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCalendarWeek } from '@fortawesome/free-solid-svg-icons'

import BetHistory from './BetHistory'
import BetResults from './BetResults'
import TabsWithoutLink from 'Components/TabsWithoutLink'
import useScrollToTop from 'Hooks/useScrollToTop'

import { getThreeDaysBehindDate } from './utils'

import { useTranslation } from 'react-i18next'

import 'react-datepicker/dist/react-datepicker.css'
import './style.scss'

export default function History() {
    const [selectedTab, setSelectedTab] = React.useState('History')
    const [startDate, setStartDate] = useState(getThreeDaysBehindDate())
    const [endDate, setEndDate] = useState(
        new Date(new Date().setHours(23, 59, 59, 999))
    )

    const party = useParty()
    const { t } = useTranslation()
    useScrollToTop()

    const onChange = (dates: any) => {
        const [start, end] = dates
        const modifiedStart = start
            ? new Date(new Date(start).setHours(0, 0, 0, 0))
            : start
        const modifiedEnd = end
            ? new Date(new Date(end).setHours(23, 59, 59, 999))
            : end
        setStartDate(modifiedStart)
        setEndDate(modifiedEnd)
    }

    const onCalendarClose = () => {
        if (startDate === null) {
            return setStartDate(
                new Date(new Date(endDate).setHours(0, 0, 0, 0))
            )
        }
        if (endDate === null) {
            return setEndDate(
                new Date(new Date(startDate).setHours(23, 59, 59, 999))
            )
        }
    }

    const tabs = [
        {
            label: t('BetHistory'),
            value: 'History',
            onclick: () => setSelectedTab('History')
        },
        {
            label: t('BetResults'),
            value: 'Results',
            onclick: () => setSelectedTab('Results')
        }
    ]

    return (
        <div className="history">
            <h4 className="history__dateRangeTitle">
                {' '}
                <FontAwesomeIcon icon={faCalendarWeek} />
                {t('DateRange')}
            </h4>
            <div className="history__dateContainer">
                <div className="history__dateContent">
                    <DatePicker
                        className="history__dateinput"
                        selected={startDate}
                        onChange={onChange}
                        startDate={startDate}
                        endDate={endDate}
                        selectsRange
                        onCalendarClose={onCalendarClose}
                    />
                </div>
            </div>
            <TabsWithoutLink tabs={tabs} selectedTab={selectedTab} />
            {selectedTab === 'History' ? (
                <BetHistory
                    startDate={startDate}
                    endDate={endDate}
                    userParty={party}
                />
            ) : (
                <BetResults
                    startDate={startDate}
                    endDate={endDate}
                    userParty={party}
                />
            )}
        </div>
    )
}
