import React from 'react'
import { BetHistory } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { CreateEvent } from '@daml/ledger'
import { useTranslation } from 'react-i18next'
import {
    CellContext,
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import numeral from 'numeral'

import GenericTable from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import getFormattedDate from 'Containers/Dashboard/getFormattedDate'
import BetPlacementIdCell from '../../shared/Cells/BetPlacementIdCell'
import EventTitleCell from '../../shared/Cells/EventTitleCell'
import { getTitleFunc } from 'Components/MyBets/utils'
import { useI18LanguageContext } from 'State/LanguageState'

interface IBetResultsTable {
    ticketId: string
    date: string
    outcome: string
    side: string
    eventTitle: { title: string; eventStartDate: string }
    wager: string
    fees: string
    winnings: string
    result: string
}

const convertToNumeral = (val: string | number | undefined) =>
    numeral(val).format('$0,0.00')

const BetResultsTable = ({
    betResultsContracts
}: {
    betResultsContracts: CreateEvent<BetHistory, BetHistory.Key, string>[]
}) => {
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()
    const columnHelper = createColumnHelper<IBetResultsTable>()

    const titleTicketId = t('ticketIdBetRes')
    const titleDate = t('dateBetRes')
    const titleOutcome = t('outcomeBetRes')
    const titleSide = t('sideBetRes')
    const titleEventTitle = t('eventTitleBetRes')
    const titleWager = t('wagerBetRes')
    const titleFees = t('feesBetRes')
    const titleWinnings = t('winningsBetRes')
    const titleResult = t('resultBetRes')

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('ticketId', {
                header: titleTicketId,
                cell: info =>
                    flexRender(BetPlacementIdCell, {
                        betPlacementId: info.getValue()
                    })
            }),
            columnHelper.accessor('date', {
                header: titleDate,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('outcome', {
                header: titleOutcome,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('side', {
                header: titleSide,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('eventTitle', {
                header: titleEventTitle,
                cell: info =>
                    flexRender(EventTitleCell, {
                        eventTitle: info.getValue().title,
                        startDate: info.getValue().eventStartDate
                    })
            }),
            columnHelper.accessor('wager', {
                header: titleWager,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('fees', {
                header: titleFees,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('winnings', {
                header: titleWinnings,
                cell: info => <>{info.getValue()}</>
            }),
            columnHelper.accessor('result', {
                header: titleResult,
                meta: {
                    getCellContext: (
                        context: CellContext<IBetResultsTable, string>
                    ) => {
                        return {
                            className:
                                context.getValue() === 'Won'
                                    ? 'success'
                                    : 'fail'
                        }
                    }
                },
                cell: info => <strong>{info.getValue()}</strong>
            })
        ],
        [
            columnHelper,
            titleDate,
            titleEventTitle,
            titleFees,
            titleOutcome,
            titleResult,
            titleSide,
            titleTicketId,
            titleWager,
            titleWinnings
        ]
    )

    const data = React.useMemo(
        () =>
            betResultsContracts
                .sort(
                    (a, b) =>
                        new Date(b.payload.placedAt).getTime() -
                        new Date(a.payload.placedAt).getTime()
                )
                .map(data => ({
                    ticketId: data.payload.details.betPlacementId,
                    date: getFormattedDate(data.payload.placedAt),
                    outcome: descriptCamelCase(
                        data.payload.details.side.value.outcome.type_.tag
                    ),
                    side: data.payload.details.side.tag,
                    eventTitle: {
                        title: getTitleFunc(
                            data.payload.details
                                ?.eventTitle as unknown as string[][],
                            lang
                        ),
                        eventStartDate: getFormattedDate(
                            data.payload.details.eventStartDate
                        )
                    },
                    wager: convertToNumeral(
                        data.payload.details.side.value.stake
                    ),
                    fees: convertToNumeral(
                        Number(data?.payload.winnings?.grossAmount) -
                            Number(data?.payload.winnings?.netAmount)
                    ),
                    winnings: convertToNumeral(
                        data?.payload.winnings?.netAmount
                    ),
                    result: data.payload.won ? 'Won' : 'Lost'
                })),
        [betResultsContracts, lang]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: true
    })

    return (
        <>
            <GenericTable
                tableHeader={table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map(header => {
                            return (
                                <th key={header.id}>
                                    {header.column.columnDef.header}
                                </th>
                            )
                        })}
                    </tr>
                ))}
                tableBodyRow={table.getRowModel().rows.map(row => (
                    <tr key={row.id}>
                        {row.getVisibleCells().map(cell => {
                            let hasMeta =
                                cell.getContext().cell.column.columnDef.meta
                            return (
                                <td
                                    key={cell.id}
                                    {...(hasMeta && {
                                        ...hasMeta.getCellContext(
                                            cell.getContext()
                                        )
                                    })}
                                >
                                    {flexRender(
                                        cell.column.columnDef.cell,
                                        cell.getContext()
                                    )}
                                </td>
                            )
                        })}
                    </tr>
                ))}
            />
            <Pagination table={table} />
        </>
    )
}

export default BetResultsTable
