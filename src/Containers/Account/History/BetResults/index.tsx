import React from 'react'
import { useQuery } from 'react-query'
import { BetHistory } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { CreateEvent } from '@daml/ledger'
import { useTranslation } from 'react-i18next'

import usePartyToken from 'Hooks/usePartyToken'

import { optionsConstructor, url } from 'Containers/Account/utils'

import LoaderSpinner from 'Components/Loader'
import SelectFilter from 'Components/Select'
import BetResultsTable from './BetResultsTable'

const BetResults = ({
    startDate,
    endDate,
    userParty
}: {
    startDate: Date
    endDate: Date
    userParty: string
}) => {
    const [statusFilter, setStatusFilter] = React.useState('')
    const { t } = useTranslation()
    const { tokenToDAMLProvider: token } = usePartyToken()
    const filterOptions = [
        { label: t('DropdownBetResults1'), value: '' },
        {
            label: t('DropdownBetResults2'),
            value: 'true'
        },
        {
            label: t('DropdownBetResults3'),
            value: 'false'
        }
    ]

    const values = !statusFilter.length
        ? {
              templateIds: [BetHistory.templateId],
              query: {
                  customer: userParty,
                  placedAt: {
                      '%lte': endDate ? endDate : startDate,
                      '%gte': startDate ? startDate : endDate
                  }
              }
          }
        : {
              templateIds: [BetHistory.templateId],
              query: {
                  customer: userParty,
                  placedAt: {
                      '%lte': endDate ? endDate : startDate,
                      '%gte': startDate ? startDate : endDate
                  },
                  won: statusFilter === 'true'
              }
          }

    const { data, isLoading } = useQuery<{
        result: CreateEvent<BetHistory, BetHistory.Key, string>[]
        status: number | string
    }>([`${JSON.stringify(values)}__results`, values], () =>
        fetch(`${url}v1/query`, optionsConstructor(token, values)).then(res =>
            res.json()
        )
    )

    if (isLoading) {
        return (
            <>
                <LoaderSpinner />
            </>
        )
    }

    return (
        <>
            <SelectFilter
                selectedOption={statusFilter}
                options={filterOptions}
                setSelectedValue={setStatusFilter}
            />
            <div className="history__tableTitle" style={{ marginTop: '20px' }}>
                <h4>{t('Activities')}</h4>
            </div>
            {data?.result && data?.result.length > 0 ? (
                <BetResultsTable betResultsContracts={data.result} />
            ) : (
                <div>
                    <p>{t('NoDataToPresent')}</p>
                </div>
            )}
        </>
    )
}

export default BetResults
