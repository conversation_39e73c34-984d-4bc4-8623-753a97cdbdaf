import React from 'react'
import { useQuery } from 'react-query'
import { BetPlacement } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { CreateEvent } from '@daml/ledger'
import { useTranslation } from 'react-i18next'

import usePartyToken from 'Hooks/usePartyToken'

import BetHistoryTable from './BetHistoryTable'
import BetHistoryCards from './BetHistoryCards'
import LoaderSpinner from 'Components/Loader'
import SelectFilter from 'Components/Select'
import { optionsConstructor, url } from 'Containers/Account/utils'

import '../style.scss'

export default function BetHistory({
    startDate,
    endDate,
    userParty
}: {
    startDate: Date
    endDate: Date
    userParty: string
}) {
    const [statusFilter, setStatusFilter] = React.useState('')
    const { tokenToDAMLProvider: token } = usePartyToken()
    const { t } = useTranslation()

    const filterOptions = [
        { label: t('DropdownBetHistory1'), value: '' },
        {
            label: t('DropdownBetHistory2'),
            value: 'Unmatched'
        },
        {
            label: t('DropdownBetHistory3'),
            value: 'Matched'
        },
        //TODO IN FUTURE: REMOVE CANCELLED AND ADD NEW TAB
        {
            label: t('DropdownBetHistory4'),
            value: 'Cancelled'
        }
    ]

    const values = statusFilter.length
        ? {
              templateIds: [BetPlacement.templateId],
              query: {
                  customer: userParty,
                  placedAt: {
                      '%lte': endDate ? endDate : startDate,
                      '%gte': startDate ? startDate : endDate
                  },
                  status: statusFilter
              }
          }
        : {
              templateIds: [BetPlacement.templateId],
              query: {
                  customer: userParty,
                  placedAt: {
                      '%lte': endDate ? endDate : startDate,
                      '%gte': startDate ? startDate : endDate
                  }
              }
          }

    const { data, isLoading } = useQuery<{
        result: CreateEvent<BetPlacement, BetPlacement.Key, string>[]
        status: number | string
    }>([`${statusFilter}__history`, values], () =>
        fetch(`${url}v1/query`, optionsConstructor(token, values)).then(res =>
            res.json()
        )
    )

    if (isLoading) {
        return (
            <>
                <LoaderSpinner />
            </>
        )
    }

    return (
        <>
            <SelectFilter
                selectedOption={statusFilter}
                options={filterOptions}
                setSelectedValue={setStatusFilter}
            />
            {data?.result ? (
                <BetHistoryCards
                    betHistoryContracts={data.result}
                    status={statusFilter}
                />
            ) : null}
            <div className="history__tableTitle" style={{ marginTop: '20px' }}>
                <h4>{t('Activities')}</h4>
            </div>
            {data?.result && data?.result.length > 0 ? (
                <BetHistoryTable betHistoryContracts={data.result} />
            ) : (
                <div>
                    <p>{t('NoDataToPresent')}</p>
                </div>
            )}
        </>
    )
}
