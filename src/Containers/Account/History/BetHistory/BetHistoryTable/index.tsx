import React from 'react'
import { useTranslation } from 'react-i18next'
import {
    CellContext,
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import numeral from 'numeral'
import { CreateEvent } from '@daml/ledger'
import { BetPlacement } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'

import GenericTable from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import getFormattedDate from 'Containers/Dashboard/getFormattedDate'

import BetPlacementIdCell from '../../shared/Cells/BetPlacementIdCell'
import EventTitleCell from '../../shared/Cells/EventTitleCell'
import {
    calculateBetResult,
    getTitleFunc,
    prepareOddForDisplay
} from 'Components/MyBets/utils'
import { useOddType } from 'State/OddTypeContext'
import { useI18LanguageContext } from 'State/LanguageState'
import Tooltip from './Tooltip'

interface IBetHistoryTable {
    betPlacementId: string
    placedAt: string
    status: string
    outcomeTypeTag: string
    sideTag: string
    eventTitle: { title: string; eventStartDate: string }
    stake: string
    odd: string
    profitLoss: { amount: string; side: string }
}

const BetHistoryTable = ({
    betHistoryContracts
}: {
    betHistoryContracts: CreateEvent<BetPlacement, BetPlacement.Key, string>[]
}) => {
    const { oddState } = useOddType()
    const { lang } = useI18LanguageContext()
    const { t } = useTranslation()

    const titleBetPlacementId = t('ticketIdBetHist')
    const titlePlacedAt = t('dateBetHist')
    const titleStatus = t('statusBetHist')
    const titleOutcomeTypeTag = t('outcomeBetHist')
    const titleSideTag = t('sideBetHist')
    const titleEventTitle = t('detailsBetHist')
    const titleStake = t('wagerBetHist')
    const titleOdds = t('oddsBetHist')
    const titleProfitLoss = t('profitLossBetHist')

    const columnHelper = createColumnHelper<IBetHistoryTable>()

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('betPlacementId', {
                header: titleBetPlacementId,
                cell: info =>
                    flexRender(BetPlacementIdCell, {
                        betPlacementId: info.getValue()
                    })
            }),
            columnHelper.accessor('placedAt', {
                header: titlePlacedAt,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('status', {
                header: titleStatus,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('outcomeTypeTag', {
                header: titleOutcomeTypeTag,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('sideTag', {
                header: titleSideTag,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('eventTitle', {
                header: titleEventTitle,
                cell: info =>
                    flexRender(EventTitleCell, {
                        eventTitle: info.getValue().title,
                        startDate: info.getValue().eventStartDate
                    })
            }),
            columnHelper.accessor('stake', {
                header: titleStake,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('odd', {
                header: titleOdds,
                cell: info => info.getValue()
            }),
            columnHelper.accessor('profitLoss', {
                header: titleProfitLoss,
                meta: {
                    getCellContext: (
                        context: CellContext<
                            IBetHistoryTable,
                            { amount: string; side: string }
                        >
                    ) => {
                        return {
                            className:
                                context.getValue().side === 'Back'
                                    ? 'success'
                                    : 'fail'
                        }
                    }
                },
                cell: info => <>{info.getValue().amount}</>
            })
        ],
        [
            columnHelper,
            titleBetPlacementId,
            titleEventTitle,
            titleOdds,
            titleOutcomeTypeTag,
            titlePlacedAt,
            titleProfitLoss,
            titleSideTag,
            titleStake,
            titleStatus
        ]
    )

    const data = React.useMemo(
        () =>
            betHistoryContracts
                .sort(
                    (a, b) =>
                        new Date(b.payload.placedAt).getTime() -
                        new Date(a.payload.placedAt).getTime()
                )
                .map(data => ({
                    betPlacementId: data.payload.details.betPlacementId,
                    placedAt: getFormattedDate(data.payload.placedAt),
                    status: data.payload.status,
                    outcomeTypeTag: descriptCamelCase(
                        data.payload.details.side.value.outcome.type_.tag
                    ),
                    sideTag: data.payload.details.side.tag,
                    eventTitle: {
                        title: getTitleFunc(
                            data.payload.details
                                ?.eventTitle as unknown as string[][],
                            lang
                        ),
                        eventStartDate: getFormattedDate(
                            data.payload.details?.eventStartDate
                        )
                    },
                    stake: numeral(
                        data.payload.details.side.value.stake
                    ).format('$0,0.00'),
                    odd: prepareOddForDisplay(data, oddState).toString(),
                    profitLoss: {
                        amount: `${
                            data.payload.details.side.tag === 'Back'
                                ? '+ '
                                : '- '
                        } ${numeral(
                            calculateBetResult(
                                Number(
                                    data.payload.details.side.value.odd.value
                                ),
                                data.payload.details.side.value.odd.tag,
                                data.payload.details.side.value.stake
                            )
                        ).format('$0,0.00')}`,
                        side: data.payload.details.side.tag
                    }
                })),
        [betHistoryContracts, lang, oddState]
    )
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: true
    })

    return (
        <>
            <GenericTable
                tableHeader={table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map(header => {
                            return (
                                <th key={header.id}>
                                    {header.column.columnDef.header}
                                    {header.column.id === 'profitLoss' ? (
                                        <Tooltip topic={t('tooltipBetHist')} />
                                    ) : null}
                                </th>
                            )
                        })}
                    </tr>
                ))}
                tableBodyRow={table.getRowModel().rows.map(row => (
                    <tr key={row.id}>
                        {row.getVisibleCells().map(cell => {
                            let hasMeta =
                                cell.getContext().cell.column.columnDef.meta
                            return (
                                <td
                                    key={cell.id}
                                    {...(hasMeta && {
                                        ...hasMeta.getCellContext(
                                            cell.getContext()
                                        )
                                    })}
                                >
                                    {flexRender(
                                        cell.column.columnDef.cell,
                                        cell.getContext()
                                    )}
                                </td>
                            )
                        })}
                    </tr>
                ))}
            />
            <Pagination table={table} />
        </>
    )
}

export default BetHistoryTable
