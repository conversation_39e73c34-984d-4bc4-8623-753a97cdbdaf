import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import ReactTooltip from 'react-tooltip'

const Tooltip = ({ topic }: { topic: string }) => (
    <>
        <FontAwesomeIcon
            icon={faInfoCircle}
            data-for={topic}
            data-tip={topic}
        />
        <ReactTooltip
            id={topic}
            place="top"
            clickable
            class="tooltipTableHeader"
        />
    </>
)
export default Tooltip
