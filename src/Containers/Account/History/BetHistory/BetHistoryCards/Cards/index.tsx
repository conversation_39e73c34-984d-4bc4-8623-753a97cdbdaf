import React from 'react'
import numeral from 'numeral'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import ReactTooltip from 'react-tooltip'
import { useTranslation } from 'react-i18next'

export default function Cards({
    totalBets,
    totalWager,
    totalProfit,
    totalExposure,
    status
}: {
    totalBets: string | number
    totalWager: string | number
    totalProfit: string | number
    totalExposure: string | number
    status: string
}) {
    const { t } = useTranslation()
    const TooltipStaked = () => (
        <>
            <FontAwesomeIcon
                icon={faInfoCircle}
                data-for="tooltipStaked"
                data-tip={t('BetHistoryTooltip')}
            />
            <ReactTooltip
                id="tooltipStaked"
                place="top"
                type="dark"
                effect="solid"
                clickable
                class="tooltipMaxWidthMobile"
            />
        </>
    )

    return (
        <div className="history__cards">
            <div className="history__card">
                <p>{t('BetHistoryCard1')}</p>
                <strong>{totalBets}</strong>
            </div>
            <div className="history__card">
                <div>
                    {t('BetHistoryCard2')}{' '}
                    {status === '' ? <TooltipStaked /> : null}
                </div>
                <strong>
                    {status === 'Cancelled'
                        ? numeral(0).format('$0,0.00')
                        : numeral(totalWager).format('$0,0.00')}
                </strong>
            </div>
            <div className="history__card">
                <p>{t('BetHistoryCard3')}</p>
                <strong>{numeral(totalProfit).format('$0,0.00')}</strong>
            </div>
            <div className="history__card">
                <p>{t('BetHistoryCard4')}</p>
                <strong>{numeral(totalExposure).format('$0,0.00')}</strong>
            </div>
        </div>
    )
}
