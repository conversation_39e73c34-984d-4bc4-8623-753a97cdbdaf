import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { BetPlacement } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import {
    calculateTotalExposure,
    calculateTotalProfit,
    calculateTotalWager
} from '../../utils'

import Cards from './Cards'

const BetHistoryCards = ({
    betHistoryContracts,
    status
}: {
    betHistoryContracts: CreateEvent<BetPlacement, BetPlacement.Key, string>[]
    status: string
}) => {
    const totalWager = calculateTotalWager(betHistoryContracts)
    const totalProfit = calculateTotalProfit(betHistoryContracts)
    const totalExposure = calculateTotalExposure(betHistoryContracts)
    return (
        <Cards
            totalBets={betHistoryContracts.length}
            totalWager={totalWager}
            totalExposure={totalExposure}
            totalProfit={totalProfit}
            status={status}
        />
    )
}

export default BetHistoryCards
