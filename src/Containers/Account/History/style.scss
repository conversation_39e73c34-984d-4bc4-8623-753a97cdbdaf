@import '../../../Styles/colors';

.history {
    &__mt40 {
        margin-top: 40px;
    }

    &__dateRangeTitle {
        display: flex;
        align-items: baseline;
        gap: 10px;
        padding: 10px 0px;
    }

    &__cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        margin: 40px 0px;
        gap: 20px;
    }

    &__dateContainer {
        display: flex;
        justify-content: flex-start;
        gap: 20px;
    }

    &__dateContent {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    &__selectFilter {
        font-family: 'Montserrat', sans-serif;
        font-size: 1.05rem;
        font-style: normal;
        font-weight: 500;
        line-height: 1.5rem;
        letter-spacing: 0px;
        text-align: left;
        border: none;
        color: $darkGrey;
        cursor: pointer;
        width: 220px;
        border: 1px solid $grayInpuBorder;
        padding: 5px 10px;
        border-radius: 6px;

        &:focus {
            outline: 1px solid $grayInpuBorder;
            border: none;
        }
    }

    &__dateinput {
        font-family: 'Montserrat', sans-serif;
        font-size: 1.05rem;
        font-style: normal;
        font-weight: 500;
        line-height: 1.5rem;
        letter-spacing: 0px;
        text-align: left;
        border: none;
        color: $darkGrey;
        cursor: pointer;
        width: 220px;
        border: 1px solid $grayInpuBorder;
        padding: 5px 10px;
        border-radius: 6px;

        &:focus {
            outline: none;
            border: none;
        }
    }

    &__card {
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: 500;
        font-size: 1.125rem;
        line-height: 1.563rem;
        text-align: center;
    }

    &__tableTitle {
        font-family: 'Montserrat-Bold', sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 0.813rem;
        line-height: 1.25rem;
        margin-bottom: 40px;
    }

    &__tabs {
        padding: 20px 0px;
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        font-size: 1rem;
        line-height: 1.25rem;
        margin: 20px 0;
        font-weight: bold;
        text-transform: capitalize;
    }

    &__tab {
        cursor: pointer;
        border-bottom: 2px solid transparent;

        &--active {
            color: $purple;
            border-bottom: 2px solid $purple;
        }
    }

    &__pagination {
        align-items: center;
        display: flex;
        gap: 40px;
        flex-wrap: wrap;
        justify-content: flex-start;
        margin-top: 10px;

        li {
            list-style: none;
            border-bottom: 3px solid transparent;
        }

        .previous,
        .next {
            color: $purple;
            cursor: pointer;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .previous.disabled,
        .next.disabled {
            color: #8298ab;
            cursor: not-allowed;
        }

        .selected {
            border-bottom: 3px solid $purple;
            color: $purple;
            font-weight: bold;
        }
    }

    .react-datepicker__time-container {
        .react-datepicker__time {
            .react-datepicker__time-box {
                ul.react-datepicker__time-list {
                    li.react-datepicker__time-list-item {
                        &--selected {
                            background-color: $purple;

                            &:hover {
                                background-color: $purple;
                            }
                        }
                    }
                }
            }
        }
    }

    .react-datepicker__month,
    .react-datepicker__quarter {
        &--selected,
        &--in-selecting-range,
        &--in-range {
            background-color: $purple;

            &:hover {
                background-color: darken($purple, 5%);
            }
        }
    }

    .react-datepicker__day,
    .react-datepicker__month-text,
    .react-datepicker__quarter-text,
    .react-datepicker__year-text {
        &--selected,
        &--in-selecting-range,
        &--in-range {
            background-color: $purple;

            &:hover {
                background-color: darken($purple, 5%);
            }
        }

        &--keyboard-selected {
            background-color: lighten($purple, 10%);

            &:hover {
                background-color: darken($purple, 5%);
            }
        }

        &--in-selecting-range:not(&--in-range) {
            background-color: rgba($purple, 0.5);
        }

        &--in-range:not(&--in-selecting-range) {
            .react-datepicker__month--selecting-range & {
                background-color: $purple;
            }
        }
    }

    .react-datepicker__month-text,
    .react-datepicker__quarter-text {
        &.react-datepicker__month--selected,
        &.react-datepicker__month--in-range,
        &.react-datepicker__quarter--selected,
        &.react-datepicker__quarter--in-range {
            &:hover {
                background-color: $purple;
            }
        }
    }
}
