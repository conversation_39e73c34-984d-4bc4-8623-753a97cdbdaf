import { BetPlacement } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { CreateEvent } from '@daml/ledger'
import { calculateBetResult } from 'Components/MyBets/utils'

type DataToCalculate = CreateEvent<BetPlacement, BetPlacement.Key, string>[]

export const getThreeDaysBehindDate = () => {
    let date = new Date()
    let dateWith3DaysAdded = date.setDate(date.getDate() - 2)
    let dateAtMidnight = new Date(dateWith3DaysAdded).setHours(0, 0, 0, 0)
    return new Date(dateAtMidnight)
}

export const calculateTotalWager = (data: DataToCalculate) =>
    data.reduce(function (previousValue: number, currentValue: any) {
        const { side } = currentValue.payload.details
        if (currentValue.payload?.status !== 'Cancelled') {
            if (side.tag === 'Back' && Boolean(side.value.stake)) {
                return previousValue + Number(side.value.stake)
            } else {
                return (
                    previousValue +
                    calculateBetResult(
                        Number(side.value.odd.value),
                        side.value.odd.tag,
                        side.value.stake
                    )
                )
            }
        } else {
            return previousValue
        }
    }, 0)

export const calculateTotalProfit = (data: DataToCalculate) =>
    data.reduce(function (previousValue: number, currentValue: any) {
        const { side } = currentValue.payload.details
        if (
            side.tag === 'Back' &&
            Boolean(side.value.stake) &&
            currentValue.payload?.status !== 'Cancelled'
        ) {
            return (
                previousValue +
                calculateBetResult(
                    Number(side.value.odd.value),
                    side.value.odd.tag,
                    side.value.stake
                )
            )
        } else {
            return previousValue
        }
    }, 0)

export const calculateTotalExposure = (data: DataToCalculate) =>
    data.reduce(function (previousValue: number, currentValue: any) {
        const { side } = currentValue.payload.details
        if (
            side.tag === 'Lay' &&
            Boolean(side.value.stake) &&
            currentValue.payload?.status !== 'Cancelled'
        ) {
            return (
                previousValue +
                calculateBetResult(
                    Number(side.value.odd.value),
                    side.value.odd.tag,
                    side.value.stake
                )
            )
        } else {
            return previousValue
        }
    }, 0)
