import React, { useState } from 'react'
import { useQuery } from 'react-query'
import { useParty } from '@daml/react'
import { CreateEvent } from '@daml/ledger'
import { TransactionHistory } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'

import useScrollToTop from 'Hooks/useScrollToTop'
import usePartyToken from 'Hooks/usePartyToken'

import { url, optionsConstructor } from 'Containers/Account/utils'
import { generatorRequestBody } from './utils'

import BonusSection from './BonusSection'
import BonusTable from './BonusTable'
import FilterSection from './FilterSection'

import './style.scss'
import { useAccountContex } from 'State/AccountContext'

export default function Rewards() {
    useScrollToTop()
    const party = useParty()
    const { tokenToDAMLProvider: token } = usePartyToken()

    const [startDate, setStartDate] = useState(
        new Date(new Date().setHours(0, 0, 0, 0))
    )
    const [endDate, setEndDate] = useState(
        new Date(new Date().setHours(23, 59, 59, 999))
    )
    const onChange = (dates: any) => {
        const [start, end] = dates
        const modifiedStart = start
            ? new Date(new Date(start).setHours(0, 0, 0, 0))
            : start
        const modifiedEnd = end
            ? new Date(new Date(end).setHours(23, 59, 59, 999))
            : end
        setStartDate(modifiedStart)
        setEndDate(modifiedEnd)
    }

    const onCalendarClose = () => {
        if (startDate === null) {
            return setStartDate(
                new Date(new Date(endDate).setHours(0, 0, 0, 0))
            )
        }
        if (endDate === null) {
            return setEndDate(
                new Date(new Date(startDate).setHours(23, 59, 59, 999))
            )
        }
    }

    const RequestBodyTransactionHistory = generatorRequestBody(
        TransactionHistory.templateId,
        endDate ? endDate : startDate,
        startDate ? startDate : endDate,
        party
    )

    const { accountContracts } = useAccountContex()

    const {
        data: TransactionHistoryData
        //TODO CHECK LOADER HERE WHAT TO TODO
        // isLoading: TransactionHistoryLoading
    } = useQuery<{
        result: CreateEvent<TransactionHistory>[]
        status: number | string
    }>(
        [
            `${RequestBodyTransactionHistory}__rewards`,
            RequestBodyTransactionHistory
        ],
        () =>
            fetch(
                `${url}v1/query`,
                optionsConstructor(token, RequestBodyTransactionHistory)
            ).then(res => res.json())
    )

    const bonusContracts = TransactionHistoryData?.result
        ? TransactionHistoryData.result.filter(
              data => data.payload.transactionType === 'Bonus'
          )
        : []

    return (
        <div className="rewards">
            <BonusSection
                totalBonusBalance={
                    accountContracts[0]?.payload?.totalBonusBalance
                }
            />
            <FilterSection
                startDate={startDate}
                onChange={onChange}
                endDate={endDate}
                onCalendarClose={onCalendarClose}
            />
            <BonusTable bonusContracts={bonusContracts} />
        </div>
    )
}
