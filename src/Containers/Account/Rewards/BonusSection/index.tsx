import React from 'react'
import { useTranslation } from 'react-i18next'
import numeral from 'numeral'

const BonusSection = ({ totalBonusBalance }: { totalBonusBalance: string }) => {
    const { t } = useTranslation()
    return (
        <div className="rewards__cards">
            <div className="rewards__card" />
            <div className="rewards__card">
                <p>{t('RewardsCard')}</p>
                <strong>{numeral(totalBonusBalance).format('$0,0.00')}</strong>
            </div>
            <div className="rewards__card" />
        </div>
    )
}

export default BonusSection
