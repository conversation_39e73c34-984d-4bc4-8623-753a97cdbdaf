@import '../../../Styles/colors';

.rewards {
    &__cards {
        display: grid;
        gap: 20px;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        margin: 40px 0px;
    }

    &__dateContainer {
        display: flex;
        justify-content: flex-start;
        gap: 20px;
        padding: 20px 0;
    }

    &__dateContent {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    &__dateinput {
        font-family: 'Montserrat', sans-serif;
        font-size: 1.25rem;
        font-style: normal;
        font-weight: 700;
        line-height: 1.5rem;
        letter-spacing: 0px;
        text-align: left;
        border: none;

        &:focus {
            outline: none;
            border: none;
        }
    }

    .react-datepicker-year-header {
        background-color: $purple;
        color: $white;
    }

    .react-datepicker__month-text--keyboard-selected {
        background-color: $purple;
        color: $white;
    }

    .react-datepicker__month--in-range {
        background-color: $purple;
        color: $white;
    }

    .react-datepicker__month-text.react-datepicker__month--in-range:hover {
        background-color: $purple;
        color: $white;
    }

    &__card {
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: 500;
        font-size: 1.125rem;
        line-height: 1.563rem;
        text-align: center;
    }

    &__tableTitle {
        font-family: 'Montserrat-Bold', sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 0.813rem;
        line-height: 1.25rem;
        margin-bottom: 40px;

        hr {
            margin: 20px 0;
            border: 0.02rem solid rgba(130, 152, 171, 0.2);
        }
    }

    &__p {
        &--success {
            color: $successgreen;
            font-weight: bold;
        }
    }

    &__tableWrapper {
        display: inline-block;
        overflow-x: auto;
        width: 100%;
    }

    &__table {
        display: table;
        width: 100%;
        td,
        th {
            padding: 15px;
            text-align: left;
        }
    }

    .react-datepicker__time-container {
        .react-datepicker__time {
            .react-datepicker__time-box {
                ul.react-datepicker__time-list {
                    li.react-datepicker__time-list-item {
                        &--selected {
                            background-color: $purple;

                            &:hover {
                                background-color: $purple;
                            }
                        }
                    }
                }
            }
        }
    }

    .react-datepicker__month,
    .react-datepicker__quarter {
        &--selected,
        &--in-selecting-range,
        &--in-range {
            background-color: $purple;

            &:hover {
                background-color: darken($purple, 5%);
            }
        }
    }

    .react-datepicker__day,
    .react-datepicker__month-text,
    .react-datepicker__quarter-text,
    .react-datepicker__year-text {
        &--selected,
        &--in-selecting-range,
        &--in-range {
            background-color: $purple;

            &:hover {
                background-color: darken($purple, 5%);
            }
        }

        &--keyboard-selected {
            background-color: lighten($purple, 10%);

            &:hover {
                background-color: darken($purple, 5%);
            }
        }

        &--in-selecting-range:not(&--in-range) {
            background-color: rgba($purple, 0.5);
        }

        &--in-range:not(&--in-selecting-range) {
            .react-datepicker__month--selecting-range & {
                background-color: $purple;
            }
        }
    }

    .react-datepicker__month-text,
    .react-datepicker__quarter-text {
        &.react-datepicker__month--selected,
        &.react-datepicker__month--in-range,
        &.react-datepicker__quarter--selected,
        &.react-datepicker__quarter--in-range {
            &:hover {
                background-color: $purple;
            }
        }
    }
}
