export const generatorRequestBody = (
    templateId: string,
    lesserThen: Date,
    greaterThen: Date,
    party: string
) => ({
    templateIds: [templateId],
    query: {
        customer: party,
        startedAt: {
            '%gte': greaterThen
        },
        terminatedAt: {
            '%lte': lesserThen
        }
    }
})

export const getValueOfOfferedAmount = (data: any) => {
    let valueFromPayload = data?.payload?.totalUsedOn
    let returnValue = Object.values(valueFromPayload.flat()[1])[1]
    return returnValue
}
