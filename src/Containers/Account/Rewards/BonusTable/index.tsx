import React from 'react'
import numeral from 'numeral'
import { useTranslation } from 'react-i18next'
import { CreateEvent } from '@daml/ledger'
import { TransactionHistory } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import Pagination from 'Components/Table/Pagination'

interface IBonusTable {
    rewardsDate: string
    rewardsAward: string
}

const BonusTable = ({
    bonusContracts
}: {
    bonusContracts: CreateEvent<TransactionHistory>[]
}) => {
    const { t } = useTranslation()
    const titleRewardsDate = t('RewardsDate')
    const titleRewardsAward = t('RewardsAward')
    const columnHelper = createColumnHelper<IBonusTable>()

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('rewardsDate', {
                header: titleRewardsDate,
                cell: info => <>{info.getValue().slice(0, 10)}</>
            }),
            columnHelper.accessor('rewardsAward', {
                header: titleRewardsAward,
                cell: info => info.getValue()
            })
        ],
        [columnHelper, titleRewardsAward, titleRewardsDate]
    )

    const data = React.useMemo(
        () =>
            bonusContracts.map(data => ({
                rewardsDate: data.payload.terminatedAt.substring(0, 10),
                rewardsAward: `${numeral(data?.payload.confirmedAmount).format(
                    '$0,0.00'
                )} bonus award`
            })),
        [bonusContracts]
    )
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: true
    })

    return (
        <>
            <div className="rewards__tableTitle">
                <h4>{t('Activities')}</h4>
                <hr />
            </div>
            {bonusContracts?.length > 0 ? (
                <>
                    <div className="rewards__tableWrapper">
                        <table className="rewards__table">
                            <thead>
                                {table.getHeaderGroups().map(headerGroup => (
                                    <tr key={headerGroup.id}>
                                        {headerGroup.headers.map(header => (
                                            <th key={header.id}>
                                                {header.column.columnDef.header}
                                                :
                                            </th>
                                        ))}
                                    </tr>
                                ))}
                            </thead>
                            <tbody>
                                {table.getRowModel().rows.map(row => (
                                    <tr key={row.id}>
                                        {row.getVisibleCells().map(cell => (
                                            <td key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    <Pagination table={table} />
                </>
            ) : (
                <div>
                    <p>{t('NoDataToPresent')}</p>
                </div>
            )}
        </>
    )
}

export default BonusTable
