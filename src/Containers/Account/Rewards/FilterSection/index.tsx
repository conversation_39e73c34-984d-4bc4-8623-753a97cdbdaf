import React from 'react'
import DatePicker from 'react-datepicker'

import 'react-datepicker/dist/react-datepicker.css'

const FilterSection = ({
    startDate,
    onChange,
    endDate,
    onCalendarClose
}: {
    startDate: Date
    onChange: (dates: any) => void
    endDate: Date
    onCalendarClose: () => void
}) => {
    return (
        <div className="rewards__dateContainer">
            <div className="history__dateContainer">
                <div className="history__dateContent">
                    <DatePicker
                        className="history__dateinput"
                        selected={startDate}
                        onChange={onChange}
                        startDate={startDate}
                        endDate={endDate}
                        selectsRange
                        onCalendarClose={onCalendarClose}
                    />
                </div>
            </div>
        </div>
    )
}

export default FilterSection
