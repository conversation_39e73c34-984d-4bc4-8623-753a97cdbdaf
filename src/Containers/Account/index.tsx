import React from 'react'
import { Switch, Route } from 'react-router'
import { useTranslation } from 'react-i18next'

import Info from 'Containers/Account/Info'
import Payments from 'Containers/Account/Payments'
import Balance from 'Containers/Account/Balance'
import Rewards from 'Containers/Account/Rewards'
import History from 'Containers/Account/History'

import Tabs from 'Components/Tabs'

import './style.scss'

export default function Account() {
    const { t } = useTranslation()

    const accountTabs = [
        { titleRoute: t('AccountTabs1'), urlRoute: '' },
        { titleRoute: t('AccountTabs2'), urlRoute: '/balance' },
        { titleRoute: t('AccountTabs3'), urlRoute: '/history' },
        { titleRoute: t('AccountTabs4'), urlRoute: '/payments' },
        { titleRoute: t('AccountTabs5'), urlRoute: '/rewards' }
    ]
    return (
        <div className="content__container accountProfile pagePadding">
            <div className="accountProfile__card">
                <h1>{t('AccountProfile')}</h1>
                <Tabs routes={accountTabs} />
                <div className="accountProfile__routes">
                    <Switch>
                        <Route exact path={'/account'}>
                            <Info />
                        </Route>
                        <Route exact path={'/account/balance'}>
                            <Balance />
                        </Route>
                        <Route exact path={'/account/payments'}>
                            <Payments />
                        </Route>
                        <Route exact path={'/account/history'}>
                            <History />
                        </Route>
                        <Route exact path={'/account/rewards'}>
                            <Rewards />
                        </Route>
                    </Switch>
                </div>
            </div>
        </div>
    )
}
