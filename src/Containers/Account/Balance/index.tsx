import React from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import numeral from 'numeral'

import useScrollToTop from 'Hooks/useScrollToTop'

import './style.scss'
import { useAccountContex } from 'State/AccountContext'

const BalanceItem = ({
    title,
    value,
    isBonusBalance = false
}: {
    title: string
    value: string
    isBonusBalance?: boolean
}) => {
    return (
        <div className="balance__block__item">
            <h3>{title}</h3>
            <div
                className={`balance__block__item__value  ${
                    isBonusBalance === true ? 'green' : ''
                } `}
            >
                {value}
            </div>
        </div>
    )
}

export default function Balance() {
    useScrollToTop()

    const { t } = useTranslation()
    const { accountContracts } = useAccountContex()

    const totalInAccount = () =>
        numeral(
            Number(accountContracts[0]?.payload.totalBetBalance) +
                Number(accountContracts[0]?.payload.totalWithdrawBalance) +
                Number(accountContracts[0]?.payload.totalMainBalance)
        ).format('$0,0.00')

    return (
        <div className="balance">
            <h2>{t('Balance1')}</h2>
            <hr />
            <div className="balance__block">
                <BalanceItem title={t('Balance2')} value={totalInAccount()} />
                <BalanceItem
                    title={t('Balance3')}
                    value={numeral(
                        accountContracts[0]?.payload.totalBetBalance
                    ).format('$0,0.00')}
                />
                <BalanceItem
                    title={t('Balance4')}
                    value={numeral(
                        accountContracts[0]?.payload.totalWithdrawBalance
                    ).format('$0,0.00')}
                />
                <BalanceItem
                    title={t('Balance5')}
                    value={numeral(
                        accountContracts[0]?.payload.totalBonusBalance
                    ).format('$0,0.00')}
                    isBonusBalance={true}
                />
                <BalanceItem
                    title={t('Balance6')}
                    value={numeral(
                        accountContracts[0]?.payload.totalFees
                    ).format('$0,0.00')}
                />
                <BalanceItem
                    title={t('Balance7')}
                    value={numeral(
                        accountContracts[0]?.payload.totalMainBalance
                    ).format('$0,0.00')}
                />
            </div>
            <div className="balance__btns">
                <Link to="/deposit" className="btn btn__green">
                    {t('Balance8')}
                </Link>
                <Link to="/withdrawal" className="btn btn__primary">
                    {t('Balance9')}
                </Link>
            </div>
        </div>
    )
}
