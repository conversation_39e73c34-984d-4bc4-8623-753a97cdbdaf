@import '../../../Styles/colors';

.balance {
    h2 {
        font-family: 'Montserrat', sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 1.125rem;
        line-height: 1.563rem;
        color: $darkGrey;
        margin-bottom: 12px;
    }
    hr {
        border-top: 1px solid rgba(130, 152, 171, 0.5);
        border-bottom: 0;
        border-left: 0;
        border-right: 0;
    }
    &__block {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 30px;
        &__item {
            //flex: 1 1;
            font-family: Montserrat, sans-serif;
            font-style: normal;
            font-weight: 500;
            font-size: 1.125rem;
            line-height: 1.563rem;
            text-align: center;
            &__value {
                font-weight: bold;
                &.green {
                    color: $successgreen;
                }
            }
            h3 {
                font-family: Montserrat, sans-serif;
                font-style: normal;
                font-weight: 500;
                font-size: 1.125rem;
                line-height: 1.563rem;
                text-align: center;
            }
            p {
                font-size: 0.938rem;
                line-height: 1.5rem;
            }
        }
    }
    &__btns {
        display: flex;
        gap: 24px;
        justify-content: flex-end;
        margin-top: 45px;
        .btn {
            font-size: 0.8rem;
            line-height: 25px;
            margin: 0;
            width: 217px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
