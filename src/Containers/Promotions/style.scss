@import '../../Styles/colors';

.promotions {
    color: $darkGrey;
    &__bigBanner {
        img {
            width: 100%;
            height: auto;
        }
    }
    h1 {
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 2.813rem;
        line-height: 50px;
        margin-top: 40px;
        margin-bottom: 30px;
    }
    &__cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(355px, 1fr));
        justify-items: center;
        gap: 20px;
        padding: 40px 0;
        &__card {
            width: 355px;
            max-width: 355px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
            border-radius: 0 0 6px 6px;
            overflow: hidden;
            color: $darkGrey;
            text-decoration: none;

            img {
                height: auto;
                width: 100%;
                max-width: 100%;
            }
            &__text {
                padding: 24px;
                h3 {
                    font-family: Montserrat, sans-serif;
                    font-style: normal;
                    font-weight: bold;
                    font-size: 1.25rem;
                    line-height: 25px;
                    margin-bottom: 9px;
                }
                p {
                    font-family: OpenSans, sans-serif;
                    font-size: 0.938rem;
                    line-height: 25px;
                }
            }
        }
    }
}

.promotion {
    &__container {
        display: grid;
        grid-template-columns: repeat(12, 1fr);
        gap: 0 5px;
    }
    &__header {
        grid-column: 1/13;
        min-height: auto;
        max-height: 250px;
        overflow: hidden;
        img {
            width: 100%;
            height: auto;
        }
    }
    &__mainarea {
        grid-column: 1/10;
        font-size: 1rem;
        padding: 16px;
        min-height: 200px;
    }

    &__card {
        &__title {
            color: $darkGrey;
            font-size: 1.5rem;
            font-weight: 700;
            padding-bottom: 16px;
        }

        &__description {
            font-size: 1rem;
            padding-bottom: 16px;
        }

        &__action {
            display: flex;
            align-items: center;
            justify-content: center;
            background: $purple;
            color: $white;
            font-size: 1rem;
            padding: 7px 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 200px;
            width: 100%;
            border-radius: 6px;
            cursor: pointer;
            transition: filter 0.2s;
            border: none;
            outline: none;

            &[disabled] {
                border: 1px solid #d3dce5;
                border-radius: 6px;
                background: #efefef;
                color: #8298ab;
                cursor: not-allowed;
            }

            &:hover {
                filter: brightness(115%);
            }
        }
    }

    &__rules {
        grid-column: 10/13;
        min-height: 200px;
        background: black;
        color: $white;
        font-size: 0.8rem;
        line-height: 1.4rem;
        padding: 16px;
    }

    @media (max-width: 1000px) {
        &__container {
            grid-template-columns: 1fr;
        }

        &__mainarea {
            grid-column: 1/13;
        }

        &__rules {
            grid-column: 1/13;
        }
    }
}
