import {
    Promotion,
    PromotionWallet
} from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { CreateEvent } from '@daml/ledger'

export const haveIUsedPromoDisplayMessageValidator = (
    promotionWalletContracts: CreateEvent<
        PromotionWallet,
        PromotionWallet.Key,
        string
    >,
    selectedPromotion: CreateEvent<Promotion, Promotion.Key, string>
) => {
    const {
        promotionId,
        config: {
            limitedPromotion,
            promoType: { tag: promoTag },
            action: { tag: actionTag }
        }
    } = selectedPromotion.payload
    const isFirstTimePromotion = promoTag === 'FirstTime'
    const promotionWalletEntriesArray =
        promotionWalletContracts?.payload?.promotionMap.entriesArray()

    if (isFirstTimePromotion) {
        const haveIUsedAnyFirstTimePromotionWithSameActionTag =
            promotionWalletEntriesArray?.filter(
                element => element[0]._4.tag === actionTag
            ).length > 0
        return haveIUsedAnyFirstTimePromotionWithSameActionTag
    }

    const haveUsedSelectedPromotion =
        promotionWalletEntriesArray?.filter(
            element =>
                element[0]._5 === promotionId &&
                element[1].usageCounter === limitedPromotion
        )?.length > 0
    return haveUsedSelectedPromotion
}

export const getPropByKeyAndLanguage = (
    promotion: any,
    lang: string,
    field: string
): string =>
    promotion?.payload?.[field]
        .entriesArray()
        .filter((t: any) => t[0] === lang)[0]?.length > 1
        ? promotion?.payload?.[field]
              .entriesArray()
              .filter((t: any) => t[0] === lang)[0][1]
        : promotion?.payload?.[field]
              .entriesArray()
              .filter((t: any) => t[0] === 'en-US')[0]?.length > 1
        ? promotion?.payload?.[field]
              .entriesArray()
              .filter((t: any) => t[0] === 'en-US')[0][1]
        : promotion?.payload?.[field]
              .entriesArray()
              .filter((t: any) => t[0] === 'es')[0]?.length > 1
        ? promotion?.payload?.[field]
              .entriesArray()
              .filter((t: any) => t[0] === 'es')[0][1]
        : promotion?.payload?.[field]
              .entriesArray()
              .filter((t: any) => t[0] === 'pt')[0]?.length > 1
        ? promotion?.payload?.[field]
              .entriesArray()
              .filter((t: any) => t[0] === 'pt')[0][1]
        : ''

export const langsConverter = { US: 'en-US', MX: 'es', BR: 'pt' } as any
