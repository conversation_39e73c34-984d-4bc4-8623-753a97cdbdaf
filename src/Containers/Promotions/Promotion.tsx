import React from 'react'
import { useParams } from 'react-router-dom'
import { Promotion as PromotionModel } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'

import { useI18LanguageContext } from 'State/LanguageState'

import useIsAuthenticated from 'Hooks/useAuth'
import useScrollToTop from 'Hooks/useScrollToTop'

import { publicContext } from 'Containers/App'
import { getPropByKeyAndLanguage, langsConverter } from './Promotion.helper'

import ButtonUnAuthUser from './PromotionButtons/ButtonUnAuthUser'
import ButtonUserLoggedInPromotion from './PromotionButtons/ButtonUserLoggedInPromotion'
import LoaderSpinner from 'Components/Loader'
import { CardImage } from '.'

import './style.scss'

type PromotionParams = {
    promotionId: string
}

export default function Promotion() {
    useScrollToTop()
    const { promotionId } = useParams<PromotionParams>()
    const { lang } = useI18LanguageContext()
    const isAuthenticated = useIsAuthenticated()

    const { contracts: promotionContracts, loading: promotionLoader } =
        publicContext.useQuery(
            PromotionModel,
            () => {
                return {
                    promotionId
                }
            },
            []
        )

    const langConverter = lang === 'BR' ? 'PT' : lang === 'MX' ? 'ES' : 'EN'

    return (
        <>
            {promotionLoader ? (
                <LoaderSpinner />
            ) : (
                <div className="promotion__container">
                    <div className="promotion__header">
                        <CardImage
                            langConverter={langConverter}
                            promotionsURL={`${getPropByKeyAndLanguage(
                                promotionContracts[0],
                                langsConverter[lang],
                                'baseUrl'
                            )}${getPropByKeyAndLanguage(
                                promotionContracts[0],
                                langsConverter[lang],
                                'bannerUrl'
                            )}`}
                            alt={
                                getPropByKeyAndLanguage(
                                    promotionContracts[0],
                                    langsConverter[lang],
                                    'shortDescription'
                                ) + ' banner'
                            }
                        />
                    </div>
                    <div className="promotion__mainarea">
                        <div className="promotion__card">
                            <div className="promotion__card__title">
                                {getPropByKeyAndLanguage(
                                    promotionContracts[0],
                                    langsConverter[lang],
                                    'title'
                                )}
                            </div>
                            <div className="promotion__card__description">
                                {getPropByKeyAndLanguage(
                                    promotionContracts[0],
                                    langsConverter[lang],
                                    'shortDescription'
                                )}
                            </div>
                            {isAuthenticated ? (
                                <ButtonUserLoggedInPromotion
                                    promotion={promotionContracts[0]}
                                />
                            ) : (
                                <ButtonUnAuthUser />
                            )}
                        </div>
                    </div>
                    <div className="promotion__rules">
                        {getPropByKeyAndLanguage(
                            promotionContracts[0],
                            langsConverter[lang],
                            'longDescription'
                        )}
                    </div>
                </div>
            )}
        </>
    )
}
