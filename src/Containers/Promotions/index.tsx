import React from 'react'
import { Switch, useRouteMatch, Route, Link } from 'react-router-dom'
import { useQuery as useQueryReactQuery } from 'react-query'

import { useI18LanguageContext } from 'State/LanguageState'
import PromotionCard from './Promotion'

import bigBanner from 'Assets/Promotions/Promotions_Banner-01.png'

import LoaderSpinner from 'Components/Loader'

import './style.scss'
import { getPropByKeyAndLanguage, langsConverter } from './Promotion.helper'

import useScrollToTop from 'Hooks/useScrollToTop'
import useActivePromotions from 'Hooks/useActivePromotions'

export function CardImage({
    promotionsURL,
    alt,
    langConverter
}: {
    promotionsURL: any
    alt: string
    langConverter: string
}) {
    const { isLoading, isError, data } = useQueryReactQuery(
        `adbanner__${alt}`,
        () => fetch(promotionsURL).then(res => res.json())
    )

    let getAltText =
        !isLoading && !isError && data?.status === 'SUCCESS'
            ? data?.placements?.placement_1?.alt_text
            : null
    let imageTranslationCode = getAltText
        ? getAltText
              ?.replaceAll('[', '')
              ?.replaceAll(']', '')
              ?.replaceAll('{', '')
              ?.replaceAll('}', '')
              ?.split(',')
              ?.filter((a: string) => a.includes(langConverter))[0]
              ?.replace(`${langConverter}:`, '')
        : null

    return !isLoading && !isError ? (
        <img
            src={
                imageTranslationCode
                    ? `https://servedbyadbutler.com/getad.img/;libID=${imageTranslationCode}`
                    : data?.placements?.placement_1?.image_url
            }
            alt={alt}
        />
    ) : null
}

function PromotionsPage() {
    useScrollToTop()
    let { url } = useRouteMatch()

    const { promotionsContracts, promotionsLoader } = useActivePromotions()

    const { lang } = useI18LanguageContext()
    const langConverter = lang === 'BR' ? 'PT' : lang === 'MX' ? 'ES' : 'EN'

    return (
        <>
            {promotionsLoader ? (
                <LoaderSpinner />
            ) : (
                <div className="promotions">
                    <div className="promotions__bigBanner">
                        <img alt="big promotion banner" src={bigBanner} />
                    </div>
                    <div className="promotions__cards">
                        {promotionsContracts.map((promotion: any) => (
                            <Link
                                to={`${url}/${promotion?.payload.promotionId}`}
                                key={promotion.contractId}
                                className="promotions__cards__card"
                            >
                                <CardImage
                                    langConverter={langConverter}
                                    promotionsURL={`${
                                        promotion &&
                                        getPropByKeyAndLanguage(
                                            promotion,
                                            langsConverter[lang],
                                            'baseUrl'
                                        )
                                    }${
                                        promotion &&
                                        getPropByKeyAndLanguage(
                                            promotion,
                                            langsConverter[lang],
                                            'thumbnailUrl'
                                        )
                                    }`}
                                    alt={
                                        promotion &&
                                        getPropByKeyAndLanguage(
                                            promotion,
                                            langsConverter[lang],
                                            'title'
                                        ) + ' thumbnail'
                                    }
                                />
                                <div className="promotions__cards__card__text">
                                    <h3>
                                        {promotion &&
                                            getPropByKeyAndLanguage(
                                                promotion,
                                                langsConverter[lang],
                                                'title'
                                            )}
                                    </h3>
                                    <p>
                                        {promotion &&
                                            getPropByKeyAndLanguage(
                                                promotion,
                                                langsConverter[lang],
                                                'shortDescription'
                                            ).slice(0, 50)}{' '}
                                        ...
                                    </p>
                                </div>
                            </Link>
                        ))}
                    </div>
                </div>
            )}
        </>
    )
}

export default function Promotions() {
    let { path } = useRouteMatch()

    return (
        <Switch>
            <Route exact path={path}>
                <PromotionsPage />
            </Route>
            <Route path={`${path}/:promotionId`}>
                <PromotionCard />
            </Route>
        </Switch>
    )
}
