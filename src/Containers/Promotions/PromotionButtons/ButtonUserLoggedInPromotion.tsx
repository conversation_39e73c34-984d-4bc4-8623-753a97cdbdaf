import React from 'react'
import { useHistory } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
    Promotion,
    PromotionWallet
} from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { CreateEvent } from '@daml/ledger'

import { useSelectedPromotionContext } from 'State/SelectPromotion'
import { AppContext } from 'State/AppProvider'
import { showBets, useBetsDispatch } from 'State/BetsContext'

import useMediaQuery from 'Hooks/useMediaQuery'
import { useStreamQueries } from '@daml/react'
import { haveIUsedPromoDisplayMessageValidator } from '../Promotion.helper'

const ButtonUserLoggedInPromotion = ({
    promotion
}: {
    promotion: CreateEvent<Promotion, Promotion.Key, string>
}) => {
    const { t } = useTranslation()
    const displayHamburguer = useMediaQuery('(max-width: 930px)')
    const [, setSelectedPromotion] = useSelectedPromotionContext()
    const { setShowSideMenu } = React.useContext(AppContext)
    const betsDispatch = useBetsDispatch()
    const { push } = useHistory()

    const {
        contracts: promotionWalletContracts,
        loading: isPromotionWalletLoading
    } = useStreamQueries(PromotionWallet)

    let endDate = promotion?.payload?.config.endDate
    let startDate = promotion?.payload?.startDate
    const today = new Date().getTime()
    const isBeforeEndDate = endDate
        ? new Date(endDate).getTime() >= today
        : true
    const isAfterStartDate = startDate
        ? new Date(startDate).getTime() <= today
        : true
    const isValidDate = isAfterStartDate && isBeforeEndDate

    const isPromotionAlreadyUsed = haveIUsedPromoDisplayMessageValidator(
        promotionWalletContracts[0],
        promotion
    )

    const handleClick = async (
        promotion: CreateEvent<Promotion, Promotion.Key, string>
    ) => {
        setSelectedPromotion(promotion)
        const {
            config: {
                action: { tag: actionTag },
                promoType: { tag: promoTag }
            }
        } = promotion.payload
        if (actionTag === 'Deposit' && promoTag === 'FirstTime') {
            return push('/first_deposit')
        }
        if (actionTag === 'Deposit') {
            return push('/deposit')
        }
        if (actionTag === 'Withdrawal') {
            return push('/withdrawal')
        }
        if (actionTag === 'Bet') {
            push('/')
            if (displayHamburguer) {
                setShowSideMenu && setShowSideMenu(false)
            }
            return await showBets(betsDispatch)
        }
    }
    return (
        <>
            <button
                className="promotion__card__action"
                disabled={
                    !isValidDate ||
                    isPromotionWalletLoading ||
                    isPromotionAlreadyUsed
                }
                onClick={async () => await handleClick(promotion)}
            >
                {t('BApplyNow')}
            </button>
            {!isValidDate ? (
                <span>
                    <p style={{ fontSize: '10px' }}>
                        This promotion is outside of the time range{' '}
                    </p>
                </span>
            ) : null}
            {isPromotionAlreadyUsed ? (
                <span>
                    <p style={{ fontSize: '10px' }}>
                        {promotion.payload.config.promoType.tag === 'FirstTime'
                            ? "You’ve already used this promotion or another 'First-Time' offer"
                            : 'You’ve already used this promotion'}
                    </p>
                </span>
            ) : null}
        </>
    )
}

export default ButtonUserLoggedInPromotion
