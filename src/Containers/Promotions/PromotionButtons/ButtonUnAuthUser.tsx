import React from 'react'
import useLoginRedirection from 'Hooks/useLoginRedirection'
import { useTranslation } from 'react-i18next'

const ButtonUnAuthUser = () => {
    const login = useLoginRedirection()
    const { t } = useTranslation()
    return (
        <button className="promotion__card__action" onClick={() => login()}>
            {t('SingInBtn')}
        </button>
    )
}

export default ButtonUnAuthUser
