@import '../../Styles/colors';

.adminPage {
    color: $darkGrey;
    @media (max-width: 450px) {
        padding: 40px 5px;
    }

    &__card {
        border-radius: 6px;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
        padding: 25px;

        @media (max-width: 450px) {
            padding: 25px 10px;
        }

        h1 {
            font-family: 'Montserrat-Bold', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 2.188rem;
            line-height: 2.75rem;
        }
    }
}

.adminTable__wrapper {
    display: inline-block;
    overflow-x: auto;
    width: 100%;

    table {
        display: table;
        width: 100%;
        td,
        th {
            padding: 15px;
        }

        th {
            text-align: left;
            border-bottom: 1px solid rgba(130, 152, 171, 0.2);
            border-top: 1px solid rgba(130, 152, 171, 0.2);
            text-transform: uppercase;
            padding-top: 1rem;
            padding-bottom: 1rem;
            font-family: 'OpenSans', sans-serif;
            font-size: 1.063rem;
            line-height: 1.563rem;
        }

        td {
            white-space: nowrap;
            text-align: left;
            font-family: 'OpenSans', sans-serif;
            font-size: 0.938rem;
            line-height: 1.25rem;
            border-bottom: none;
            color: rgb(20, 20, 20);
        }

        .success {
            color: $tableSuccessGreen;
        }

        .fail {
            color: $tableFailRed;
        }

        .smaller-font {
            font-size: 0.7rem;
        }
    }
}

.btn__admin {
    padding: 4px 8px;
    font-size: 0.8rem;
    line-height: 25px;
    margin: 0;
    max-width: 217px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $white;
    cursor: pointer;
    text-decoration: none;
    background-color: $purple;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    font-family: 'Montserrat-Bold', sans-serif;
}

.attentionMessage {
    font-weight: 300;
    font-size: 12px;
}

.promotionInputContainerMargin {
    margin: 10px 0;
}
