import React from 'react'
import LoaderSpinner from 'Components/Loader'
import { useNavigate } from 'react-router-dom'
import useNoListingContracts from 'Hooks/useNoListingContracts'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable,
    ColumnFiltersState
} from '@tanstack/react-table'

import { useSportTranslationsContext } from 'State/SportTranslationsContext'

import Table from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import FilterContainer from 'Components/Table/Filter'
import FilterInput from 'Components/Table/Filter/Input'

import getFormattedDate from 'Containers/Dashboard/getFormattedDate'
import { getTextByLang } from 'Components/Event/EventCard/SportTranslations.helper'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import { getSearchState, handleSearch } from 'Components/Table/Filter/utils'
import { generateMarketNameForAdminTables } from '../utils/generateMarketNameForAdminTables'

interface ITableData {
    eventStartDate: string
    eventTitle: string
    market: any
    tournament: string
    geography: string
    action: string
}

const colFiltersInitState = [
    { id: 'eventTitle', value: '' },
    { id: 'market', value: '' },
    { id: 'geography', value: '' },
    { id: 'tournament', value: '' }
]

const EditEvents = () => {
    const { push } = useNavigate()
    const { contracts, isLoading } = useNoListingContracts()

    const preparedContracts = contracts.filter(
        contract => contract.payload.details.origin === 'Customer'
    )

    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>(colFiltersInitState)
    const { sportTranslationsContracts, loadingSportTranslations } =
        useSportTranslationsContext()
    const US = React.useMemo(
        () =>
            sportTranslationsContracts.length > 0 &&
            sportTranslationsContracts[0]?.payload?.sportsMap
                ? getTextByLang(
                      'en_uk',
                      sportTranslationsContracts[0]?.payload?.sportsMap
                  )
                : [],
        [sportTranslationsContracts]
    )

    const columnHelper = createColumnHelper<ITableData>()
    const columns = React.useMemo(
        () => [
            columnHelper.accessor('eventStartDate', {
                header: 'Start Date',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('eventTitle', {
                header: 'Event Title',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('market', {
                header: 'Market',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('geography', {
                header: 'Geography',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('tournament', {
                header: 'Tournament',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('action', {
                header: '',
                cell: info => {
                    return (
                        <button
                            className="btn__admin"
                            onClick={() =>
                                navigate(`/admin-update-event/${info.getValue()}`)
                            }
                        >Edit
                                                    </button>
                    );
                }
            })
        ],
        [columnHelper, push]
    )

    let data = React.useMemo(
        () =>
            preparedContracts.map(contract => ({
                eventStartDate: getFormattedDate(
                    contract.payload.details.startDate
                ),
                eventTitle: getEventTitleFromLang(
                    contract.payload.details.eventTitle,
                    'US'
                ),
                market: generateMarketNameForAdminTables({
                    market: contract.payload.details.market,
                    usLang: US
                }),
                tournament:
                    contract.payload.details.submarkets.find(
                        data => data.tag === 'Tournament'
                    )?.value ?? '-',
                geography: contract.payload.details.geography.value,
                action: contract.payload.eventId.label
            })),
        [US, preparedContracts]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: false,
        state: {
            columnFilters
        },
        onColumnFiltersChange: setColumnFilters
    })

    if (isLoading || loadingSportTranslations) {
        return <LoaderSpinner />
    }

    if (!preparedContracts.length) {
        return <p>There are no contracts available to edit</p>
    }

    return (
        <>
            <FilterContainer>
                {React.Children.toArray(
                    colFiltersInitState.map(col => {
                        const title = descriptCamelCase(col.id)
                        return (
                            <FilterInput
                                label={title.toLocaleUpperCase()}
                                type="text"
                                placeholder={`Filter by ${title.toLocaleLowerCase()}`}
                                onChange={e =>
                                    handleSearch(table, e.target.value, col.id)
                                }
                                value={
                                    (getSearchState(table, col.id) as string) ??
                                    ''
                                }
                            />
                        )
                    })
                )}
            </FilterContainer>
            <Table
                tableHeader={table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                            <th key={header.id}>
                                {header.column.columnDef.header}
                            </th>
                        ))}
                    </tr>
                ))}
                tableBodyRow={table.getRowModel().rows.map(row => (
                    <tr key={row.id}>
                        {row.getVisibleCells().map(cell => (
                            <td key={cell.id}>
                                {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                )}
                            </td>
                        ))}
                    </tr>
                ))}
            />
            <Pagination table={table} />
        </>
    )
}

export default EditEvents
