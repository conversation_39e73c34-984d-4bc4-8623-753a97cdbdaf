import React from 'react'
import { toast } from 'react-toastify'
import { Formik } from 'formik'
import { useNavigate, useParams } from 'react-router-dom'
import { Optional } from '@daml/types'
import { useLedger } from '@daml/react'

import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import {
    EventInstrument,
    InputOutcomeOdd,
    Market
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { OutcomeSubType } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'

import useIsAllowed from 'Hooks/useIsAdminAccount'
import { publicContext } from 'Containers/App'

import LoaderSpinner from 'Components/Loader'
import FormEdit from 'Components/Admin/Form/Form'
import GoBackNavigation from 'Components/GoBackNavigation'
import ConfirmModal from './ConfirmModal'
import { getYupSchemaValidation, handleDateTimeToUserLocalTime } from './utils'
import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'
import { getOddMinMaxValues } from 'Components/MyBets/utils'
import moment from 'moment'
import { MapGenerator } from 'Containers/Admin/CreatePromotion/utils'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'

export type TTag =
    | 'ThreeWay'
    | 'TwoWay'
    | 'Winner'
    | 'Default'
    | 'OverUnder'
    | 'ThreeWayHandicap'

export interface IOutcomes {
    odds: string
    participantId: Optional<string>
    participantOrder: string
    tag: TTag
    valueOfTag: string | {}
    subtype: OutcomeSubType
    order: string
}

export interface IParticipants {
    name: string
    id: string
    order: string
    co_op: string
}

export type TEditFormData = {
    startDate: string
    englishTitle: string
    spanishTitle: string
    portugueseTitle: string
    market: string
    sportMarketName: string
    eventParticipants: IParticipants[]
    outcomes: IOutcomes[]
    tournamentTitle: string
    geography: string
    description: string
}

const EditEventPage = () => {
    const ledger = useLedger()
    const [isModalOpen, setIsOpenModal] = React.useState(false)
    const { assetLabel } = useParams<{ assetLabel: string }>()
    const { push } = useNavigate()
    const { isAdmin, loading, EventManagerServiceContract } = useIsAllowed()
    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader
    } = useGlobalGamblingConfigContext()

    const { contracts, loading: loadingContract } = publicContext.useQuery(
        EventInstrument,
        () => {
            return {
                eventId: { label: assetLabel }
            }
        },
        [assetLabel]
    )

    React.useEffect(() => {
        if (!loading && !isAdmin) {
            return navigate('/');
        }
        return () => {}
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAdmin, loading])

    if (loadingContract || loading || GlobalGamblingConfigurationLoader) {
        return <LoaderSpinner />
    }

    if (!contracts.length) {
        return <p>No Events for the given id</p>
    }

    const minValue = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'minOdd'
    )
    const maxValue = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'maxOdd'
    )

    const constructedOutcomes = contracts[0].payload.details.outcomes
        .sort((a, b) => Number(a.outcome.order) - Number(b.outcome.order))
        .map(
            ({
                outcome: {
                    participantId,
                    participantOrder,
                    subtype,
                    order,
                    type_: { tag, value }
                },
                odds
            }) => ({
                participantId,
                participantOrder,
                subtype,
                order,
                tag,
                valueOfTag: value,
                odds: odds.map.entriesArray()[0][0].value
            })
        )

    const INIT_VALUES: TEditFormData = {
        startDate:
            handleDateTimeToUserLocalTime(
                contracts[0].payload.details.startDate
            ) ?? '',
        market: contracts[0].payload.details.market.tag,
        sportMarketName:
            typeof contracts[0].payload.details.market.value === 'string'
                ? contracts[0].payload.details.market.value
                : '',
        description: 'description',
        englishTitle:
            getEventTitleFromLang(
                contracts[0].payload.details.eventTitle,
                'en-US'
            ) ?? '',
        spanishTitle:
            getEventTitleFromLang(
                contracts[0].payload.details.eventTitle,
                'MX'
            ) ?? '',
        portugueseTitle:
            getEventTitleFromLang(
                contracts[0].payload.details.eventTitle,
                'BR'
            ) ?? '',
        tournamentTitle: contracts[0].payload.details.submarkets[0].value,
        geography: contracts[0].payload.details.geography.value,
        eventParticipants: contracts[0].payload.details.eventParticipants.map(
            par => ({ ...par, co_op: par.co_op === null ? '' : par.co_op[0] })
        ),
        outcomes: constructedOutcomes
    }
    return (
        <section className="content__container adminPage pagePadding">
            <div className="adminPage__card">
                <GoBackNavigation
                    to="/admin/edit-events"
                    children={'Back to admin'}
                />
                <Formik
                    initialValues={INIT_VALUES}
                    onSubmit={values => {
                        const {
                            eventParticipants,
                            englishTitle,
                            spanishTitle,
                            portugueseTitle,
                            market,
                            outcomes,
                            sportMarketName,
                            startDate
                        } = values
                        const prepareedParticipants = eventParticipants.map(
                            participant => ({
                                ...participant,
                                co_op:
                                    participant.co_op.length > 0
                                        ? [participant.co_op]
                                        : null
                            })
                        )
                        const preparedStartDate =
                            moment(startDate).toISOString()
                        const preparedMapForTitles = MapGenerator(
                            englishTitle,
                            spanishTitle,
                            portugueseTitle
                        )
                        const marketToPass = {
                            tag: market,
                            value:
                                sportMarketName.length > 0
                                    ? sportMarketName
                                    : {}
                        } as unknown as Market
                        const outcomesToPass = outcomes.map(
                            ({
                                odds,
                                order,
                                participantId,
                                participantOrder,
                                subtype,
                                tag,
                                valueOfTag
                            }) => ({
                                outcome: {
                                    order,
                                    participantId:
                                        tag === 'OverUnder'
                                            ? null
                                            : participantId,
                                    participantOrder,
                                    subtype,
                                    type_: {
                                        tag,
                                        value: valueOfTag
                                    }
                                },
                                odd: {
                                    tag: 'Decimal',
                                    value: odds
                                }
                            })
                        ) as InputOutcomeOdd[]
                        ledger
                            .exercise(
                                EventManagerService.RequestEventUpdate,
                                EventManagerServiceContract[0].contractId,
                                {
                                    eventOrigin: {
                                        tag: 'CustomerUpdate',
                                        value: {
                                            eventParticipants:
                                                prepareedParticipants,
                                            //GENERATE MAP TITLE
                                            eventTitle: preparedMapForTitles,
                                            market: marketToPass,
                                            outcomes: outcomesToPass,
                                            submarkets:
                                                contracts[0].payload.details
                                                    .submarkets,
                                            startDate: preparedStartDate,
                                            eventResults:
                                                contracts[0].payload.details
                                                    .eventResults,
                                            eventStatus:
                                                contracts[0].payload.details
                                                    .eventStatus,
                                            assetLabel:
                                                contracts[0].payload.eventId
                                                    .label
                                        }
                                    },
                                    oldEventKey: contracts[0].key
                                }
                            )
                            .then(() => toast.success('Request Event Update'))
                            .catch(error => {
                                console.error(error)
                                toast.error(
                                    'Something went wrong please try again later'
                                )
                            })
                            .finally(() => navigate('/admin/edit-events'))
                    }}
                    validationSchema={getYupSchemaValidation(
                        minValue,
                        maxValue
                    )}
                >
                    <>
                        <FormEdit handleEdit={() => setIsOpenModal(true)} />
                        <ConfirmModal
                            isOpen={isModalOpen}
                            handleClose={() => setIsOpenModal(false)}
                        />
                    </>
                </Formik>
            </div>
        </section>
    );
}

export default EditEventPage
