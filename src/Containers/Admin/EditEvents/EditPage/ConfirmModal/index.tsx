import React from 'react'

import BasicModal from 'Components/Modals/BasicModal'
import { useFormikContext } from 'formik'


const ConfirmModal = ({ isOpen, handleClose }: { isOpen: boolean, handleClose: () => void }) => {
    const { submitForm } = useFormikContext()

    return (
        <BasicModal
            isOpenModal={isOpen}
            body={<p>Are you sure you want to update this event?</p>}
            footerBody={
                <>
                    <button className="btn btn__primary" onClick={submitForm}>
                        Yes
                    </button>
                    <button className="btn btn__grey" onClick={handleClose}>
                        No
                    </button>
                </>
            }
            handleClose={handleClose} />
    )
}

export default ConfirmModal