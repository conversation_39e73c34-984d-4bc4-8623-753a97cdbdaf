import * as Yup from 'yup'
import moment from 'moment-timezone'
import { regexDecimal } from 'Components/Admin/Form/constants'

export const handleDateTimeToUserLocalTime = (date: string) => {
    const utcDate = new Date(date)
    const year = utcDate.getFullYear()
    const month = String(utcDate.getMonth() + 1).padStart(2, '0')
    const day = String(utcDate.getDate()).padStart(2, '0')
    const hours = String(utcDate.getHours()).padStart(2, '0')
    const minutes = String(utcDate.getMinutes()).padStart(2, '0')
    const seconds = String(utcDate.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`
}

export const getYupSchemaValidation = (
    minOddValue: string,
    maxOddValue: string
) => {
    return Yup.object({
        englishTitle: Yup.string()
            .min(1)
            .required('Event english title is a required field'),
        spanishTitle: Yup.string()
            .min(1)
            .required('Event spanish title is a required field'),
        portugueseTitle: Yup.string()
            .min(1)
            .required('Event portuguese title is a required field'),
        description: Yup.string()
            .min(1)
            .required('Description is a required  field'),
        tournamentTitle: Yup.string()
            .min(1)
            .required('Tournament title is a required  field'),
        geography: Yup.string()
            .min(1)
            .required('Geography is a required  field'),
        market: Yup.string().min(1).required('Market is a required  field'),
        sportMarketName: Yup.string().when('market', {
            is: (market: string) => {
                return market === 'Sport'
            },
            then: Yup.string()
                .min(1)
                .required('Sport Name is a required  field')
        }),
        startDate: Yup.date()
            .test('startDate', 'It should be an hour later', function (val) {
                return moment(val).isAfter(moment().add(1, 'hours'))
            })
            .required('Start date is a required  field'),
        eventParticipants: Yup.array()
            .of(
                Yup.object().shape({
                    name: Yup.string()
                        .min(1)
                        .required('name is a required  field'),
                    id: Yup.string().min(1).required('id is a required  field'),
                    order: Yup.string()
                        .min(1)
                        .required('order is a required  field'),
                    co_op: Yup.string().nullable()
                })
            )
            .min(2, 'You need to have at least 2 participants'),
        outcomes: Yup.array()
            .of(
                Yup.object().shape({
                    participantId: Yup.string().nullable(),
                    participantOrder: Yup.string(),
                    tag: Yup.string(),
                    valueOfTag: Yup.mixed().test(
                        'is-string-or-object',
                        'Must be a string or an empty object',
                        value => {
                            return (
                                typeof value === 'string' ||
                                (typeof value === 'object' &&
                                    Object.keys(value).length === 0)
                            )
                        }
                    ),
                    subtype: Yup.string(),
                    order: Yup.string(),
                    odds: Yup.string()
                        .matches(regexDecimal, 'Please add a valid Odd')
                        .test(
                            val =>
                                Number(val) >= Number(minOddValue) &&
                                Number(val) <= Number(maxOddValue)
                        )
                        .required()
                })
            )
            .min(
                2,
                'You need to have at least 2 odds, odd value and value of tag must be a decimal number (ex: 2.55).'
            )
    })
}
