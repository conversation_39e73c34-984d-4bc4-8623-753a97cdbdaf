import React from 'react'
import { Field, FieldProps, useFormikContext } from 'formik'
import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import { IFormRecreatePromotion } from './RecreatePromoModal'

const ModalFormBody = () => {
    const { errors } = useFormikContext<IFormRecreatePromotion>()
    return (
        <>
            <p>Are you sure you want to recreate this promotion?</p>
            <label htmlFor="startDate">Start Date:</label>
            <Field name="startDate">
                {({ field }: FieldProps) => (
                    <input
                        type="datetime-local"
                        className="register__input"
                        min={new Date().toISOString().slice(0, -8)}
                        step="1"
                        {...field}
                    />
                )}
            </Field>
            {errors?.startDate ? (
                <ErrorMessage message={errors?.startDate} />
            ) : null}
        </>
    )
}

export default ModalFormBody
