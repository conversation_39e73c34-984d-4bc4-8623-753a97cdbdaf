import { useFormikContext } from 'formik'
import React from 'react'
import { IFormRecreatePromotion } from './RecreatePromoModal'

const ModalFooterBody = ({ handleClose }: { handleClose: () => void }) => {
    const { submitForm, errors, validateForm } =
        useFormikContext<IFormRecreatePromotion>()

    const validateCallBack = React.useCallback(() => {
        validateForm()
    }, [validateForm])

    React.useEffect(() => {
        validateCallBack()
    }, [validateCallBack])

    return (
        <>
            <button
                className="btn btn__primary"
                disabled={errors?.startDate ? true : false}
                onClick={submitForm}
            >
                Yes
            </button>
            <button className="btn btn__grey" onClick={handleClose}>
                No
            </button>
        </>
    )
}

export default ModalFooterBody
