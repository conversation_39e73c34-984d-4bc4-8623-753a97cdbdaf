import Ledger from '@daml/ledger'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import { toast } from 'react-toastify'
import * as Yup from 'yup'
import moment from 'moment-timezone'

interface IRecreatePromotionFunction {
    promotion: Promotion
    ledger: Ledger
}

export const recreatePromotionFunction = async ({
    promotion,
    ledger
}: IRecreatePromotionFunction) => {
    try {
        const marketingManagerService = await ledger.query(
            MarketingManagerService
        )
        if (!marketingManagerService.length) {
            throw new Error('The user must have a marketing manager service')
        }
        await ledger.exercise(
            MarketingManagerService.CreatePromotionRequest,
            marketingManagerService[0].contractId,
            promotion
        )
        return toast.success('Promotion recreation sucessfuly requested')
    } catch (error) {
        console.log(error)
        return toast.error('Something went wrong')
    }
}

export const validationSchemaRecreatePromotion = Yup.object({
    startDate: Yup.string()
        .test(
            'startDate',
            'Start date should be 10 minutes later than now',
            function (val) {
                console.log(val)
                return moment(val).isAfter(moment().add(10, 'minutes'))
            }
        )
        .required('Start date is a required  field')
})
