import React from 'react'
import moment from 'moment-timezone'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { useLedger } from '@daml/react'
import {
    recreatePromotionFunction,
    validationSchemaRecreatePromotion
} from './utils'
import BasicModal from 'Components/Modals/BasicModal'
import { Formik } from 'formik'
import ModalReinstatePromotionBody from './ModalReinstatePromotionBody'
import ModalReinstatePromotionFooter from './ModalReinstatePromotionFooter'
import LoaderSpinner from 'Components/Loader'

import './style.scss'

interface IRecreatePromotionModal {
    handleClose: () => void
    promotion: Promotion
    openModal: boolean
}

export interface IFormRecreatePromotion {
    startDate: string
}

const RecreatePromotionModal = ({
    promotion,
    openModal,
    handleClose
}: IRecreatePromotionModal) => {
    const ledger = useLedger()
    const [loading, setLoading] = React.useState(false)
    const handlePromoUpdate = async (startDate: string) => {
        setLoading(true)
        const { config } = promotion
        let newConfig = { ...config, endDate: null }
        let newStartDate = moment(startDate).toISOString()
        let promotionBody = {
            ...promotion,
            promotionId: Math.floor(Math.random() * 10000).toString(),
            config: newConfig,
            status: 'Active',
            startDate: newStartDate
        } as Promotion
        await recreatePromotionFunction({
            promotion: promotionBody,
            ledger
        })
        handleClose()
    }

    return (
        <Formik
            initialValues={{
                startDate: ''
            }}
            onSubmit={async ({ startDate }, { resetForm }) => {
                handlePromoUpdate(startDate)
                resetForm()
            }}
            validationSchema={validationSchemaRecreatePromotion}
        >
            <BasicModal
                body={
                    loading ? (
                        <div className="spinnerContainerForModalPromotions">
                            <LoaderSpinner />
                        </div>
                    ) : (
                        <ModalReinstatePromotionBody />
                    )
                }
                footerBody={
                    loading ? null : (
                        <ModalReinstatePromotionFooter
                            handleClose={handleClose}
                        />
                    )
                }
                isOpenModal={openModal}
                handleClose={handleClose}
            />
        </Formik>
    )
}

export default RecreatePromotionModal
