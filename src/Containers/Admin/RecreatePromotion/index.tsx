import React from 'react'
import {
    Promotion,
    Status
} from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { useStreamQueries } from '@daml/react'
import LoaderSpinner from 'Components/Loader'
import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import Table from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'

import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import RecreatePromotionModal from './RecreatePromoModal'

interface IModalData {
    promotion: null | Promotion
    openModal: boolean
}

interface ITableData {
    promotionTitle: string
    promotion: Promotion
    expireDate: null | string
}

const RecreatePromotion = () => {
    const [modalData, setModalData] = React.useState<IModalData>({
        promotion: null,
        openModal: false
    })
    const { contracts, loading } = useStreamQueries(
        Promotion,
        () => [{ status: 'Expired' as Status }],
        []
    )

    const openModal = (promotion: Promotion) => {
        setModalData({
            promotion,
            openModal: true
        })
    }

    const closeModal = () => {
        setModalData({
            promotion: null,
            openModal: false
        })
    }

    const columnHelper = createColumnHelper<ITableData>()
    const columns = React.useMemo(
        () => [
            columnHelper.accessor('promotionTitle', {
                header: 'Promotion Title',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('expireDate', {
                header: 'Expire Date',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('promotion', {
                header: '',
                cell: info => {
                    return (
                        <button
                            className="btn__admin"
                            onClick={() => openModal(info.getValue())}
                        >
                            Recreate
                        </button>
                    )
                }
            })
        ],
        [columnHelper]
    )

    let data = React.useMemo(
        () =>
            contracts.map(data => ({
                promotionTitle: getEventTitleFromLang(
                    data.payload.title,
                    'en-US'
                ),
                promotion: data.payload,
                expireDate: data.payload.config.endDate ?? '-'
            })),
        [contracts]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: false
    })

    if (loading) {
        return <LoaderSpinner />
    }

    if (!contracts.length) {
        return (
            <div>
                <p>There are no expired promotions</p>
            </div>
        )
    }

    return (
        <>
            {modalData.openModal && modalData.promotion !== null ? (
                <RecreatePromotionModal
                    handleClose={closeModal}
                    openModal={modalData.openModal}
                    promotion={modalData.promotion}
                />
            ) : null}
            <Table
                tableHeader={table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                            <th key={header.id}>
                                {header.column.columnDef.header}
                            </th>
                        ))}
                    </tr>
                ))}
                tableBodyRow={table.getRowModel().rows.map(row => (
                    <tr key={row.id}>
                        {row.getVisibleCells().map(cell => (
                            <td key={cell.id}>
                                {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                )}
                            </td>
                        ))}
                    </tr>
                ))}
            />
            <Pagination table={table} />
        </>
    )
}

export default RecreatePromotion
