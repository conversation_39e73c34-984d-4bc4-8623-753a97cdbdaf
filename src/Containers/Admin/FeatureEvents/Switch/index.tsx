import React from 'react'
import Switch from 'react-switch'

const SwitchFeatured = ({
    isChecked,
    handleChange
}: {
    isChecked: boolean
    handleChange: () => Promise<string | number>
}) => {
    const [optimisticChecked, setOptmisticCheck] = React.useState(isChecked)

    const handleOptimisticChange = () => {
        handleChange().then(() => {
            isChecked ? setOptmisticCheck(true) : setOptmisticCheck(false)
        })
    }

    return (
        <Routes
            checked={optimisticChecked}
            onChange={handleOptimisticChange}
            onColor="#a000e1"
        />
    );
}

export default SwitchFeatured
