import React from 'react'
import { publicContext } from 'Containers/App'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { useLedger } from '@daml/react'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import { CreateEvent } from '@daml/ledger'

import SwitchFeatured from './Switch'
import LoaderSpinner from 'Components/Loader'
import Table from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import FilterContainer from 'Components/Table/Filter'
import FilterInput from 'Components/Table/Filter/Input'
import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable,
    ColumnFiltersState
} from '@tanstack/react-table'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import getFormattedDate from 'Containers/Dashboard/getFormattedDate'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import { getTextByLang } from 'Components/Event/EventCard/SportTranslations.helper'
import { featureEventRequest, colFiltersInitState, ITableData } from './utils'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import { getSearchState, handleSearch } from 'Components/Table/Filter/utils'
import { generateMarketNameForAdminTables } from '../utils/generateMarketNameForAdminTables'

type ManagerServiceContract =
    | CreateEvent<EventManagerService, EventManagerService.Key, string>[]
    | CreateEvent<
          MarketingManagerService,
          MarketingManagerService.Key,
          string
      >[]

const FeatureEvents = ({
    ManagerServiceContract,
    isEventManager
}: {
    ManagerServiceContract: ManagerServiceContract
    isEventManager: boolean
}) => {
    const ledger = useLedger()
    const { loading, contracts: eventInstrumentContracts } =
        publicContext.useStreamQueries(
            EventInstrument,
            () => [
                {
                    status: 'Active',
                    details: {
                        eventStatus: 'NotStarted'
                    }
                } as EventInstrument
            ],
            []
        )
    const { sportTranslationsContracts, loadingSportTranslations } =
        useSportTranslationsContext()

    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>(colFiltersInitState)
    const columnHelper = createColumnHelper<ITableData>()

    const isPageLoading = loadingSportTranslations || loading

    const US = React.useMemo(
        () =>
            sportTranslationsContracts.length > 0 &&
            sportTranslationsContracts[0]?.payload?.sportsMap
                ? getTextByLang(
                      'en_uk',
                      sportTranslationsContracts[0]?.payload?.sportsMap
                  )
                : [],
        [sportTranslationsContracts]
    )

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('eventStartDate', {
                header: 'Start Date',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('eventTitle', {
                header: 'Event Title',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('market', {
                header: 'Market',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('geography', {
                header: 'Geography',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('tournament', {
                header: 'Tournament',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('featured', {
                header: 'Featured',
                cell: info => {
                    return (
                        <>
                            <SwitchFeatured
                                isChecked={info.getValue().isFeatured}
                                handleChange={async () =>
                                    await featureEventRequest({
                                        ledger,
                                        serviceEventManager:
                                            ManagerServiceContract,
                                        eventLabel: info.getValue().eventLabel,
                                        isEventFeatured:
                                            info.getValue().isFeatured,
                                        isEventManager
                                    })
                                }
                            />
                        </>
                    )
                }
            })
        ],
        [ManagerServiceContract, columnHelper, ledger, isEventManager]
    )

    let data = React.useMemo(
        () =>
            eventInstrumentContracts.map(contract => ({
                eventStartDate: getFormattedDate(
                    contract.payload.details.startDate
                ),
                eventTitle: getEventTitleFromLang(
                    contract.payload.details.eventTitle,
                    'US'
                ),
                market: generateMarketNameForAdminTables({
                    market: contract.payload.details.market,
                    usLang: US
                }),
                tournament:
                    contract.payload.details.submarkets.find(
                        data => data.tag === 'Tournament'
                    )?.value ?? '-',
                geography: contract.payload.details.geography.value,
                featured: {
                    eventLabel: contract.payload.eventId.label,
                    isFeatured: contract.payload.featured
                }
            })),
        [US, eventInstrumentContracts]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: false,
        state: {
            columnFilters
        },
        onColumnFiltersChange: setColumnFilters
    })

    if (isPageLoading) {
        return <LoaderSpinner />
    }

    return (
        <>
            <FilterContainer>
                {React.Children.toArray(
                    colFiltersInitState.map(col => {
                        const title = descriptCamelCase(col.id)
                        return (
                            <FilterInput
                                label={title.toLocaleUpperCase()}
                                type="text"
                                placeholder={`Filter by ${title.toLocaleLowerCase()}`}
                                onChange={e =>
                                    handleSearch(table, e.target.value, col.id)
                                }
                                value={
                                    (getSearchState(table, col.id) as string) ??
                                    ''
                                }
                            />
                        )
                    })
                )}
            </FilterContainer>
            <Table
                tableHeader={table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                            <th key={header.id}>
                                {header.column.columnDef.header}
                            </th>
                        ))}
                    </tr>
                ))}
                tableBodyRow={table.getRowModel().rows.map(row => (
                    <tr key={row.id}>
                        {row.getVisibleCells().map(cell => (
                            <td key={cell.id}>
                                {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                )}
                            </td>
                        ))}
                    </tr>
                ))}
            />
            <Pagination table={table} />
        </>
    )
}

export default FeatureEvents
