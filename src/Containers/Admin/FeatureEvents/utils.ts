import Ledger, { CreateEvent } from '@daml/ledger'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import { toast } from 'react-toastify'

interface IFeatureEventRequest {
    ledger: Ledger
    serviceEventManager:
        | CreateEvent<EventManagerService, EventManagerService.Key, string>[]
        | CreateEvent<
              MarketingManagerService,
              MarketingManagerService.Key,
              string
          >[]
    eventLabel: string
    isEventFeatured: boolean
    isEventManager: boolean
}

export interface ITableData {
    eventStartDate: string
    eventTitle: string
    market: string
    tournament: string
    geography: string
    featured: { eventLabel: string; isFeatured: boolean }
}

export const colFiltersInitState = [
    { id: 'eventTitle', value: '' },
    { id: 'market', value: '' },
    { id: 'geography', value: '' },
    { id: 'tournament', value: '' }
]

export const featureEventRequest = ({
    ledger,
    serviceEventManager,
    eventLabel,
    isEventFeatured,
    isEventManager
}: IFeatureEventRequest) => {
    let requestToggleFeaturedEvent = isEventManager
        ? EventManagerService.RequestToggleFeaturedEvent
        : MarketingManagerService.RequestToggleFeaturedEvent
    return ledger
        .exercise(
            requestToggleFeaturedEvent,
            serviceEventManager[0].contractId,
            { label: eventLabel }
        )
        .then(() =>
            toast.success(
                isEventFeatured
                    ? 'Event removed from featured successfully.'
                    : 'Event featured successfully.'
            )
        )
        .catch(() =>
            toast.error('Something went wrong, please try again later.')
        )
}
