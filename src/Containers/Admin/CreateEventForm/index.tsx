import React from 'react'
import { Formik } from 'formik'
import { TEditFormData } from '../EditEvents/EditPage'
import FormCreateEvent from 'Components/Admin/Form/Form'
import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'
import { getOddMinMaxValues } from 'Components/MyBets/utils'
import { getYupSchemaValidation } from '../EditEvents/EditPage/utils'
import LoaderSpinner from 'Components/Loader'
import { useLedger } from '@daml/react'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import { CreateEvent } from '@daml/ledger'
import { generateAssetLabel } from 'Components/Admin/Form/utils'
import {
    Market,
    InputOutcomeOdd
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import moment from 'moment'
import { toast } from 'react-toastify'
import ConfirmEventPage from './ConfirmEvent'
import useScrollToTop from 'Hooks/useScrollToTop'
import { MapGenerator } from '../CreatePromotion/utils'

const INIT_VALUES: TEditFormData = {
    startDate: '',
    market: '',
    sportMarketName: '',
    description: '',
    englishTitle: '',
    spanishTitle: '',
    portugueseTitle: '',
    tournamentTitle: '',
    geography: '',
    eventParticipants: [],
    outcomes: []
}

const CreateEventForm = ({
    isEventManager,
    ManagerServiceContract
}: {
    isEventManager: boolean
    ManagerServiceContract:
        | CreateEvent<EventManagerService, EventManagerService.Key, string>[]
        | CreateEvent<
              MarketingManagerService,
              MarketingManagerService.Key,
              string
          >[]
}) => {
    const [isConfirmingCreation, setIsConfirmingCreation] =
        React.useState(false)
    const ledger = useLedger()
    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader
    } = useGlobalGamblingConfigContext()

    useScrollToTop()

    if (GlobalGamblingConfigurationLoader) {
        return <LoaderSpinner />
    }

    const minValue = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'minOdd'
    )
    const maxValue = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'maxOdd'
    )

    return (
        <div>
            <Formik
                initialValues={INIT_VALUES}
                onSubmit={(values, { resetForm, setSubmitting }) => {
                    let choice = isEventManager
                        ? EventManagerService.RequestEventOrigination
                        : MarketingManagerService.RequestEventOrigination
                    const preparedMapForTitles = MapGenerator(
                        values.englishTitle,
                        values.spanishTitle,
                        values.portugueseTitle
                    )
                    ledger
                        .exercise(
                            choice,
                            ManagerServiceContract[0].contractId,
                            {
                                eventOrigin: {
                                    tag: 'CustomerEvent',
                                    value: {
                                        startDate: moment(
                                            values.startDate
                                        ).toISOString(),
                                        description: values.description,
                                        assetLabel:
                                            generateAssetLabel().toString(),
                                        market: {
                                            tag: values.market,
                                            value:
                                                values.sportMarketName.length >
                                                0
                                                    ? values.sportMarketName
                                                    : {}
                                        } as unknown as Market,
                                        submarkets: [
                                            {
                                                tag: 'Tournament',
                                                value: values.tournamentTitle
                                            }
                                        ],
                                        geography: {
                                            tag: 'Geography',
                                            value: values.geography
                                        },
                                        eventTitle: preparedMapForTitles,
                                        eventParticipants:
                                            values.eventParticipants.map(
                                                participant => ({
                                                    ...participant,
                                                    co_op:
                                                        participant.co_op
                                                            .length > 0
                                                            ? [
                                                                  participant.co_op
                                                              ]
                                                            : null
                                                })
                                            ),
                                        outcomes: values.outcomes.map(
                                            ({
                                                odds,
                                                order,
                                                participantId,
                                                participantOrder,
                                                subtype,
                                                tag,
                                                valueOfTag
                                            }) => ({
                                                outcome: {
                                                    order,
                                                    participantId:
                                                        tag === 'OverUnder'
                                                            ? null
                                                            : participantId ===
                                                              ''
                                                            ? null
                                                            : participantId,
                                                    participantOrder:
                                                        participantOrder === ''
                                                            ? '0'
                                                            : participantOrder,
                                                    subtype,
                                                    type_: {
                                                        tag,
                                                        value: valueOfTag
                                                    }
                                                },
                                                odd: {
                                                    tag: 'Decimal',
                                                    value: odds
                                                }
                                            })
                                        ) as InputOutcomeOdd[],
                                        eventStatus: 'NotStarted',
                                        eventResults: []
                                    }
                                }
                            }
                        )
                        .then(() => toast.success('Request Event Origination'))
                        .catch(error => {
                            console.error(error)
                            toast.error(
                                'Something went wrong please try again later'
                            )
                        })
                        .finally(() => {
                            setIsConfirmingCreation(false)
                            resetForm()
                            setSubmitting(false)
                        })
                }}
                validationSchema={getYupSchemaValidation(minValue, maxValue)}
            >
                {isConfirmingCreation ? (
                    <ConfirmEventPage
                        handleCancel={() => setIsConfirmingCreation(false)}
                    />
                ) : (
                    <FormCreateEvent
                        handleEdit={() => setIsConfirmingCreation(true)}
                        isEdit={false}
                    />
                )}
            </Formik>
        </div>
    )
}

export default CreateEventForm
