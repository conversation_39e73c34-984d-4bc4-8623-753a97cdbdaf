import React from 'react'
import { useFormikContext } from 'formik'

import { TEditFormData } from 'Containers/Admin/EditEvents/EditPage'
import LoaderSpinner from 'Components/Loader'
import useScrollToTop from 'Hooks/useScrollToTop'

import 'Components/Admin/Form/style.scss'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import { getTextByLang } from 'Components/Event/EventCard/SportTranslations.helper'

const DisplayArrayDataContainer = ({
    children
}: {
    children: React.ReactNode
}) => <span className="displayArrayDataContainer">{children}</span>

const MainEventInformationContainer = ({
    children
}: {
    children: React.ReactNode
}) => <div className="mainEventInformationContainer">{children}</div>

const MainEventInformation = ({ children }: { children: React.ReactNode }) => (
    <div className="mainEventInformation">{children}</div>
)

export default function ConfirmEventPage({
    handleCancel
}: {
    handleCancel: () => void
}) {
    const { values, submitForm, isSubmitting } =
        useFormikContext<TEditFormData>()

    const { sportTranslationsContracts, loadingSportTranslations } =
        useSportTranslationsContext()

    const US =
        !loadingSportTranslations &&
        sportTranslationsContracts.length > 0 &&
        sportTranslationsContracts[0]?.payload?.sportsMap
            ? getTextByLang(
                  'en_uk',
                  sportTranslationsContracts[0]?.payload?.sportsMap
              )
            : []

    useScrollToTop()

    if (isSubmitting || loadingSportTranslations) {
        return <LoaderSpinner />
    }

    return (
        <MainEventInformationContainer>
            <h2>Please review your data:</h2>
            <MainEventInformation>
                <h3>Start Date:</h3>
                <p>{values.startDate.replace('T', ', ')}</p>
            </MainEventInformation>
            <MainEventInformation>
                <h3>Event english title:</h3>
                <p>{values.englishTitle}</p>
            </MainEventInformation>
            <MainEventInformation>
                <h3>Event spanish title:</h3>
                <p>{values.spanishTitle}</p>
            </MainEventInformation>
            <MainEventInformation>
                <h3>Event portuguese title:</h3>
                <p>{values.portugueseTitle}</p>
            </MainEventInformation>
            <MainEventInformation>
                <h3>Description:</h3>
                <p>{values.description}</p>
            </MainEventInformation>
            <MainEventInformation>
                <h3>Market:</h3>
                <p>{values.market}</p>
            </MainEventInformation>
            {values.sportMarketName ? (
                <MainEventInformation>
                    <h3>Sport Market Name:</h3>
                    {/** CHANGE HERE TO DISPLAY NAME */}
                    <p>
                        {
                            US.filter(
                                (sport: Record<string, string>) =>
                                    sport[values.sportMarketName]
                            )[0][values.sportMarketName]
                        }
                    </p>
                </MainEventInformation>
            ) : null}
            <MainEventInformation>
                <h3>Tournament title:</h3>
                <p>{values.tournamentTitle}</p>
            </MainEventInformation>
            <h3>Event Participants</h3>
            {React.Children.toArray(
                values.eventParticipants.map(ep => {
                    return (
                        <DisplayArrayDataContainer>
                            <p>id: {ep.id}</p>
                            <p>name: {ep.name}</p>
                            <p>order: {ep.order}</p>
                            {ep.co_op.length > 0 ? (
                                <p>co_op: {ep.co_op}</p>
                            ) : null}
                        </DisplayArrayDataContainer>
                    )
                })
            )}
            <h3>Outcomes</h3>
            {React.Children.toArray(
                values.outcomes.map(outcome => {
                    return (
                        <DisplayArrayDataContainer>
                            <p>subtype: {outcome.subtype}</p>
                            <p>order: {outcome.order}</p>
                            <p>tag: {outcome.tag}</p>
                            <p>participant id: {outcome.participantId}</p>
                            <p>participant order: {outcome.participantOrder}</p>
                            <p>subtype: {outcome.subtype}</p>
                            {typeof outcome.valueOfTag === 'string' ? (
                                <p>value of tag:{outcome.valueOfTag}</p>
                            ) : null}
                            <p>odds: {outcome.odds}</p>
                        </DisplayArrayDataContainer>
                    )
                })
            )}
            <button
                className="registerbtn__nextStep"
                onClick={() => submitForm()}
            >
                Submit
            </button>
            <button
                className="registerbtn__previousStep width100 "
                onClick={() => handleCancel()}
            >
                Cancel
            </button>
        </MainEventInformationContainer>
    )
}
