import React from 'react'
import { useNavigate } from 'react-router-dom'

import useIsAllowed from 'Hooks/useIsAdminAccount'

import LoaderSpinner from 'Components/Loader'
import TabsContent from './TabsContent'

import './style.scss'

export default function Admin() {
    const {
        isAdmin,
        loading,
        EventManagerServiceContract,
        MarketingManagerServiceContract
    } = useIsAllowed()
    const { push } = useNavigate()

    React.useEffect(() => {
        if (!loading && !isAdmin) {
            return navigate('/');
        }
        return () => {}
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAdmin, loading])

    return (
        <>
            {loading ? (
                <LoaderSpinner />
            ) : (
                <TabsContent
                    EventManagerServiceContract={EventManagerServiceContract}
                    MarketingManagerServiceContract={
                        MarketingManagerServiceContract
                    }
                />
            )}
        </>
    )
}
