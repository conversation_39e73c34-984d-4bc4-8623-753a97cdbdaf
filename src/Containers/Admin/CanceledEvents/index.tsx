import React from 'react'
import { toast } from 'react-toastify'
import { CreateEvent } from '@daml/ledger'
import { useLedger } from '@daml/react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'

import { publicContext } from 'Containers/App'

import TableCanceledContracts from 'Components/Admin/CanceledEvents/TableCanceledContracts'

export default function CanceledEvents({ EventManagerServiceContract }: {
    EventManagerServiceContract: CreateEvent<
        EventManagerService,
        EventManagerService.Key,
        string
    >[]
}) {
    const ledger = useLedger()
    const { contracts, loading } = publicContext.useStreamQueries(EventInstrument, () => [
        {
            details: {
                eventStatus: "Cancelled",
            }
        } as EventInstrument
    ], [])

    const handleClick = (eventLabel: string) => ledger.exercise(
        EventManagerService.RequestReinstateEvent,
        EventManagerServiceContract[0].contractId,
        { eventLabel }
    )
        .then(() => {
            toast.success(
                'Requested event resinstatement'
            );
        }
        ).catch(() => {
            toast.error('Something went wrong, please try again later.');
        })

    return (
        loading ?
            <div>loading...</div> :
            <TableCanceledContracts contracts={contracts} handleClick={handleClick} />
    )
}
