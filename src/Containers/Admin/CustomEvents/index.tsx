import React from 'react'
import { toast } from 'react-toastify'
import { CreateEvent } from '@daml/ledger'
import { useLedger } from '@daml/react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'

import { publicContext } from 'Containers/App'

import TableCustomEvents from 'Components/Admin/CustomEvents/TableCustomEvents'
import { Outcome, Status } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'
import LoaderSpinner from 'Components/Loader'

export default function CustomEvents({ EventManagerServiceContract }: {
    EventManagerServiceContract: CreateEvent<
        EventManagerService,
        EventManagerService.Key,
        string
    >[]
}) {
    const ledger = useLedger()
    const { contracts, loading } = publicContext.useStreamQueries(EventInstrument, () => [
        {
            status: "Active",
            details: {
                origin: 'Customer',
            }
        } as EventInstrument
    ], [])

    const handleClick = (eventStatus: Status, eventKey: EventInstrument.Key, results?: Outcome[]) => {
        const argumentsConstruct = eventStatus === 'Finished' ? {
            newEventStatus: "Finished" as Status,
            eventResults: results as unknown as Outcome[],
            oldEventKey: eventKey
        } : {
            newEventStatus: eventStatus as Status,
            eventResults: null,
            oldEventKey: eventKey
        }
        ledger.exercise(
            EventManagerService.RequestEventStatusUpdate,
            EventManagerServiceContract[0].contractId,
            argumentsConstruct
        )
            .then(() => {
                toast.success(
                    'Requested event was updated'
                );
            }
            ).catch(() => {
                toast.error('Something went wrong, please try again later.');
            })

    }
    if (loading) {
        return <LoaderSpinner />
    }

    if (!contracts.length) {
        return <p>There are no custom events.</p>
    }

    return <TableCustomEvents contracts={contracts} handleClick={handleClick} />
}
