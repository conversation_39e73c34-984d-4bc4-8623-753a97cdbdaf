import * as Yup from 'yup'

import { regexDecimal } from 'Components/Admin/Form/constants'
import { OutcomeOdds } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { Participant } from '@daml.js/enetpulse-integration/lib/EnetPulseIntegration/Events'

export type TFormDataUpdateOdds = {
    outcomeOdds: {
        outcome: {
            participantId: string | null,
            participantOrder: string,
            type_: { tag: string, value: string | {} },
            subtype: string,
            order: string
        }, odd: {
            tag: string,
            value: string
        }
    }[]
}

export const generateInitDataUpdateOdds = (outcomes: OutcomeOdds[]): TFormDataUpdateOdds => {
    return {
        outcomeOdds: outcomes.sort((a, b) => a < b ? -1 : 1).map(({ outcome, odds }) => ({
            outcome: {
                participantId: outcome.participantId,
                participantOrder: outcome.participantOrder,
                type_: { tag: outcome.type_.tag, value: outcome.type_.value },
                subtype: outcome.subtype,
                order: outcome.order
            },
            odd: { tag: "Decimal", value: odds.map.entriesArray().filter(data => data[0].tag === "Decimal")[0][0].value }
        }))
    }
}

export const getYupSchemaValidation = (minOddValue: string, maxOddValue: string) => {
    return Yup.object({
        outcomeOdds: Yup.array().of(
            Yup.object().shape({
                outcome: Yup.object().shape({
                    participantId: Yup.string().nullable(),
                    participantOrder: Yup.string().nullable(),
                    type_: Yup.object().shape(
                        {
                            tag: Yup.string().nullable(),
                            value: Yup.lazy((value) =>
                                typeof value === 'string'
                                    ? Yup.string() : Yup.object())
                        }),
                    subtype: Yup.string().nullable(),
                    order: Yup.string().nullable()
                }),
                odd: Yup.object().shape({
                    tag: Yup.string().nullable(),
                    value: Yup.string().matches(regexDecimal, 'Please add a valid Odd').test(val => Number(val) >= Number(minOddValue) && Number(val) <= Number(maxOddValue)).required(),
                })
            })
        )
    })
}

export function getLabelForForm(outcomeFromForm: {
    participantId: string | null;
    participantOrder: string;
    type_: {
        tag: string;
        value: string | {};
    },
    subtype: string;
    order: string;
}, eventParticipants: Participant[]) {
    const { subtype, participantId } = outcomeFromForm;
    if (subtype === "Win") {
        const name = eventParticipants.filter(part => part.id === participantId)[0]?.name
        if (name) {
            return name
        }
        return "Draw"
    }
    return subtype
}