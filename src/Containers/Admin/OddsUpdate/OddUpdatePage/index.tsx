import React from 'react'
import { useHistory, useParams, Link } from 'react-router-dom'
import { useLedger } from '@daml/react'
import { Formik } from 'formik'
import {
    EventInstrument,
    InputOutcomeOdd
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'
import { toast } from 'react-toastify'

import { publicContext } from 'Containers/App'
import OddsUpdateFormFields from 'Components/Admin/OddsUpdatePage/OddsUpdateFormFields'
import LoaderSpinner from 'Components/Loader'
import ConfirmModal from 'Components/Admin/OddsUpdatePage/ConfirmModal'

import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'
import useIsAllowed from 'Hooks/useIsAdminAccount'
import {
    getYupSchemaValidation,
    TFormDataUpdateOdds,
    generateInitDataUpdateOdds
} from '../utils'
import { getOddMinMaxValues } from 'Components/MyBets/utils'

import './style.scss'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'

const GoBack = () => {
    return (
        <Link to={'/admin/update-odd'} className="gambyladminpanel__backarrow">
            <FontAwesomeIcon
                icon={faArrowLeft}
                style={{ fontSize: '0.7rem' }}
                color="#a000e1"
            />{' '}
            Back to admin
        </Link>
    )
}

function OddsUpdatePage() {
    const { isAdmin, loading, EventManagerServiceContract } = useIsAllowed()
    const { eventId } = useParams<{ eventId: string }>()
    const { push } = useHistory()
    const [openModal, setOpenModal] = React.useState(false)
    const ledger = useLedger()
    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader
    } = useGlobalGamblingConfigContext()

    const { contracts, loading: loadingEvent } = publicContext.useQuery(
        EventInstrument,
        () => {
            return {
                eventId: {
                    label: eventId
                }
            }
        },
        [eventId]
    )

    React.useEffect(() => {
        if (!loading && !isAdmin) {
            return push('/')
        }
        return () => {}
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAdmin, loading])

    if (loading || loadingEvent || GlobalGamblingConfigurationLoader) {
        return <LoaderSpinner />
    }

    if (!contracts.length) {
        return <p>There are no events for the given id.</p>
    }

    const { eventParticipants, outcomes, eventTitle } =
        contracts[0].payload.details

    const valueMinOdd = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'minOdd'
    )
    const valueMaxOdd = getOddMinMaxValues(
        GlobalGamblingConfigurationContract,
        'maxOdd'
    )

    const handleSubmit = ({ outcomeOdds }: TFormDataUpdateOdds) => {
        ledger
            .exercise(
                EventManagerService.RequestUpdateEventOdds,
                EventManagerServiceContract[0].contractId,
                {
                    eventCid: contracts[0].contractId,
                    newOutcomes: outcomeOdds as unknown as InputOutcomeOdd[]
                }
            )
            .then(() => toast.success('Requested odd update'))
            .catch(error => {
                console.error('error on requesting odd  update', error)
                toast.error('something went wrong, please try again later')
            })
    }

    const INIT_VALUES = generateInitDataUpdateOdds(outcomes)

    return (
        <section className="content__container adminPage pagePadding">
            <div className="adminPage__card">
                <GoBack />
                <h1>{getEventTitleFromLang(eventTitle, 'US')}</h1>
                <Formik
                    initialValues={INIT_VALUES}
                    validationSchema={getYupSchemaValidation(
                        valueMinOdd,
                        valueMaxOdd
                    )}
                    onSubmit={async (values, { resetForm }) => {
                        await handleSubmit(values)
                        resetForm()
                        push('/admin/update-odd')
                    }}
                >
                    <>
                        <OddsUpdateFormFields
                            eventParticipants={eventParticipants}
                            maxOdd={valueMaxOdd}
                            minOdd={valueMinOdd}
                            handleClick={() => setOpenModal(true)}
                        />
                        <ConfirmModal
                            isOpen={openModal}
                            handleClose={() => setOpenModal(false)}
                        />
                    </>
                </Formik>
            </div>
        </section>
    )
}

export default OddsUpdatePage
