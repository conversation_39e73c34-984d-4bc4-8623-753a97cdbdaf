import React from 'react'
import LoaderSpinner from 'Components/Loader'
import { useNavigate } from 'react-router-dom'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'

import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import Table from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import useActivePromotions from 'Hooks/useActivePromotions'

interface ITableData {
    promotionTitle: string
    promotionId: string
}

const UpdatePromotion = () => {
    const { push } = useNavigate()
    const { promotionsContracts: contracts, promotionsLoader: loading } =
        useActivePromotions()

    const columnHelper = createColumnHelper<ITableData>()
    const columns = React.useMemo(
        () => [
            columnHelper.accessor('promotionTitle', {
                header: 'Promotion Title',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('promotionId', {
                header: '',
                cell: info => {
                    return (
                        <button
                            className="btn__admin"
                            onClick={() =>
                                navigate(`/update-promotion/${info.getValue()}`)
                            }
                        >Edit
                                                    </button>
                    );
                }
            })
        ],
        [columnHelper, push]
    )

    let data = React.useMemo(
        () =>
            contracts.map(data => ({
                promotionTitle: getEventTitleFromLang(
                    data.payload.title,
                    'en-US'
                ),
                promotionId: data.payload.promotionId
            })),
        [contracts]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: false
    })

    if (loading) {
        return <LoaderSpinner />
    }

    if (!contracts.length) {
        return <p>There are no contracts available to edit</p>
    }

    return (
        <>
            <Table
                tableHeader={table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                            <th key={header.id}>
                                {header.column.columnDef.header}
                            </th>
                        ))}
                    </tr>
                ))}
                tableBodyRow={table.getRowModel().rows.map(row => (
                    <tr key={row.id}>
                        {row.getVisibleCells().map(cell => (
                            <td key={cell.id}>
                                {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                )}
                            </td>
                        ))}
                    </tr>
                ))}
            />
            <Pagination table={table} />
        </>
    )
}

export default UpdatePromotion
