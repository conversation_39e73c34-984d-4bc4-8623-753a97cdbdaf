import React from 'react'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { useQuery } from '@daml/react'
import { useHistory, useParams } from 'react-router-dom'
import LoaderSpinner from 'Components/Loader'
import { toast } from 'react-toastify'
import moment from 'moment-timezone'
import { useLedger } from '@daml/react'
import { Formik } from 'formik'

import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import {
    PromotionAction,
    PromotionType
} from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'

import {
    endDateConverter,
    IPromotionForm,
    MapGenerator,
    ValidationSchemaPromotion
} from 'Containers/Admin/CreatePromotion/utils'
import ConfirmModal from 'Components/Admin/PromotionForm/ConfirmModal'
import PromotionForm from 'Components/Admin/PromotionForm/Form'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import GoBackNavigation from 'Components/GoBackNavigation'
import { handleDateTimeToUserLocalTime } from '../EditEvents/EditPage/utils'
import useIsAllowed from 'Hooks/useIsAdminAccount'

const UpdatePromotionForm = () => {
    const [openModal, setOpenModal] = React.useState(false)
    const ledger = useLedger()
    const { push } = useHistory()
    const {
        isAdmin,
        loading: loadingAdmin,
        MarketingManagerServiceContract
    } = useIsAllowed()
    const { promotionId } = useParams<{ promotionId: string }>()
    const { contracts, loading } = useQuery(
        Promotion,
        () => {
            return {
                promotionId
            }
        },
        []
    )

    React.useEffect(() => {
        if (!loadingAdmin && !isAdmin) {
            return push('/')
        }
        return () => {}
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAdmin, loadingAdmin])

    if (loading) {
        return <LoaderSpinner />
    }

    if (!contracts.length) {
        return <p>There are no contracts available to edit</p>
    }

    const {
        bannerUrl,
        baseUrl,
        config,
        longDescription,
        shortDescription,
        startDate,
        title,
        thumbnailUrl
    } = contracts[0].payload
    const initialValues: IPromotionForm = {
        englishTitle: getEventTitleFromLang(title, 'US'),
        spanishTitle: getEventTitleFromLang(title, 'MX'),
        portugueseTitle: getEventTitleFromLang(title, 'BR'),
        englishBaseURL: getEventTitleFromLang(baseUrl, 'US'),
        spanishBaseURL: getEventTitleFromLang(baseUrl, 'MX'),
        portugueseBaseURL: getEventTitleFromLang(baseUrl, 'BR'),
        englishBannerURL: getEventTitleFromLang(bannerUrl, 'US'),
        spanishBannerURL: getEventTitleFromLang(bannerUrl, 'MX'),
        portugueseBannerURL: getEventTitleFromLang(bannerUrl, 'BR'),
        englishLongDescription: getEventTitleFromLang(longDescription, 'US'),
        spanishLongDescription: getEventTitleFromLang(longDescription, 'MX'),
        portugueseLongDescription: getEventTitleFromLang(longDescription, 'BR'),
        englishShortDescription: getEventTitleFromLang(shortDescription, 'US'),
        spanishShortDescription: getEventTitleFromLang(shortDescription, 'MX'),
        portugueseShortDescription: getEventTitleFromLang(
            shortDescription,
            'BR'
        ),
        englishThumbnailUrl: getEventTitleFromLang(thumbnailUrl, 'US'),
        spanishThumbnailUrl: getEventTitleFromLang(thumbnailUrl, 'MX'),
        portugueseThumbnailUrl: getEventTitleFromLang(thumbnailUrl, 'BR'),
        startDate: handleDateTimeToUserLocalTime(startDate) ?? '',
        endDate: config.endDate ? config.endDate : '',
        limitedPromotion: config.limitedPromotion ?? '',
        maxAmount: config.maxAmount ?? '',
        minAmount: config.minAmount ?? '',
        actionTag: config.action.tag,
        actionValue: config.action.value.tag,
        valueTag: config.action.value.value.tag,
        valueTagValue: config.action.value.value.value,
        promoTypeTag: config.promoType.tag,
        promoTypeValue:
            config.promoType.tag === 'FirstTime'
                ? config.promoType.value ?? ''
                : ''
    }

    const handleExercise = (data: IPromotionForm): Promise<any> => {
        return ledger
            .exercise(
                Service.UpdatePromotionRequest,
                MarketingManagerServiceContract[0].contractId,
                {
                    promotionCid: contracts[0].contractId,
                    //CHANGE HERE to start date AND ADD MARGINS
                    newStartDate:
                        data.startDate.length > 0
                            ? moment(data.startDate).toISOString()
                            : null,
                    newTitle: MapGenerator(
                        data.englishTitle,
                        data.spanishTitle,
                        data.portugueseTitle
                    ),
                    newThumbnailUrl: MapGenerator(
                        data.englishThumbnailUrl,
                        data.spanishThumbnailUrl,
                        data.portugueseThumbnailUrl
                    ),
                    newBannerUrl: MapGenerator(
                        data.englishBannerURL,
                        data.spanishBannerURL,
                        data.portugueseBannerURL
                    ),
                    newBaseUrl: MapGenerator(
                        data.englishBaseURL,
                        data.spanishBaseURL,
                        data.portugueseBaseURL
                    ),
                    newLongDescription: MapGenerator(
                        data.englishLongDescription,
                        data.spanishLongDescription,
                        data.portugueseLongDescription
                    ),
                    newShortDescription: MapGenerator(
                        data.englishShortDescription,
                        data.spanishShortDescription,
                        data.portugueseShortDescription
                    ),
                    newConfig: {
                        action: {
                            tag: data.actionTag,
                            value: {
                                tag: data.actionValue,
                                value: {
                                    tag: data.valueTag,
                                    value: data.valueTagValue
                                }
                            }
                        } as unknown as PromotionAction,
                        endDate: endDateConverter(data.endDate),
                        limitedPromotion:
                            data.limitedPromotion.length > 0
                                ? data.limitedPromotion
                                : null,
                        maxAmount:
                            data.maxAmount.length > 0 ? data.maxAmount : null,
                        minAmount:
                            data.minAmount.length > 0 ? data.minAmount : null,
                        promoType: {
                            tag: data.promoTypeTag,
                            value:
                                data.promoTypeTag === 'Reload' ||
                                data.promoTypeTag === 'Other'
                                    ? {}
                                    : data.promoTypeValue.length > 0
                                    ? data.promoTypeValue
                                    : null
                        } as unknown as PromotionType
                    }
                }
            )
            .then(() => {
                toast.success('Promotion creation requested')
                setOpenModal(false)
                push('/admin/update-promotion')
            })
            .catch(error => {
                toast.error('Something went wrong')
                console.error('error creating promo', error)
                setOpenModal(false)
            })
    }

    return (
        <section className="content__container adminPage pagePadding">
            <div className="adminPage__card">
                <GoBackNavigation
                    to="/admin/update-promotion"
                    children={'Back to admin'}
                />
                <Formik
                    initialValues={initialValues}
                    onSubmit={async (values, { resetForm }) => {
                        await handleExercise(values)
                        resetForm()
                    }}
                    validationSchema={ValidationSchemaPromotion({
                        isEdit: true
                    })}
                >
                    <>
                        <PromotionForm
                            handleClick={() => setOpenModal(true)}
                            isEdit
                        />
                        <ConfirmModal
                            isOpen={openModal}
                            handleClose={() => setOpenModal(false)}
                            message="Are you sure you want to update this promotion?"
                        />
                    </>
                </Formik>
            </div>
        </section>
    )
}

export default UpdatePromotionForm
