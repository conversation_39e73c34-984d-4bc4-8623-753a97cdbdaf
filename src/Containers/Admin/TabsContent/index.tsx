import React from 'react'
import { Route, Routes, useNavigate, useLocation } from 'react-router-dom'
import { Service as EventManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Service'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import { CreateEvent } from '@daml/ledger'

import CanceledEvents from '../CanceledEvents'
import TabsWithoutLink from 'Components/TabsWithoutLink'
import CustomEvents from '../CustomEvents'
import CreatePromotion from '../CreatePromotion'
import OddUpdate from '../OddsUpdate'
import EditEvents from '../EditEvents'
import CreateEventForm from '../CreateEventForm'
import UpdatePromotion from '../UpdatePromotions'
import FeatureEvents from '../FeatureEvents'
import RecreatePromotion from '../RecreatePromotion'

import useCloseBetSlip from 'Hooks/useCloseBetSlip'

const TabsContent = ({
    EventManagerServiceContract,
    MarketingManagerServiceContract
}: {
    EventManagerServiceContract: CreateEvent<
        EventManagerService,
        EventManagerService.Key,
        string
    >[]
    MarketingManagerServiceContract: CreateEvent<
        MarketingManagerService,
        MarketingManagerService.Key,
        string
    >[]
}) => {
    const { push } = useNavigate()
    const { pathname } = useLocation()

    useCloseBetSlip()

    const isEventManager = EventManagerServiceContract.length > 0 ? true : false
    const isMarketingManager =
        MarketingManagerServiceContract.length > 0 ? true : false

    const tabs = [
        {
            label: 'Create Event',
            value: '/admin',
            onclick: () => navigate('/admin'),
            available: isEventManager
        },
        {
            label: 'Canceled Events',
            value: '/admin/canceled-event',
            onclick: () => navigate('/admin/canceled-event'),
            available: isEventManager
        },
        {
            label: 'Custom Events',
            value: '/admin/custom-event',
            onclick: () => navigate('/admin/custom-event'),
            available: isEventManager
        },
        {
            label: 'Create Promotion',
            value: '/admin/createpromotion',
            onclick: () => navigate('/admin/createpromotion'),
            available: isMarketingManager
        },
        {
            label: 'Update Odds',
            value: '/admin/update-odd',
            onclick: () => navigate('/admin/update-odd'),
            available: isEventManager
        },
        {
            label: 'Edit Events',
            value: '/admin/edit-events',
            onclick: () => navigate('/admin/edit-events'),
            available: isEventManager
        },
        {
            label: 'Update Promotions',
            value: '/admin/update-promotion',
            onclick: () => navigate('/admin/update-promotion'),
            available: isMarketingManager
        },
        {
            label: 'Feature Events',
            value: '/admin/feature-events',
            onclick: () => navigate('/admin/feature-events'),
            available: isEventManager || isMarketingManager
        },
        {
            label: 'Recreate Promotion',
            value: '/admin/recreate-promotion',
            onclick: () => navigate('/admin/recreate-promotion'),
            available: isMarketingManager
        }
    ]

    const serviceForBothOptions =
        EventManagerServiceContract.length > 0
            ? EventManagerServiceContract
            : MarketingManagerServiceContract

    return (
        <div className="content__container adminPage pagePadding">
            <div className="adminPage__card">
                <h1>Admin Panel</h1>
                <TabsWithoutLink
                    tabs={tabs.filter(tab => tab.available === true)}
                    selectedTab={pathname}
                />
                <Routes>
                    <div>
                        <Route exact path={'/admin'}>
                            <CreateEventForm
                                ManagerServiceContract={
                                    EventManagerServiceContract.length > 0
                                        ? EventManagerServiceContract
                                        : MarketingManagerServiceContract
                                }
                                isEventManager={isEventManager}
                            />
                        </Route>
                        <Route exact path={'/admin/canceled-event'}>
                            <CanceledEvents
                                EventManagerServiceContract={
                                    EventManagerServiceContract
                                }
                            />
                        </Route>
                        <Route exact path={'/admin/custom-event'}>
                            <CustomEvents
                                EventManagerServiceContract={
                                    EventManagerServiceContract
                                }
                            />
                        </Route>
                        <Route exact path={'/admin/createpromotion'}>
                            <CreatePromotion
                                MarketingManagerContract={
                                    MarketingManagerServiceContract
                                }
                            />
                        </Route>
                        <Route exact path={'/admin/update-odd'}>
                            <OddUpdate />
                        </Route>
                        <Route exact path={'/admin/edit-events'}>
                            <EditEvents />
                        </Route>
                        <Route exact path={'/admin/update-promotion'}>
                            <UpdatePromotion />
                        </Route>
                        <Route exact path={'/admin/feature-events'}>
                            <FeatureEvents
                                ManagerServiceContract={serviceForBothOptions}
                                isEventManager={isEventManager}
                            />
                        </Route>
                        <Route exact path={'/admin/recreate-promotion'}>
                            <RecreatePromotion />
                        </Route>
                    </div>
                </Routes>
            </div>
        </div>
    );
}

export default TabsContent
