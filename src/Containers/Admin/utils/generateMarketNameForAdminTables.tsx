import { Market } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'

interface generateMarketNameForAdminTablesParams {
    market: Market
    usLang: Record<string, string>[]
}

export const generateMarketNameForAdminTables = ({
    market,
    usLang
}: generateMarketNameForAdminTablesParams) => {
    const isEntertainment = market.tag === 'Entertainment'
    const isPolitics = market.tag === 'Politics'
    if (isEntertainment || isPolitics) {
        return market?.tag
    }
    const usName =
        usLang.filter(sportTitle => sportTitle[market.value])[0][
            market.value
        ] ?? '-'
    return usName
}
