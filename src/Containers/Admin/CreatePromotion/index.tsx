import React from 'react'
import { toast } from 'react-toastify'
import moment from 'moment-timezone'
import { useLedger } from '@daml/react'
import { CreateEvent } from '@daml/ledger'
import { Formik } from 'formik'

import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import {
    PromotionAction,
    PromotionType
} from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'

import {
    IPromotionForm,
    INIT_VALUES_CREATE_PROMOTION_FORM,
    MapGenerator,
    ValidationSchemaPromotion,
    endDateConverter
} from './utils'
import ConfirmModal from 'Components/Admin/PromotionForm/ConfirmModal'
import PromotionForm from 'Components/Admin/PromotionForm/Form'

import '../style.scss'

function CreatePromotion({
    MarketingManagerContract
}: {
    MarketingManagerContract: CreateEvent<Service, Service.Key, string>[]
}) {
    const [openModal, setOpenModal] = React.useState(false)
    const ledger = useLedger()

    function handleExercise(values: IPromotionForm) {
        const {
            englishTitle,
            spanishTitle,
            portugueseTitle,
            englishBannerURL,
            spanishBannerURL,
            portugueseBannerURL,
            englishLongDescription,
            spanishLongDescription,
            portugueseLongDescription,
            englishBaseURL,
            spanishBaseURL,
            portugueseBaseURL,
            englishShortDescription,
            spanishShortDescription,
            portugueseShortDescription,
            englishThumbnailUrl,
            spanishThumbnailUrl,
            portugueseThumbnailUrl,
            endDate,
            limitedPromotion,
            maxAmount,
            minAmount,
            startDate,
            actionTag,
            actionValue,
            promoTypeTag,
            promoTypeValue,
            valueTag,
            valueTagValue
        } = values

        const testTitle = MapGenerator(
            englishTitle,
            spanishTitle,
            portugueseTitle
        )
        const testBanner = MapGenerator(
            englishBannerURL,
            spanishBannerURL,
            portugueseBannerURL
        )
        const testBaseUrl = MapGenerator(
            englishBaseURL,
            spanishBaseURL,
            portugueseBaseURL
        )
        const testLongDesc = MapGenerator(
            englishLongDescription,
            spanishLongDescription,
            portugueseLongDescription
        )
        const testShortDesc = MapGenerator(
            englishShortDescription,
            spanishShortDescription,
            portugueseShortDescription
        )
        const testThumbnailURL = MapGenerator(
            englishThumbnailUrl,
            spanishThumbnailUrl,
            portugueseThumbnailUrl
        )

        ledger
            .exercise(
                Service.CreatePromotionRequest,
                MarketingManagerContract[0].contractId,
                {
                    title: testTitle,
                    bannerUrl: testBanner,
                    baseUrl: testBaseUrl,
                    config: {
                        action: {
                            tag: actionTag,
                            value: {
                                tag: actionValue,
                                value: {
                                    tag: valueTag,
                                    value: valueTagValue
                                }
                            }
                        } as unknown as PromotionAction,
                        endDate: endDateConverter(endDate),
                        limitedPromotion:
                            limitedPromotion.length > 0
                                ? limitedPromotion
                                : null,
                        maxAmount: maxAmount.length > 0 ? maxAmount : null,
                        minAmount: minAmount.length > 0 ? minAmount : null,
                        promoType: {
                            tag: promoTypeTag,
                            value:
                                promoTypeTag === 'Reload'
                                    ? {}
                                    : promoTypeValue.length > 0
                                    ? promoTypeValue
                                    : null
                        } as unknown as PromotionType
                    },
                    longDescription: testLongDesc,
                    promotionId: Math.floor(Math.random() * 10000).toString(),
                    shortDescription: testShortDesc,
                    startDate: moment(startDate).toISOString(),
                    status: 'Active',
                    thumbnailUrl: testThumbnailURL
                }
            )
            .then(() => {
                toast.success('Promotion creation requested')
                setOpenModal(false)
            })
            .catch(error => {
                toast.error('Something went wrong')
                console.error('error creating promo', error)
                setOpenModal(false)
            })
    }

    return (
        <Formik
            initialValues={INIT_VALUES_CREATE_PROMOTION_FORM}
            onSubmit={async (values, { resetForm }) => {
                await handleExercise(values)
                resetForm()
            }}
            validationSchema={ValidationSchemaPromotion({
                isEdit: false
            })}
        >
            <>
                <PromotionForm handleClick={() => setOpenModal(true)} />
                <ConfirmModal
                    isOpen={openModal}
                    handleClose={() => setOpenModal(false)}
                />
            </>
        </Formik>
    )
}

export default CreatePromotion
