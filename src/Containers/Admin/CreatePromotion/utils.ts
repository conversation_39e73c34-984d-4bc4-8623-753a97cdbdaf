import * as Yup from 'yup'
import moment from 'moment-timezone'
import * as damlTypes from '@daml/types'
import { regexDecimal } from 'Components/Admin/Form/constants'

const wholeNumberRegex = /^\d+$/

export interface IPromotionForm {
    englishTitle: string
    spanishTitle: string
    englishBaseURL: string
    spanishBaseURL: string
    portugueseBaseURL: string
    portugueseTitle: string
    englishBannerURL: string
    spanishBannerURL: string
    portugueseBannerURL: string
    englishLongDescription: string
    spanishLongDescription: string
    portugueseLongDescription: string
    englishShortDescription: string
    spanishShortDescription: string
    portugueseShortDescription: string
    englishThumbnailUrl: string
    spanishThumbnailUrl: string
    portugueseThumbnailUrl: string
    startDate: string
    endDate: string
    limitedPromotion: string
    maxAmount: string
    minAmount: string
    actionTag: string
    actionValue: string
    valueTag: string
    valueTagValue: string
    promoTypeTag: string
    promoTypeValue: string
}

export const INIT_VALUES_CREATE_PROMOTION_FORM: IPromotionForm = {
    englishTitle: '',
    spanishTitle: '',
    englishBaseURL: '',
    spanishBaseURL: '',
    portugueseBaseURL: '',
    portugueseTitle: '',
    englishBannerURL: '',
    spanishBannerURL: '',
    portugueseBannerURL: '',
    englishLongDescription: '',
    spanishLongDescription: '',
    portugueseLongDescription: '',
    englishShortDescription: '',
    spanishShortDescription: '',
    portugueseShortDescription: '',
    englishThumbnailUrl: '',
    spanishThumbnailUrl: '',
    portugueseThumbnailUrl: '',
    startDate: '',
    endDate: '',
    limitedPromotion: '', //whole number || null
    maxAmount: '', //Decimal || null,
    minAmount: '', //Decimal || null,
    actionTag: 'Bet',
    actionValue: 'Discount',
    valueTag: 'Percentage',
    valueTagValue: '', //Decimal
    promoTypeTag: 'FirstTime',
    promoTypeValue: '' //null || "whole number" || {} if tag === Reload
}

export const ValidationSchemaPromotion = ({
    isEdit = false
}: {
    isEdit?: boolean
}) =>
    Yup.object({
        englishTitle: Yup.string()
            .min(1)
            .required('English title is a required field'),
        spanishTitle: Yup.string()
            .min(1)
            .required('Spanish title is a required field'),
        portugueseTitle: Yup.string()
            .min(1)
            .required('Portuguese title is a required field'),
        englishBaseURL: Yup.string()
            .min(1)
            .required('English base URL is a required field'),
        spanishBaseURL: Yup.string()
            .min(1)
            .required('Spanish base URL is a required field'),
        portugueseBaseURL: Yup.string()
            .min(1)
            .required('Portuguese base URL is a required field'),
        englishBannerURL: Yup.string()
            .min(1)
            .required('English banner URL is a required field'),
        spanishBannerURL: Yup.string()
            .min(1)
            .required('Spanish banner URL is a required field'),
        portugueseBannerURL: Yup.string()
            .min(1)
            .required('Portuguese banner URL is a required field'),
        englishLongDescription: Yup.string()
            .min(1)
            .required('English long description is a required field'),
        spanishLongDescription: Yup.string()
            .min(1)
            .required('Spanish long description is a required field'),
        portugueseLongDescription: Yup.string()
            .min(1)
            .required('Portuguese long description is a required field'),
        englishShortDescription: Yup.string()
            .min(1)
            .required('English short description is a required field'),
        spanishShortDescription: Yup.string()
            .min(1)
            .required('Spanish short description is a required field'),
        portugueseShortDescription: Yup.string()
            .min(1)
            .required('Portuguese short description is a required field'),
        englishThumbnailUrl: Yup.string()
            .min(1)
            .required('English thumbnail url  is a required field'),
        spanishThumbnailUrl: Yup.string()
            .min(1)
            .required('Spanish thumbnail url  is a required field'),
        portugueseThumbnailUrl: Yup.string()
            .min(1)
            .required('Portuguese thumbnail url  is a required field'),
        startDate: isEdit
            ? Yup.string().nullable()
            : Yup.string()
                  .test(
                      'startDate',
                      'It should be an hour later than now',
                      function (val) {
                          return moment(val).isAfter(moment().add(1, 'hours'))
                      }
                  )
                  .required('Start date is a required  field'),
        endDate: Yup.string()
            .nullable()
            .test(
                'endDate',
                'End Date must be today or a future date.',
                function (val) {
                    if (!val) return true // Validation to allow null values
                    const parsedDate = moment(val, 'YYYY-MM-DD', true) // Strict parsing of End DAte
                    if (!parsedDate.isValid()) return false // Ensure it's a valid date
                    const formatedNowDate = moment(Date.now()).format(
                        'YYYY-MM-DD'
                    ) // Current Date in a valid format
                    return parsedDate.isSameOrAfter(formatedNowDate)
                }
            ),
        limitedPromotion: Yup.string()
            .matches(wholeNumberRegex, 'Please add a valid value')
            .nullable(),
        //IF !bet && !deposit -> bonus -> percentage return no max amount
        maxAmount: Yup.string()
            .matches(regexDecimal, 'Please add a valid value')
            .nullable()
            .test(
                'validateMaxAmount',
                "Max Amount can only be set when the Promotion Action Type is 'Bet,' or when the Promotion Action Type is 'Deposit' with 'Bonus' selected in the Discount or Bonus field, and 'Percentage' selected in the Percentage or Cash field.",
                function (value) {
                    const { actionValue, valueTag, actionTag } = this.parent

                    // Allow maxAmount only if the conditions for "Bonus" and "Deposit" with "Percentage" OR if it's a "Bet"
                    if (
                        (actionValue === 'Bonus' &&
                            valueTag === 'Percentage' &&
                            actionTag === 'Deposit') ||
                        actionTag === 'Bet'
                    ) {
                        return true // valid case, maxAmount can exist
                    }

                    // Otherwise, return false to trigger the error message
                    return !value // should return false only if maxAmount has a value
                }
            ),
        minAmount: Yup.string()
            .matches(regexDecimal, 'Please add a valid value')
            .nullable(),
        actionTag: Yup.string()
            .min(1)
            .required('Action Tag is a required field'),
        actionValue: Yup.string()
            .min(1)
            .test(
                'validateActionValue',
                "'Bonus' can only be selected when the Promotion Action Type is set to 'Deposit'.",
                function (value) {
                    const { actionTag } = this.parent
                    // If actionTag is 'Deposit', then actionValue can be 'Bonus'
                    if (actionTag === 'Deposit' && value === 'Bonus') {
                        return true // Valid case for Bonus with Deposit
                    }
                    // If actionTag is NOT 'Deposit', ensure actionValue is NOT 'Bonus'
                    if (actionTag !== 'Deposit' && value === 'Bonus') {
                        return false // Invalid case: Bonus with non-Deposit actionTag
                    }
                    return true // Other values are valid
                }
            )
            .required('Action Value is a required field'),
        valueTag: Yup.string()
            .min(1)
            .test(
                'validateValueTag',
                "'Cash' can only be selected when 'Bonus' is chosen in the Discount or Bonus field.",
                function (value) {
                    const { actionValue } = this.parent
                    // If valueTag is 'Cash', then actionValue can't be be 'Bonus'
                    if (value === 'Cash' && actionValue !== 'Bonus') {
                        return false
                    }
                    return true
                }
            )
            .required('Value is a required field'),
        valueTagValue: Yup.string()
            .matches(regexDecimal, 'Please add a valid value')
            .required('Value is a required field'),
        promoTypeTag: Yup.string()
            .min(1)
            .required('Type of promotion is a required field'),
        promoTypeValue: Yup.string()
            .matches(wholeNumberRegex, 'Please add a valid value')
            .nullable()
    })

export function MapGenerator(en: string, sp: string, pt: string) {
    return damlTypes
        .emptyMap<string, string>()
        .set('en-US', en)
        .set('es', sp)
        .set('pt', pt)
}

/**
 *
 * @param date string
 * @returns in case no value for date is set returns null, if there is a date returns a formatted date with type YYYY-MM-DD
 */
export function endDateConverter(date: string) {
    let preparedDate = null
    if (date.length > 0) {
        preparedDate = moment(date).format('YYYY-MM-DD')
    }
    return preparedDate
}
