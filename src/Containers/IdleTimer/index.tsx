import React from 'react'
import { useNavigate } from 'react-router-dom'
import IdleTimer from 'react-idle-timer'
import { useAuth0 } from '@auth0/auth0-react'

import useIsAuthenticated from 'Hooks/useAuth'
import { useUserDispatch, signOut } from 'State/UserContext'

import { isLocalDev } from 'config'
import { logoutParams } from 'Constants/Auth0Constants'
import {
    signOutManagerLogin,
    useManagerLoginDispatch,
    useManagerLoginState
} from 'State/ManagerLoginContext'
import { cache } from 'Utils/cache'

const { remove } = cache()

export default function IdleTimerComponent() {
    const idleTimer1Ref = React.useRef<any>(null)
    const isAuthenticated = useIsAuthenticated()
    const userDispatch = useUserDispatch()
    const { isAuthenticated: managerIsAuthenticated } = useManagerLoginState()
    const managerDispatch = useManagerLoginDispatch()
    const navigate = useNavigate()
    const { logout } = useAuth0()

    //CHANGE HERE LOGIC
    const handleLogoutAuth0 = () => {
        logout(logoutParams)
    }

    const handlManagerLogout = () => {
        signOutManagerLogin(managerDispatch, history)
    }

    // CHANGE HERE AUTH0
    const handleLogoutClick = () => {
        remove('bets')
        if (managerIsAuthenticated) {
            return handlManagerLogout()
        }
        if (isLocalDev) {
            return signOut(userDispatch, history)
        }
        return handleLogoutAuth0()
    }

    return isAuthenticated ? (
        <IdleTimer
            ref={idleTimer1Ref as any}
            timeout={60 * 60 * 1000}
            onIdle={handleLogoutClick}
        />
    ) : null
}
