import React from 'react'
import { useHistory } from 'react-router-dom'
import { Formik } from 'formik'
import * as Yup from 'yup'
import { toast } from 'react-toastify'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'

import {
    loginManager,
    useManagerLoginDispatch
} from 'State/ManagerLoginContext'
import { validateGambylAdminToken } from 'Utils/validateGambylAdminToken'

import useScrollToTop from 'Hooks/useScrollToTop'
import useIsAuthenticated from 'Hooks/useAuth'

import './style.scss'

const LoginSchema = Yup.object({
    partyData: Yup.string().required('Required field')
})

function Wrapper({ children }: { children: React.ReactNode }) {
    return (
        <div className="loginManager">
            <div className="loginManager__container">{children}</div>
        </div>
    )
}

export default function EventMarketingManagerLogin() {
    const history = useHistory()
    const managerDispatch = useManagerLoginDispatch()
    const isAuthenticated = useIsAuthenticated()

    useScrollToTop()

    const handleLogin = async (values: { partyData: string }) => {
        const { partyData } = values
        if (partyData.length === 0) {
            return toast.error('Wrong credentials')
        }
        let { token, party, isGambylManager } = await validateGambylAdminToken(
            partyData
        )
        if (isGambylManager) {
            loginManager(managerDispatch, history, { token, party })
            return toast.success('Logged in successfully')
        }
        return toast.error('Wrong credentials')
    }

    if (isAuthenticated) {
        return (
            <Wrapper>
                <h2>You are already authenticated</h2>
            </Wrapper>
        )
    }

    return (
        <Wrapper>
            <h2>Sign in to your account</h2>
            <Formik
                initialValues={{ partyData: '' }}
                onSubmit={values => {
                    handleLogin(values)
                }}
                validationSchema={LoginSchema}
            >
                {formik => (
                    <form
                        className="loginManager__form"
                        onSubmit={formik.handleSubmit}
                    >
                        <input
                            id="partyData"
                            placeholder="Please add your Manager Token"
                            className="register__input"
                            {...formik.getFieldProps('partyData')}
                        />
                        {formik.touched.partyData && formik.errors.partyData ? (
                            <ErrorMessage message={formik.errors.partyData} />
                        ) : null}
                        <button
                            disabled={Boolean(formik.errors.partyData)}
                            className="registerbtn__nextStep"
                            type="submit"
                        >
                            Sign In
                        </button>
                    </form>
                )}
            </Formik>
        </Wrapper>
    )
}
