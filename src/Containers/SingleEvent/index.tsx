import {
    Details,
    EventInstrument
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { publicContext } from 'Containers/App'
import { useParams } from 'react-router-dom'

import LoaderSpinner from 'Components/Loader'

import Carousel from 'Components/Dashboard/Carousel'
import useScrollToTop from 'Hooks/useScrollToTop'
import BreadcrumbsForEventPages from 'Components/BreadcrumbsForEventPages'
import Event from 'Components/Event'

export default function SingleEvent() {
    useScrollToTop()
    const { assetLabel } = useParams<{ assetLabel: string }>()
    const { contracts: eventContract, loading: eventLoading } =
        publicContext.useStreamQueries(
            EventInstrument,
            () => [
                {
                    eventId: { label: assetLabel },
                    details: {
                        eventStatus: 'NotStarted'
                    } as Details
                }
            ],
            [assetLabel]
        )

    const EventRenderChecker = !eventLoading

    if (!EventRenderChecker) {
        return <LoaderSpinner />
    }

    return (
        <>
            <Carousel />
            <div className="dashboard__wrapper">
                <div className="dashboard__card">
                    <BreadcrumbsForEventPages
                        event={eventContract}
                        levelOfBreadcrumbs="game"
                    />
                    {eventContract.length
                        ? eventContract.map((event, index) => {
                              return <Event event={event} key={index} />
                          })
                        : null}
                    {!eventContract.length ? (
                        <span>There no available events at the moment.</span>
                    ) : null}
                </div>
            </div>
        </>
    )
}
