import numeral from 'numeral'
import React from 'react'
import { Link } from 'react-router-dom'
import { useBetsState } from 'State/BetsContext'
import { useGlobalGamblingConfigContext } from 'State/GlobalGamblingConfigsContext'
import HeaderSecurity from '../HeaderSecurity'
import PopularPayments from '../PopularPayments'
import { useTranslation } from 'react-i18next'

export default function InfoSection({
    status
}: {
    status: string | undefined
}) {
    const { t } = useTranslation()
    const { showBets: isBetSlipOpen } = useBetsState()
    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader
    } = useGlobalGamblingConfigContext()

    const [unverifiedDepositLimit, setUnverifiedDepositLimit] =
        React.useState('')

    React.useEffect(() => {
        if (!GlobalGamblingConfigurationLoader) {
            setUnverifiedDepositLimit(
                GlobalGamblingConfigurationContract[0]?.payload
                    ?.unverifiedAccountMaxAmount
            )
        }
        return () => { }
    }, [GlobalGamblingConfigurationContract, GlobalGamblingConfigurationLoader])

    let isNotVerified = status === 'failed' || status === 'notStarted'
    let isVerifyingPending = status === 'pending'

    return isBetSlipOpen ? null : (
        <div className="depositpage__info">
            <HeaderSecurity />
            <div className="depositpage__infoText">
                <p>{t("DepositInfoSectionP1")}</p>
                {(isNotVerified || isVerifyingPending) && (
                    <>
                        <p>
                            <b>Specifics:</b>
                        </p>
                        {unverifiedDepositLimit?.length > 0 ? (
                            <p>
                                {t("DepositInfoSectionP2a1")}{' '}
                                <b>
                                    {numeral(
                                        Number(unverifiedDepositLimit)
                                    ).format('$0,0.00')}
                                </b>{' '}
                                {t("DepositInfoSectionP2a2")}
                            </p>
                        ) : null}
                    </>
                )}
                {isNotVerified && (
                    <p>
                        {t("DepositInfoSectionP3a1")}{' '}<Link to="/account">{t("DepositInfoSectionP3a2")}</Link>{' '}{t("DepositInfoSectionP3a3")}
                    </p>
                )}
                {isVerifyingPending && (
                    <p>
                        {t("DepositInfoSectionP4")}
                    </p>
                )}
                <p>
                    {t("DepositInfoSectionP5a1")}{' '}
                    <a href="mailto:<EMAIL>"><EMAIL></a>{' '}
                    {t("DepositInfoSectionP5a2")}
                </p>
            </div>
            <PopularPayments />
        </div>
    )
}
