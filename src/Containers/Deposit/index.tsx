import { Account } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import {
    GamblerUnverifiedIdentity,
    GamblerUnverifiedIdentityRequest
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { PromotionWallet } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import { DepositRequest } from '@daml.js/moneymatrix-integration/lib/MoneyMatrixIntegration/Deposit'
import { useLedger, useParty, useQuery, useStreamQueries } from '@daml/react'
import React from 'react'
import { v4 as uuidv4 } from 'uuid'
import { useNavigate } from 'react-router-dom'

import useIsFirstDeposit from 'Hooks/useIsFirstDeposit'
import useScrollToTop from 'Hooks/useScrollToTop'

import { useSelectedPromotionContext } from 'State/SelectPromotion'
import useIsAuthenticated from 'Hooks/useAuth'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'

import CloseXPopup from 'Components/CloseXPopup'
import LoaderSpinner from 'Components/Loader'
import PromotionsDropdown from 'Components/PromotionsDropdown'
import DepositForm from './DepositForm'
import InfoSection from './InfoSection'

import { haveIUsedPromo } from 'Components/PromotionsDropdown/promotion.helper'

import PaymentIntegrationIframe from 'Components/PaymentIntegrationIframe'
import useDepositLimits from 'Hooks/useDepositLimits'
import useRedirectToFirstDeposit from 'Hooks/useRedirectToFirstDeposit'
import './style.scss'
import { publicContext } from 'Containers/App'
import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { useI18LanguageContext } from 'State/LanguageState'
import { generateLocaleForPaymentIntegration } from 'Utils/generateLocaleForPaymentIntegration'
import { PendingDeposits } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'

export default function Deposit() {
    //Library HOOKS
    const { push } = useNavigate()
    const partyLedger = useParty()
    const depositLedger = useLedger()

    //CONTEXTS
    const isAuthenticated = useIsAuthenticated()
    const [selectedPromotion, setSelectedPromotion] =
        useSelectedPromotionContext()
    const { status } = useVerificationStatusContext()
    const { lang } = useI18LanguageContext()

    //STATES
    const [isLoading, setIsloading] = React.useState(false)
    const [promo, setPromo] = React.useState(() =>
        selectedPromotion?.payload.config.action.tag === 'Deposit'
            ? selectedPromotion
            : null
    )
    const [hasUserResquestedDeposit, setHasUserResquestedDeposit] =
        React.useState(false)
    const [cashierURL, setCashierUrl] = React.useState<string>('')

    //QUERIES
    const { contracts: accountContracts, loading: accountLoading } = useQuery(
        Account,
        () => {
            return { customer: partyLedger }
        },
        [partyLedger]
    )

    const {
        contracts: globalGamblingConfigsContract,
        loading: loadingGlobalGamblingConfigsContract
    } = publicContext.useQuery(GlobalGamblingConfiguration)

    const {
        contracts: promotionWalletContracts,
        loading: loadingPromotionWalletContracts
    } = useQuery(
        PromotionWallet,
        () => {
            return {
                customer: partyLedger
            }
        },
        [partyLedger]
    )

    const { contracts: gamblingServiceQuery, loading: gamblingServiceLoader } =
        useQuery(
            Service,
            () => {
                return { customer: partyLedger }
            },
            [partyLedger]
        )

    const {
        contracts: gamblerUnverifiedIdentity,
        loading: gamblerUnverifiedIndentityLoader
    } = useStreamQueries(
        GamblerUnverifiedIdentity,
        () => [{ customer: partyLedger }],
        [partyLedger]
    )

    const {
        contracts: gamblerUnverifiedIdentityRequest,
        loading: gamblerUnverifiedIdentityRequestLoader
    } = useStreamQueries(
        GamblerUnverifiedIdentityRequest,
        () => [{ customer: partyLedger }],
        [partyLedger]
    )

    const { contracts: existingURLContract, loading: existingURLLoading } =
        useStreamQueries(DepositRequest, () => [{ client: partyLedger }], [
            partyLedger
        ])
    const {
        contracts: pendingDepositContract,
        loading: isPendingDepositLoading
    } = useStreamQueries(PendingDeposits, () => [{ customer: partyLedger }], [
        partyLedger
    ])

    //CUSTOM HOOKS
    let depositLimits = useDepositLimits(accountLoading, accountContracts[0])
    useRedirectToFirstDeposit(accountLoading, accountContracts[0])
    const { isFirstDeposit, isLoadingFirstDeposit } =
        useIsFirstDeposit(partyLedger)
    useScrollToTop()

    function getUrl(uuid: string) {
        let depositRequestQuery = depositLedger.streamQueries(DepositRequest, [
            { transactionId: uuid }
        ])
        depositRequestQuery.on('change', response => {
            const url = response[0]?.payload.cashierURL
            if (!response.length) {
                return navigate('/');
            }
            if (
                window.location.pathname === '/deposit' ||
                window.location.pathname === '/first_deposit'
            ) {
                setCashierUrl(url)
            }
            depositRequestQuery.close()
        })
        depositRequestQuery.on('close', () => {
            setIsloading(false)
        })
    }

    const initDeposit = async (values: { requestedAmount: string }) => {
        try {
            setIsloading(true)
            if (!gamblingServiceQuery.length) {
                setIsloading(false)
                return
            }
            const paymentIntegrationpParty =
                globalGamblingConfigsContract[0]?.payload?.integrationParties
                    .entriesArray()
                    //CHANGE HERE LATER
                    .filter(a => a[0] === 'moneyMatrix')[0][1]
            const uuid = uuidv4()
            const language = generateLocaleForPaymentIntegration(lang)
            const requestConstructor = promo
                ? {
                      integrationParty: paymentIntegrationpParty,
                      requestedAmount: values.requestedAmount,
                      currency: 'USD',
                      transactionId: uuid,
                      promotion: promo?.key,
                      language
                  }
                : {
                      integrationParty: paymentIntegrationpParty,
                      requestedAmount: values.requestedAmount,
                      currency: 'USD',
                      transactionId: uuid,
                      promotion: null,
                      language
                  }
            await depositLedger.exercise(
                Service.RequestDepositAccount,
                gamblingServiceQuery[0]?.contractId,
                requestConstructor
            )
            setSelectedPromotion(null)
            setPromo(null)
            setHasUserResquestedDeposit(true)
            getUrl(uuid)
        } catch (error) {
            setIsloading(false)
            setHasUserResquestedDeposit(false)
            console.error('error on deposit', error)
        }
    }

    const loaderOrDeposit = () =>
        accountLoading ||
        isLoadingFirstDeposit ||
        isLoading ||
        loadingPromotionWalletContracts ||
        loadingGlobalGamblingConfigsContract ||
        gamblerUnverifiedIndentityLoader ||
        gamblingServiceLoader ||
        isPendingDepositLoading ? (
            <LoaderSpinner className="depositpage__loader" />
        ) : (
            <DepositForm
                handleSubmit={initDeposit}
                status={status}
                isFirstDeposit={isFirstDeposit}
                PromoComponent={
                    isAuthenticated && (
                        <PromotionsDropdown
                            styleClass="depositpage__promo"
                            promoFilter="Deposit"
                            defaultSelected={selectedPromotion}
                            showInfoIcon
                            onValueChange={setPromo}
                            firstTime={isFirstDeposit}
                            serviceContracts={gamblingServiceQuery}
                            serviceLoading={gamblingServiceLoader}
                            promotionWalletContracts={promotionWalletContracts}
                            isLoadingPromoWallet={
                                loadingPromotionWalletContracts
                            }
                        />
                    )
                }
                promo={promo}
                depositLimits={depositLimits}
                haveIUsedPromotion={haveIUsedPromo(
                    promotionWalletContracts[0],
                    promo
                )}
                gamblingServiceContracts={gamblingServiceQuery}
                gamblingServiceLoader={gamblingServiceLoader}
                existingURLContract={existingURLContract}
                existingURLLoading={existingURLLoading}
                gamblerUnverifiedIdentity={gamblerUnverifiedIdentity}
                gamblerUnverifiedIdentityLoader={
                    gamblerUnverifiedIndentityLoader
                }
                globalGamblingConfigsContract={globalGamblingConfigsContract}
                loadingGlobalGamblingConfigsContract={
                    loadingGlobalGamblingConfigsContract
                }
                gamblerUnverifiedIdentityRequestLoader={
                    gamblerUnverifiedIdentityRequestLoader
                }
                gamblerUnverifiedIdentityRequest={
                    gamblerUnverifiedIdentityRequest
                }
                accountContract={accountContracts}
                pendingDepositContract={pendingDepositContract}
            />
        )

    const renderIFrame = () => {
        if (cashierURL.length > 0) {
            return <PaymentIntegrationIframe iframeUrl={cashierURL} />
        }
        return <LoaderSpinner className="depositpage__loader" />
    }

    const depositFormOrIframe = () => {
        if (hasUserResquestedDeposit) {
            return renderIFrame()
        } else {
            return loaderOrDeposit()
        }
    }

    return (
        <div className="depositpage">
            <div className="depositpage__container">
                <CloseXPopup className="depositclose" path="/" />
                <div className="depositpage__form">{depositFormOrIframe()}</div>
                <InfoSection status={status} />
            </div>
        </div>
    )
}
