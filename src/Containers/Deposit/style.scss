@import '../../Styles/colors';

.depositclose {
    position: absolute;
    top: 20px;
    right: 25px;
    cursor: pointer;
    color: $gray;
    font-size: 1.2rem;
    font-weight: bold;
}

.depositpage {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGFirstDeposit.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 100vh;
    max-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    @media (max-height: 880px) and (max-width: 1245px) {
        min-height: 880px;
    }

    &__loader {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &__container {
        color: $darkGrey;
        text-align: center;
        line-height: 1.7rem;
        font-size: 1.15rem;
        display: flex;
        gap: 30px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        background: $white;
        width: 90%;
        padding: 40px 30px;
        border-radius: 6px;
        position: relative;

        p + p {
            margin-bottom: 15px;
        }

        @media (max-width: 1218px) {
            max-height: 800px;
            overflow-y: scroll;
        }
    }

    &__infoText {
        padding: 30px 0px;
    }

    &__info {
        flex: 1 1 400px;
        max-width: 100%;
        p {
            font-size: 0.95rem;

            a {
                text-decoration: none;
                color: $purple;
                font-weight: bold;
            }
        }

        p + p {
            margin-top: 10px;
        }
    }

    &__form {
        flex: 1 1 400px;
        text-align: left;

        max-height: 750px;

        h3 {
            font-family: 'Montserrat', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 2.188rem;
            line-height: 2.75rem;
            margin-bottom: 20px;
        }

        &__p {
            font-family: 'Montserrat', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 0.813rem;
            line-height: 1.25rem;
            letter-spacing: 0.063rem;
            text-transform: uppercase;
        }

        &__message {
            font-size: 0.9rem;
            font-weight: 400;
            color: $darkGrey;
            text-align: center;
        }

        button {
            width: 100%;
            margin: 20px 0 0 0;
        }
    }

    &__radio {
        accent-color: $purple;
        margin: 20px 0;

        p {
            margin-bottom: 20px;
        }
    }

    &__radioWrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        align-items: center;
    }

    &__radioContainer {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    &__icon {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    &__loader {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__promo {
        display: flex;
        flex-direction: column;
        margin-top: 20px;

        label {
            font-family: 'Montserrat', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 0.813rem;
            line-height: 1.25rem;
            letter-spacing: 0.063rem;
            text-transform: uppercase;
        }

        select {
            border: 1px solid $grayInpuBorder;
            border-radius: 6px;
            color: $black;
            font-family: 'Open Sans', sans-serif;
            font-size: 0.813rem;
            font-style: normal;
            font-weight: normal;
            height: 50px;
            line-height: 1.25rem;
            margin-top: 20px;
            padding: 0 20px;
            width: 100%;
            transition: border-color 0.2s ease-in-out,
                box-shadow 0.2s ease-in-out;

            &:focus {
                border-color: $blueShaddow;
                outline: 0;
                box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
            }

            &::placeholder {
                color: $black;
                opacity: 1;
            }

            &[type='checkbox'] {
                accent-color: $purple;
                cursor: pointer;
                height: 15px;
                padding: 0;
                margin-top: 0;
                vertical-align: middle;
                width: 25px;
                &:focus {
                    outline: 0;
                    box-shadow: none;
                }
            }
        }
    }
}
