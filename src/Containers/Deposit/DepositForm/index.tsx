import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { Formik, FormikValues } from 'formik'
import React from 'react'
import CurrencyInput from 'react-currency-input-field'
import TagManager from 'react-gtm-module'
import { Link, useHistory } from 'react-router-dom'
import * as Yup from 'yup'

import QuickDeposit from '../QuickDeposit'
import { QuickDepositOptions } from '../QuickDeposit/QuickDepositOptions'
import { checkIfLimitForUnverifiedIsPassed } from '../utils/checkIfLimitForUnverifiedIsPassed.utils'
import { promoValidator } from '../utils/promovalidator.utils'

import {
    Account,
    PendingDeposits
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import {
    GamblerUnverifiedIdentity,
    GamblerUnverifiedIdentityRequest
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { DepositRequest } from '@daml.js/moneymatrix-integration/lib/MoneyMatrixIntegration/Deposit'
import { CreateEvent } from '@daml/ledger'
import LoaderSpinner from 'Components/Loader'
import ErrorMessage from 'Components/RegisterUser/ErrorMessage'
import moment from 'moment-timezone'
import numeral from 'numeral'
import { useTranslation } from 'react-i18next'
import { logEvent } from 'Utils/analytics'
import ChargesCalculations from '../Messages/ChargesCalculations'
import DepositLimits from '../Messages/DepositLimits'
import FormTitle from '../Messages/FormTitle'
import HaveIUsedPromotion from '../Messages/HaveIUsedPromotion'
import UnverifiedDepositLimitMessage from '../Messages/UnverifiedDepositLimit'
import UserEmailMessage from '../Messages/UserEmailMessage'
import { diffOfDaysFromValue } from '../utils/calculateDaysForPromo.utils'

export default function DepositForm({
    handleSubmit,
    status,
    isFirstDeposit,
    PromoComponent,
    promo,
    depositLimits,
    haveIUsedPromotion,
    gamblingServiceContracts,
    gamblingServiceLoader,
    existingURLContract,
    existingURLLoading,
    gamblerUnverifiedIdentity,
    gamblerUnverifiedIdentityLoader,
    globalGamblingConfigsContract,
    loadingGlobalGamblingConfigsContract,
    gamblerUnverifiedIdentityRequest,
    gamblerUnverifiedIdentityRequestLoader,
    accountContract,
    pendingDepositContract
}: {
    handleSubmit: (values: { requestedAmount: string }) => Promise<void>
    status: string | undefined
    isFirstDeposit: boolean
    PromoComponent?: React.ReactNode
    promo?: any | null
    depositLimits: any[]
    haveIUsedPromotion: boolean
    gamblingServiceContracts: readonly CreateEvent<
        Service,
        Service.Key,
        string
    >[]
    gamblingServiceLoader: boolean
    existingURLContract: readonly CreateEvent<
        DepositRequest,
        DepositRequest.Key,
        string
    >[]
    existingURLLoading: boolean
    gamblerUnverifiedIdentity: readonly CreateEvent<
        GamblerUnverifiedIdentity,
        GamblerUnverifiedIdentity.Key,
        string
    >[]
    gamblerUnverifiedIdentityLoader: boolean
    globalGamblingConfigsContract: readonly CreateEvent<
        GlobalGamblingConfiguration,
        GlobalGamblingConfiguration.Key,
        string
    >[]
    loadingGlobalGamblingConfigsContract: boolean
    gamblerUnverifiedIdentityRequest: readonly CreateEvent<
        GamblerUnverifiedIdentityRequest,
        GamblerUnverifiedIdentityRequest.Key,
        string
    >[]
    gamblerUnverifiedIdentityRequestLoader: boolean
    accountContract: readonly CreateEvent<Account, Account.Key, string>[]
    pendingDepositContract: readonly CreateEvent<
        PendingDeposits,
        PendingDeposits.Key,
        string
    >[]
}) {
    const { t } = useTranslation()
    const { push } = useHistory()
    let fee = globalGamblingConfigsContract[0]?.payload?.depositFee
    let minDepositAmount =
        globalGamblingConfigsContract[0]?.payload?.minDepositAmount
    let unverifiedDepositLimit =
        globalGamblingConfigsContract[0]?.payload?.unverifiedAccountMaxAmount
    const pendingDeposits = pendingDepositContract[0]?.payload?.pendingDeposits
    const pendingDepositsSum = pendingDeposits
        ?.entriesArray()
        .reduce(
            (accumulator, [_, value]) => Number(accumulator) + Number(value),
            0
        )

    const {
        totalMainBalance,
        totalBetBalance,
        totalWithdrawBalance,
        totalBonusBetBalance
    } = accountContract[0]?.payload
    const totalAccountBalance =
        Number(totalMainBalance) +
        Number(totalWithdrawBalance) +
        (Number(totalBetBalance) - Number(totalBonusBetBalance))

    function getValidationSchema(status: string | undefined) {
        const ValidationSchema = Yup.object({
            requestedAmount: Yup.string()
                .max(10, 'The deposit input accepts only till 10 digits.')
                .test(
                    '__testforamount',
                    `Minimum deposit value is ${numeral(
                        minDepositAmount
                    ).format('$0,0.00')} ${
                        status !== 'success'
                            ? `and max ${numeral(unverifiedDepositLimit).format(
                                  '$0,0.00'
                              )}`
                            : ''
                    }`,
                    vl => {
                        if (status !== 'success') {
                            return (
                                Number(vl) >= Number(minDepositAmount) &&
                                Number(vl) < Number(unverifiedDepositLimit) + 1
                            )
                        }
                        return Number(vl) >= Number(minDepositAmount)
                    }
                )
                .required()
        })
        return ValidationSchema
    }

    function checkIfDisabled(formik: FormikValues, promo: any) {
        return promo
            ? Boolean(
                  formik.values?.requestedAmount?.length === 0 ||
                      formik.errors.requestedAmount ||
                      !promoValidator(
                          promo?.payload?.config,
                          formik.values.requestedAmount
                      )
              )
            : Boolean(
                  formik.values?.requestedAmount?.length === 0 ||
                      formik.errors.requestedAmount
              )
    }

    //HERE VALIDATION FOR DEPOSIT LIMIT
    const today = new Date()
    const checkLimit = (value: any) => {
        if (depositLimits?.length) {
            return depositLimits.some(limit => {
                if (
                    limit[0] === 'Daily' &&
                    moment(today).isSame(limit[1]?.date, 'day')
                ) {
                    return (
                        Number(limit[1]?.limit) <
                        Number(limit[1]?.accrued) + Number(value)
                    )
                }
                if (
                    limit[0] === 'Daily' &&
                    moment(today).isAfter(limit[1]?.date, 'day')
                ) {
                    return Number(limit[1]?.limit) < 0 + Number(value)
                }
                if (
                    limit[0] === 'Weekly' &&
                    diffOfDaysFromValue(today, limit[1]?.date) < 7
                ) {
                    return (
                        Number(limit[1]?.limit) <
                        Number(limit[1]?.accrued) + Number(value)
                    )
                }
                if (
                    limit[0] === 'Weekly' &&
                    diffOfDaysFromValue(today, limit[1]?.date) >= 7
                ) {
                    return Number(limit[1]?.limit) < 0 + Number(value)
                }
                if (
                    limit[0] === 'Monthly' &&
                    moment(today).isSame(limit[1]?.date, 'month')
                ) {
                    return (
                        Number(limit[1]?.limit) <
                        Number(limit[1]?.accrued) + Number(value)
                    )
                }
                if (
                    limit[0] === 'Monthly' &&
                    moment(today).isAfter(limit[1]?.date, 'month')
                ) {
                    return Number(limit[1]?.limit) < 0 + Number(value)
                }
                if (
                    limit[0] === 'Yearly' &&
                    moment(today).isSame(limit[1]?.date, 'year')
                ) {
                    return (
                        Number(limit[1]?.limit) <
                        Number(limit[1]?.accrued) + Number(value)
                    )
                }
                if (
                    limit[0] === 'Yearly' &&
                    moment(today).isAfter(limit[1]?.date, 'year')
                ) {
                    return Number(limit[1]?.limit) < 0 + Number(value)
                }
                return (
                    Number(limit[1]?.limit) <
                    Number(limit[1]?.accrued) + Number(value)
                )
            })
        } else {
            return false
        }
    }

    const checkDisableDeposit = (formik: any, promo: any) =>
        checkIfDisabled(formik, promo) ||
        checkLimit(formik.values?.requestedAmount) ||
        (!gamblerUnverifiedIdentityLoader &&
            gamblerUnverifiedIdentity[0]?.payload?.countryCode === '-') ||
        haveIUsedPromotion ||
        checkIfLimitForUnverifiedIsPassed(
            formik.values?.requestedAmount,
            status,
            totalAccountBalance,
            Number(unverifiedDepositLimit),
            pendingDepositsSum
        ) ||
        gamblingServiceContracts.length === 0

    const isDiscountTypePromo =
        promo?.payload?.config?.action.value.tag === 'Discount'

    const TagManagerData = () => {
        if (isFirstDeposit) {
            logEvent('first_deposit')
            TagManager.dataLayer({
                dataLayer: {
                    event: 'first_deposit'
                }
            })
        }
    }

    const renderDepositCondition =
        gamblingServiceLoader &&
        gamblingServiceContracts.length === 0 &&
        gamblerUnverifiedIdentityLoader &&
        gamblerUnverifiedIdentityRequestLoader

    return (
        <>
            {renderDepositCondition ? (
                <LoaderSpinner className="depositpage__loader" />
            ) : (
                <>
                    <Formik
                        initialValues={{
                            requestedAmount: minDepositAmount
                        }}
                        onSubmit={async (
                            { requestedAmount },
                            { resetForm }
                        ) => {
                            await handleSubmit({
                                requestedAmount
                            })
                            TagManagerData()
                            resetForm()
                        }}
                        validationSchema={getValidationSchema(status)}
                    >
                        {formik => (
                            <form
                                className="contact__form"
                                onSubmit={formik.handleSubmit}
                            >
                                <FormTitle isFirstDeposit={isFirstDeposit} />
                                <UserEmailMessage
                                    gamblerUnverifiedIdentityLoader={
                                        gamblerUnverifiedIdentityLoader
                                    }
                                    gamblerUnverifiedIdentityContract={
                                        gamblerUnverifiedIdentity
                                    }
                                    gamblerUnverifiedIdentityRequestLoader={
                                        gamblerUnverifiedIdentityRequestLoader
                                    }
                                    gamblerUnverifiedIdentityRequest={
                                        gamblerUnverifiedIdentityRequest
                                    }
                                />
                                <div className="depositpage__radio">
                                    <p className="depositpage__form__p">
                                        {t('QuickDeposit')}
                                    </p>
                                    <div>
                                        <QuickDeposit
                                            options={QuickDepositOptions(
                                                status
                                            )}
                                            isChecked={
                                                formik.values.requestedAmount
                                            }
                                            handleSelectBehaviour={element => {
                                                if (
                                                    formik.values
                                                        .requestedAmount ===
                                                    element.currentTarget.value
                                                ) {
                                                    return formik.setFieldValue(
                                                        'requestedAmount',
                                                        ''
                                                    )
                                                }
                                                formik.setFieldTouched(
                                                    'requestedAmount'
                                                )
                                                formik.setFieldValue(
                                                    'requestedAmount',
                                                    element.currentTarget.value
                                                )
                                            }}
                                        />
                                    </div>
                                </div>
                                <p className="depositpage__form__p">
                                    {t('DepositAmount')}
                                </p>
                                <CurrencyInput
                                    intlConfig={{
                                        locale: 'en-US',
                                        currency: 'USD'
                                    }}
                                    id="requestedAmount"
                                    name="requestedAmount"
                                    placeholder={`${t(
                                        'AmountToDeposit'
                                    )}. ${numeral(minDepositAmount).format(
                                        '$0,0.00'
                                    )}`}
                                    defaultValue={20}
                                    decimalsLimit={2}
                                    allowNegativeValue={false}
                                    step={1}
                                    value={formik.values.requestedAmount}
                                    onValueChange={value => {
                                        formik.setFieldTouched(
                                            'requestedAmount',
                                            true
                                        )
                                        formik.setFieldValue(
                                            'requestedAmount',
                                            value
                                        )
                                    }}
                                    prefix="$"
                                    className="register__input"
                                />
                                {formik.touched.requestedAmount &&
                                formik.errors.requestedAmount ? (
                                    <ErrorMessage
                                        message={formik.errors.requestedAmount}
                                    />
                                ) : null}
                                {PromoComponent}
                                <button
                                    type="submit"
                                    className="btn btn__green"
                                    disabled={checkDisableDeposit(
                                        formik,
                                        promo
                                    )}
                                >
                                    {t('DepositBtn')}
                                </button>
                                {minDepositAmount?.length > 0 &&
                                fee?.length > 0 &&
                                Number(formik.values.requestedAmount) >=
                                    Number(minDepositAmount) ? (
                                    <ChargesCalculations
                                        requestedAmount={
                                            formik.values?.requestedAmount
                                        }
                                        GlobalGamblingConfigurationLoader={
                                            loadingGlobalGamblingConfigsContract
                                        }
                                        haveIUsedPromotion={haveIUsedPromotion}
                                        isDiscountTypePromo={
                                            isDiscountTypePromo
                                        }
                                        promo={promo}
                                        fee={fee}
                                        minDepositValue={minDepositAmount}
                                    />
                                ) : null}
                                {minDepositAmount?.length > 0 &&
                                fee?.length > 0 ? (
                                    <DepositLimits
                                        depositLimits={depositLimits}
                                    />
                                ) : null}
                                <HaveIUsedPromotion
                                    haveIUsedPromotion={haveIUsedPromotion}
                                    promo={promo}
                                    requestedAmount={
                                        formik.values?.requestedAmount
                                    }
                                    isDiscountTypePromo={isDiscountTypePromo}
                                />
                                {!existingURLLoading &&
                                existingURLContract.length > 0 ? (
                                    <p className="depositpage__form__message">
                                        {t('DepositInProg1')}{' '}
                                        <Link to="/account/payments">
                                            {t('DepositInProg2')}
                                        </Link>
                                    </p>
                                ) : null}
                                {status !== undefined &&
                                status.length > 0 &&
                                unverifiedDepositLimit?.length > 0 ? (
                                    <UnverifiedDepositLimitMessage
                                        requestedAmount={
                                            formik.values?.requestedAmount
                                        }
                                        status={status}
                                        totalAccountBalance={
                                            totalAccountBalance
                                        }
                                        unverifiedDepositLimit={Number(
                                            unverifiedDepositLimit
                                        )}
                                        pendingDepositsSum={pendingDepositsSum}
                                    />
                                ) : null}
                            </form>
                        )}
                    </Formik>
                    <button
                        type="submit"
                        className="btn btn__primary"
                        onClick={() => push('/')}
                    >
                        {t('RedirectHome')}
                    </button>
                </>
            )}
        </>
    )
}
