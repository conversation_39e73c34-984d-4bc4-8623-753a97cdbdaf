import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import numeral from 'numeral'
import { promoValidator } from 'Containers/Deposit/utils/promovalidator.utils'
import { useTranslation } from 'react-i18next'

export default function ChargesCalculations({
    requestedAmount,
    GlobalGamblingConfigurationLoader,
    haveIUsedPromotion,
    isDiscountTypePromo,
    promo,
    fee,
    minDepositValue
}: {
    requestedAmount: string
    GlobalGamblingConfigurationLoader: boolean
    haveIUsedPromotion: boolean
    isDiscountTypePromo: boolean
    promo: CreateEvent<Promotion, Promotion.Key, string>
    fee: string
    minDepositValue: string
}) {
    const maxValueForDisplayDeposit = 11
    const { t } = useTranslation()

    const getFeePromo = (promoDiscaunt: string) => {
        return Number(fee) - Number(fee) * Number(promoDiscaunt)
    }

    function feeCalculation(value: string, promoDiscaunt?: string) {
        if (!promoDiscaunt) {
            return Number(fee) * Number(value) + Number(value)
        }
        const feePromo = getFeePromo(promoDiscaunt)
        return Number(feePromo) * Number(value) + Number(value)
    }

    if (
        !requestedAmount?.length &&
        requestedAmount?.length < maxValueForDisplayDeposit &&
        !GlobalGamblingConfigurationLoader
    ) {
        return null
    }

    if (
        !haveIUsedPromotion &&
        isDiscountTypePromo &&
        Number(requestedAmount) >= Number(minDepositValue) &&
        promoValidator(promo?.payload?.config, requestedAmount)
    ) {
        return (
            <p className="depositpage__form__message">
                {t("DepositChargesMessage1")}{' '}
                {numeral(
                    Number(
                        feeCalculation(
                            requestedAmount,
                            promo?.payload?.config.action.value.value.value
                        )
                    )
                ).format('$0,0.00')}{' '}
                {t("DepositChargesMessage2")}{' '}
                {getFeePromo(promo?.payload?.config.action.value.value.value) *
                    100}
                {t("DepositChargesMessage3")}
            </p>
        )
    }

    if (requestedAmount?.length < maxValueForDisplayDeposit) {
        return (
            <p className="depositpage__form__message">
                {t("DepositChargesMessage1")}{' '}
                {numeral(Number(feeCalculation(requestedAmount))).format(
                    '$0,0.00'
                )}{' '}
                {t("DepositChargesMessage2")}{' '}{Number(fee) * 100}{t("DepositChargesMessage3")}
            </p>
        )
    }

    return null
}
