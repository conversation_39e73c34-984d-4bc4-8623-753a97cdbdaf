import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { Link } from 'react-router-dom'
import { GamblerUnverifiedIdentity, GamblerUnverifiedIdentityRequest } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { useTranslation } from 'react-i18next'
import { isLocalDev } from 'config'

//Change message HERE
export default function UserEmailMessage({
    gamblerUnverifiedIdentityLoader,
    gamblerUnverifiedIdentityContract,
    gamblerUnverifiedIdentityRequest,
    gamblerUnverifiedIdentityRequestLoader
}: {
    gamblerUnverifiedIdentityLoader: boolean
    gamblerUnverifiedIdentityContract: readonly CreateEvent<
        GamblerUnverifiedIdentity,
        GamblerUnverifiedIdentity.Key,
        string
    >[]
    gamblerUnverifiedIdentityRequest: readonly CreateEvent<
        GamblerUnverifiedIdentityRequest,
        GamblerUnverifiedIdentityRequest.Key,
        string
    >[],
    gamblerUnverifiedIdentityRequestLoader: boolean
}) {
    const Messages = () => {
        const { t } = useTranslation();

        //First condition: doesn't have setup unverified identity
        const hasContractLength = gamblerUnverifiedIdentityContract?.length > 0
        const countryCode = isLocalDev ? "" : gamblerUnverifiedIdentityContract[0]?.payload.countryCode
        const isCountryCodeDash = countryCode === "-"
        const conditionToDisplayErrorMessage1 = isLocalDev ? !hasContractLength : hasContractLength && isCountryCodeDash

        //Second condition: is waiting for unverified identity to be approved
        const isWaitingRequestToBeApproved = gamblerUnverifiedIdentityRequest.length > 0

        if (conditionToDisplayErrorMessage1) {
            return (
                <p>
                    {t("DepositEmailMessagesP1")}{' '}
                    <Link to="/identity_setup">{t("DepositEmailMessagesP1a2")}</Link>
                </p>
            )
        }
        if (isWaitingRequestToBeApproved) {
            return (
                <p>
                    {t("DepositEmailMessagesP2")}
                </p>
            )
        }
        return null
    }
    return !gamblerUnverifiedIdentityLoader && !gamblerUnverifiedIdentityRequestLoader ? <Messages /> : null
}
