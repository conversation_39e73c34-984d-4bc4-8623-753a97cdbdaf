import React from 'react'
import numeral from 'numeral'
import moment from 'moment-timezone'
import { diffOfDaysFromValue } from 'Containers/Deposit/utils/calculateDaysForPromo.utils'

export default function DepositLimits({
    depositLimits
}: {
    depositLimits: any
}) {
    const today = new Date()

    const setLimitValue = (currentValue: any) => {
        if (
            currentValue[0] === 'Daily' &&
            moment(today).isSame(currentValue[1]?.date, 'day')
        ) {
            return currentValue[1]?.accrued
        }
        if (
            currentValue[0] === 'Daily' &&
            moment(today).isAfter(currentValue[1]?.date, 'day')
        ) {
            return 0
        }
        if (
            currentValue[0] === 'Weekly' &&
            diffOfDaysFromValue(today, currentValue[1]?.date) < 7
        ) {
            return currentValue[1]?.accrued
        }
        if (
            currentValue[0] === 'Weekly' &&
            diffOfDaysFromValue(today, currentValue[1]?.date) >= 7
        ) {
            return 0
        }
        if (
            currentValue[0] === 'Monthly' &&
            moment(today).isSame(currentValue[1]?.date, 'month')
        ) {
            return currentValue[1]?.accrued
        }
        if (
            currentValue[0] === 'Monthly' &&
            moment(today).isAfter(currentValue[1]?.date, 'month')
        ) {
            return 0
        }
        if (
            currentValue[0] === 'Yearly' &&
            moment(today).isSame(currentValue[1]?.date, 'year')
        ) {
            return currentValue[1]?.accrued
        }
        if (
            currentValue[0] === 'Yearly' &&
            moment(today).isAfter(currentValue[1]?.date, 'year')
        ) {
            return 0
        }
        return currentValue[1]?.accrued
    }

    const getAccruedMessage = () => {
        if (depositLimits?.length) {
            return depositLimits.reduce(
                (previousValue: any, currentValue: any) => {
                    return (
                        previousValue +
                        `You already deposit a total of ${numeral(
                            setLimitValue(currentValue)
                        ).format('$0,0.00')} from your ${numeral(
                            currentValue[1]?.limit
                        ).format(
                            '$0,0.00'
                        )} ${currentValue[0].toLowerCase()} limit. `
                    )
                },
                ''
            )
        }
        return ''
    }

    return depositLimits && depositLimits.length ? (
        <p className="depositpage__form__message">{getAccruedMessage()}</p>
    ) : null
}
