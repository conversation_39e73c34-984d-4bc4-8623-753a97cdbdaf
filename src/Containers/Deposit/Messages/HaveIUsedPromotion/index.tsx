import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'

import { promoValidator } from 'Containers/Deposit/utils/promovalidator.utils'
import { PromoCalculation } from 'Containers/Deposit/utils/promoCalculations.utils'

export default function HaveIUsedPromotion({
    haveIUsedPromotion,
    promo,
    requestedAmount,
    isDiscountTypePromo
}: {
    haveIUsedPromotion: boolean
    promo: CreateEvent<Promotion, Promotion.Key, string>
    requestedAmount: string
    isDiscountTypePromo: boolean
}) {
    const maxValueForDisplayDeposit = 11

    return haveIUsedPromotion ? (
        <p className="depositpage__form__message">
            You have already used that promotion.
        </p>
    ) : promo &&
      Number(requestedAmount) >= 20 &&
      requestedAmount?.length < maxValueForDisplayDeposit &&
      promoValidator(promo?.payload?.config, requestedAmount) &&
      !isDiscountTypePromo &&
      requestedAmount?.length < maxValueForDisplayDeposit ? (
        <p className="depositpage__form__message">
            {PromoCalculation(promo?.payload?.config, requestedAmount)}
        </p>
    ) : null
}
