import React from 'react'
import { useTranslation } from 'react-i18next'

export default function FormTitle({
    isFirstDeposit
}: {
    isFirstDeposit: boolean
}) {
    const { t } = useTranslation()
    return isFirstDeposit ? (
        <>
            <h3>{t("FirstDepositTitle")}</h3>
            <p className="depositpage__form__p">
                {t("FirstDepositSubTitle1")}
            </p>
            <p className="depositpage__form__p">
                {t("FirstDepositSubTitle2")}
            </p>
        </>
    ) : (
        <h3> {t("DepositTitle")}</h3>
    )
}
