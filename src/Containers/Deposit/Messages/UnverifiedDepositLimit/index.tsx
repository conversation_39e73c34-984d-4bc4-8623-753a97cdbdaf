import React from 'react'
import numeral from 'numeral'

import { checkIfLimitForUnverifiedIsPassed } from 'Containers/Deposit/utils/checkIfLimitForUnverifiedIsPassed.utils'

export default function UnverifiedDepositLimitMessage({
    requestedAmount,
    status,
    totalAccountBalance,
    unverifiedDepositLimit,
    pendingDepositsSum,
}: {
    requestedAmount: string
    status: string | undefined
    totalAccountBalance: number
    unverifiedDepositLimit: number,
    pendingDepositsSum: number
}) {

    if (checkIfLimitForUnverifiedIsPassed(
        requestedAmount,
        status,
        totalAccountBalance,
        unverifiedDepositLimit,
        pendingDepositsSum
    )) {
        const hasReachedTotal = unverifiedDepositLimit - totalAccountBalance - pendingDepositsSum === 0
        if (totalAccountBalance === 0 && pendingDepositsSum === 0) {
            return <p className="depositpage__form__message">Unverified users can have a maximum of {numeral(unverifiedDepositLimit).format('$0,0.00')} deposited in the account</p>
        }
        if (hasReachedTotal) {
            return <p className="depositpage__form__message">
                The total funds in your account are {numeral(totalAccountBalance).format('$0,0.00')}. You have pending deposits with the amount of {numeral(pendingDepositsSum).format('$0,0.00')}. Unverified users are able to deposit up to a total of ${numeral(unverifiedDepositLimit).format('$0,0.00')}, if you wish to deposit more, please verify your account.
            </p>
        }
        return <p className="depositpage__form__message">
            The total funds in your account are {numeral(totalAccountBalance).format('$0,0.00')}. You have pending deposits totalizing an amount of {numeral(pendingDepositsSum).format('$0,0.00')}. You are able to deposit {numeral(unverifiedDepositLimit - totalAccountBalance - pendingDepositsSum).format('$0,0.00')}
        </p>
    }

    return null
}
