import numeral from 'numeral'

const finalCalculationCash = (baseValue: any, promoValue: any) =>
    Number(baseValue) + Number(promoValue.value.value)

const finalCalculationPercentageBonus = (
    promotionResultMaxAmount: any,
    baseValue: any,
    promoValue: any
) => {
    if (promotionResultMaxAmount) {
        return Number(baseValue) * Number(promoValue.value.value) <=
            Number(promotionResultMaxAmount)
            ? Number(baseValue) +
                  Number(baseValue) * Number(promoValue.value.value)
            : Number(baseValue) + Number(promotionResultMaxAmount)
    }
    return (
        Number(baseValue) + Number(baseValue) * Number(promoValue.value.value)
    )
}

const bonusCalculation = (
    promotionResultMaxAmount: any,
    baseValue: any,
    promoValue: any
) => {
    if (promotionResultMaxAmount) {
        return Number(baseValue) * Number(promoValue.value.value) <=
            Number(promotionResultMaxAmount)
            ? Number(baseValue) * Number(promoValue.value.value)
            : Number(promotionResultMaxAmount)
    }
    return Number(baseValue) * Number(promoValue.value.value)
}

const percentageOrCash = (
    promoValue: any,
    baseValue: any,
    promoConfig: any
) => {
    const { maxAmount: promotionResultMaxAmount } = promoConfig

    if (promoValue.value.tag === 'Cash') {
        return `You will receive a bonus of ${numeral(
            Number(promoValue.value.value)
        ).format(
            '$0,0.00'
        )} by applying this promotion. In total you will receive ${numeral(
            finalCalculationCash(baseValue, promoValue)
        ).format('$0,0.00')} on your account.`
    } else if (promoValue.value.tag === 'Percentage') {
        return `You will receive a bonus of ${numeral(
            bonusCalculation(promotionResultMaxAmount, baseValue, promoValue)
        ).format(
            '$0,0.00'
        )}  by applying this promotion. In total you will receive ${numeral(
            finalCalculationPercentageBonus(
                promotionResultMaxAmount,
                baseValue,
                promoValue
            )
        ).format('$0,0.00')} on your account`
    }
}

export function PromoCalculation(promoConfig: any, actionValue: any) {
    const calcValue = () => {
        switch (promoConfig?.action?.value?.tag) {
            case 'Bonus':
                const val = percentageOrCash(
                    promoConfig?.action?.value,
                    actionValue,
                    promoConfig
                )
                return val
            case 'Discount':
                const val2 = percentageOrCash(
                    promoConfig?.action?.value,
                    actionValue,
                    promoConfig
                )
                return val2
            default:
                return
        }
    }
    return calcValue()
}
