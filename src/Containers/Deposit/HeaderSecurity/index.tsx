import React from 'react'
import Lock from 'Assets/headersecurity/Lock.png'
import Headset from 'Assets/headersecurity/Headset.png'
import PCI from 'Assets/headersecurity/PCI.png'
import { useTranslation } from 'react-i18next'

import './style.scss'

export default function HeaderSecurity() {
    const { t } = useTranslation()
    return (
        <div className="headerSecurity__container">
            <div className="headerSecurity__card">
                <img src={Lock} alt="SSL Secured" />
                <p>SSL {t("DepositSecured")}</p>
            </div>
            <div className="headerSecurity__card">
                <img src={PCI} alt="PCI Secured" />
                <p>PCI {t("DepositSecured")}</p>
            </div>
            <div className="headerSecurity__card">
                <img src={Headset} alt="live support" />
                <p>{t("DepositLiveSupport")}</p>
            </div>
        </div>
    )
}
