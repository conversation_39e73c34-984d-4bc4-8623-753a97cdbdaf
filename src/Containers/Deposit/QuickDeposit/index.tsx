import React from 'react'

export default function QuickDeposit({
    options,
    handleSelectBehaviour,
    isChecked
}: {
    options: { value: string }[]
    handleSelectBehaviour: (value: React.FormEvent<HTMLInputElement>) => void
    isChecked: string
}) {
    return (
        <div className="depositpage__radioWrapper">
            {options.map(option => (
                <div className="depositpage__radioContainer" key={option.value}>
                    <input
                        onClick={e => handleSelectBehaviour(e)}
                        type="radio"
                        value={option.value}
                        checked={Boolean(isChecked === option.value)}
                    />{' '}
                    <label>${option.value}</label>
                </div>
            ))}
        </div>
    )
}
