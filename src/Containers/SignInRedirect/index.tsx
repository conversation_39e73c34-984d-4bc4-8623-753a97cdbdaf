import React from 'react'
import LoaderSpinner from 'Components/Loader'
import useSignupRedirection from 'Hooks/useSignupRedirect'
import { isProd } from 'config'

const SignInRedirect = () => {
    const login = useSignupRedirection()

    React.useEffect(() => {
        if (isProd) {
            // Create the first script element
            const script1 = document.createElement('script')
            script1.type = 'text/javascript'
            script1.id = 'pap_x2s6df8d'
            script1.src =
                'https://thesportsmarket.postaffiliatepro.com/scripts/ezgvmnjf9'
            document.body.appendChild(script1)
            // Create the second script element
            const script2 = document.createElement('script')
            script2.type = 'text/javascript'
            script2.innerHTML = `
           window?.PostAffTracker?.setAccountId('default1');
           try {
               window?.PostAffTracker?.track();
           } catch (err) {}
         `
            document.body.appendChild(script2)
            // Cleanup function to remove scripts on component unmount
            return () => {
                document.body.removeChild(script1)
                document.body.removeChild(script2)
            }
        }
        login()
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    return (
        <>
            <LoaderSpinner />
        </>
    )
}

export default SignInRedirect
