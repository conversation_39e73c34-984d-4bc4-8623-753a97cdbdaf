import { countriesFullList } from 'Constants/countries'
export const setDateBeforeSavingState = (date: Date | string) => {
    let yourDate = new Date(date)
    const offset = yourDate.getTimezoneOffset()
    yourDate = new Date(yourDate.getTime() - offset * 60 * 1000)
    return yourDate.toISOString().split('T')[0]
}

export const convertCountryCodeToName = (code: string) =>
    countriesFullList.filter(c => c.code3 === code)[0]?.name ?? ''

export const convertCountryNameToCode3 = (code: string) =>
    countriesFullList.filter(c => c.name === code)[0]?.code3 ?? ''
