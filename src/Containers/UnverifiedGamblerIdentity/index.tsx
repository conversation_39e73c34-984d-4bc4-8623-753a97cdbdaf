import { useAuth0 } from '@auth0/auth0-react'
import { GamblerUnverifiedIdentity } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { Role as GamblingRole } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Role'
import {
    Request,
    Service
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { useLedger, useParty, useQuery, useStreamQueries } from '@daml/react'
import { logoutParams } from 'Constants/Auth0Constants'
import React from 'react'
import TagManager from 'react-gtm-module'
import { useHistory, useLocation } from 'react-router-dom'
import { toast } from 'react-toastify'

import LoaderSpinner from 'Components/Loader'
import FormSetupData from './Form'

import { isLocalDev, isProd } from 'config'
import useScrollToTop from 'Hooks/useScrollToTop'
import { convertCountryNameToCode3, setDateBeforeSavingState } from './utils'

import { publicContext } from 'Containers/App'
import { signOut, useUserDispatch } from 'State/UserContext'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'
import { logEvent } from 'Utils/analytics'
import './style.scss'

interface IStateType {
    fromDeposit: boolean
    fromWithdraw: boolean
}

export interface IFormChildren {
    formData: ISetupForm
    handleNextStep: (data: ISetupForm, final?: boolean) => void
    handlePrevStep?: (data: ISetupForm) => void
}

export interface ISetupForm {
    email: string | undefined
    country: string | undefined
    firstName: string | undefined
    lastName: string | undefined
    birthday: Date | null | undefined
    city: string | undefined
    postalCode: string | undefined
    subDivision: string | undefined
    addressLine1: string | undefined
}

export default function EmailSetup() {
    const { user, logout } = useAuth0()

    const initFormState = {
        email: user?.email ?? '',
        country: '',
        firstName: user?.given_name ?? '',
        lastName: user?.family_name ?? '',
        birthday: null,
        city: '',
        postalCode: '',
        subDivision: '',
        addressLine1: ''
    }

    const ledger = useLedger()
    const party = useParty()
    const { status } = useVerificationStatusContext()
    const publicParty = publicContext.useParty()
    const publicLedger = publicContext.useLedger()
    const [generalLoader, setGeneralLoader] = React.useState(true)
    const { contracts: serviceContracts, loading: isServiceLoading } =
        useStreamQueries(Service, () => [{ customer: party }], [party])
    const {
        contracts: gamblerUnverifiedIdentity,
        loading: gamblerUnverifiedIdentityLoader
    } = useQuery(
        GamblerUnverifiedIdentity,
        () => {
            return { customer: party }
        },
        [party]
    )

    const history = useHistory()
    const { push } = history
    const userDispatch = useUserDispatch()

    const { state } = useLocation<IStateType>()

    const [formData, setFormData] = React.useState<ISetupForm>(initFormState)
    const [isLoading, setIsLoading] = React.useState(false)

    const handleLogout = () => {
        if (isLocalDev) {
            return signOut(userDispatch, history)
        }
        return logout(logoutParams)
    }

    //ADD START OF GAMBLING ROLE HERE
    const startGamblingServiceRequest = async (provider: string) => {
        ledger
            .create(Request, {
                provider,
                customer: party,
                observers: [publicParty]
            })
            .then(() => {
                logEvent('sign_up_success')
                TagManager.dataLayer({
                    dataLayer: {
                        event: 'sign_up_success'
                    }
                })
                return
            })
            .catch(e => {
                console.error('error on startGamblingServiceRequest', e)
                toast.error('Something went wrong, please try again later')
                return handleLogout()
            })
            .finally(() => setGeneralLoader(false))
    }

    //COPY EFFECT TO UNVERIFIED IDENTITY
    React.useEffect(() => {
        if (!isServiceLoading) {
            publicLedger
                .query(GamblingRole)
                .then(gamblingRoleQuery => {
                    if (
                        status === 'notStarted' &&
                        serviceContracts?.length === 0 &&
                        gamblingRoleQuery.length === 1
                    ) {
                        startGamblingServiceRequest(
                            gamblingRoleQuery[0]?.payload.provider
                        )
                    }
                })
                .catch(error => {
                    console.error('error on queries', error)
                    toast.error('Something went wrong, please try again later')
                    //return logout()
                })
        }
        if (serviceContracts?.length > 0) {
            setGeneralLoader(false)
        }
        //eslint-disable-next-line
    }, [serviceContracts, isServiceLoading])

    //CHANGE HERE TO VIDEO LAST CONDITION
    const handleRedirection = () =>
        state?.fromDeposit
            ? push('/deposit')
            : state?.fromWithdraw
            ? push('/withdrawal')
            : push('/intro')

    const handlePostTradeClick = (email: string) => {
        const sale = (window as any)?.PostAffTracker.createAction('signup')
        sale.setCampaignID('11111111')
        sale.setOrderID(email)
        sale.setProductID('SignUp')
        sale.setTotalCost('0')
        sale.setData1(email)
        ;(window as any)?.PostAffTracker.register()
    }

    const handleConfirm = async () => {
        setIsLoading(true)
        let code3Country = convertCountryNameToCode3(formData.country as string)
        let formatedDate = formData.birthday
            ? setDateBeforeSavingState(formData.birthday)
            : ''
        let phoneNumberToPass =
            gamblerUnverifiedIdentity.length > 0
                ? gamblerUnverifiedIdentity[0].payload.phoneNumber
                : '-'
        ledger
            .exercise(
                Service.RequestUnverifiedIdentity,
                serviceContracts[0]?.contractId,
                {
                    phoneNumber: phoneNumberToPass,
                    addressLine1: formData.addressLine1 as string,
                    firstName: formData.firstName as string,
                    lastName: formData.lastName as string,
                    subDivision: formData.subDivision as string,
                    city: formData.city as string,
                    countryCode: code3Country,
                    emailAddress: formData.email as string,
                    birthday: formatedDate,
                    postalCode: formData.postalCode as string
                }
            )
            .then(() => {
                if (isProd) {
                    if (!document.getElementById('pap_x2s6df8d')) {
                        const script = document.createElement('script')
                        script.type = 'text/javascript'
                        script.id = 'pap_x2s6df8d'
                        script.src =
                            'https://thesportsmarket.postaffiliatepro.com/scripts/ezgvmnjf9'
                        script.onload = () =>
                            handlePostTradeClick(formData.email as string)
                        document.body.appendChild(script)
                    } else {
                        handlePostTradeClick(formData.email as string)
                    }
                }
                handleRedirection()
            })
            .catch(() => handleError())
    }

    const handleCancel = () => {
        setFormData(initFormState)
        setStep(0)
    }

    const handleError = () => {
        toast.error('Something went wrong please try again later')
        setIsLoading(false)
        handleCancel()
    }

    useScrollToTop()

    const HasAlreadyData = () => (
        <>
            <p>You already setup your personal data.</p>
        </>
    )

    const [step, setStep] = React.useState(0)
    const handleNextStep = async (data: ISetupForm, final: boolean = false) => {
        setFormData(prevState => ({ ...prevState, ...data }))
        if (final) {
            return await handleConfirm()
        }
        setStep(prev => prev + 1)
    }

    const handlePreviousStep = (data: ISetupForm) => {
        setFormData(prevState => ({ ...prevState, ...data }))
        setStep(prev => prev - 1)
    }

    const hasUnverifiedIdentityValidation = isLocalDev
        ? !gamblerUnverifiedIdentity.length
        : gamblerUnverifiedIdentity[0]?.payload?.countryCode === '-' ||
          !gamblerUnverifiedIdentity.length

    const HasData = () =>
        !isServiceLoading &&
        !gamblerUnverifiedIdentityLoader &&
        hasUnverifiedIdentityValidation ? (
            <FormSetupData
                data={formData}
                currentStep={step}
                handleNextStep={handleNextStep}
                handlePreviousStep={handlePreviousStep}
            />
        ) : (
            <HasAlreadyData />
        )

    return (
        <div className="unverifiedIdentity">
            <div className="unverifiedIdentity__container">
                {isLoading ||
                isServiceLoading ||
                gamblerUnverifiedIdentityLoader ||
                generalLoader ? (
                    <LoaderSpinner className="unverifiedIdentity__loader" />
                ) : (
                    <HasData />
                )}
            </div>
        </div>
    )
}
