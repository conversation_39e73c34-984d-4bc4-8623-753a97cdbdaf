import React from 'react'
import { Formik } from 'formik'
import * as Yup from 'yup'

import InputForm from 'Components/Inputs'
import ErrorMessage from 'Components/RegisterUser/ErrorMessage'

import { countriesFullList } from 'Constants/countries'
import { IFormChildren } from '../../index'
import { useTranslation } from 'react-i18next'

const strRe =
    /^(?:[A-Za-z]{2,}(?:(\.\s|'s\s|\s?-\s?|\s)?(?=[A-Za-z]+))){1,2}(?:[A-Za-z]+)?$/

const LoginSchema = Yup.object({
    country: Yup.string().required('Country is a required field'),
    city: Yup.string()
        .min(1)
        .max(30)
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .matches(strRe, 'Please add a valid city name')
        .required('City is a required field'),
    postalCode: Yup.string()
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .min(1)
        .max(10, 'Postal Code must be at most 10 characters')
        .required('Postal Code is a required field'),
    subDivision: Yup.string()
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .min(1)
        .max(30)
        .required('State/District is a required field'),
    addressLine1: Yup.string()
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .min(1)
        .max(30)
        .required('Address is a required field')
})

export default function Step2({
    handleNextStep,
    handlePrevStep,
    formData
}: IFormChildren) {
    const { t } = useTranslation()
    return (
        <>
            <Formik
                initialValues={formData}
                onSubmit={values => {
                    handleNextStep(values)
                }}
                validationSchema={LoginSchema}
            >
                {formik => {
                    const shouldDisableButton = Boolean(
                        formik.errors.addressLine1 ||
                        formik.errors.country ||
                        formik.errors.city ||
                        formik.errors.subDivision ||
                        formik.errors.postalCode
                    )

                    return (
                        <form onSubmit={formik.handleSubmit}>
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerCOP')}
                                </label>
                                <InputForm
                                    id="country"
                                    placeholder="Please add your country of payment"
                                    type="selectComplex"
                                    optionsComplex={countriesFullList}
                                    {...formik.getFieldProps('country')}
                                />
                            </div>
                            {formik.errors.country ? (
                                <ErrorMessage message={formik.errors.country} />
                            ) : null}
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerCity')}
                                </label>
                                <InputForm
                                    id="city"
                                    placeholder="Please add your city"
                                    type="text"
                                    {...formik.getFieldProps('city')}
                                />
                            </div>
                            {formik.errors.city ? (
                                <ErrorMessage message={formik.errors.city} />
                            ) : null}
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerIdentityAddress')}
                                </label>
                                <InputForm
                                    id="addressLine1"
                                    placeholder="Please add your address"
                                    type="text"
                                    {...formik.getFieldProps('addressLine1')}
                                />
                            </div>
                            {formik.errors.addressLine1 ? (
                                <ErrorMessage
                                    message={formik.errors.addressLine1}
                                />
                            ) : null}
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerIdentityState')}
                                </label>
                                <InputForm
                                    id="subDivision"
                                    placeholder="Please add your State"
                                    type="text"
                                    {...formik.getFieldProps('subDivision')}
                                />
                            </div>
                            {formik.errors.subDivision ? (
                                <ErrorMessage
                                    message={formik.errors.subDivision}
                                />
                            ) : null}
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerIdentityPostalCode')}
                                </label>
                                <InputForm
                                    id="postalCode"
                                    placeholder="Please add your postal code"
                                    type="text"
                                    {...formik.getFieldProps('postalCode')}
                                />
                            </div>
                            {formik.errors.postalCode ? (
                                <ErrorMessage
                                    message={formik.errors.postalCode}
                                />
                            ) : null}
                            <div className="unverifiedIdentity__action">
                                <button
                                    disabled={shouldDisableButton}
                                    className="registerbtn__nextStep"
                                    type="submit"
                                >
                                    {t('UnverifiedGamblerIdentityNext')}
                                </button>
                                <button
                                    className="registerbtn__previousStep width100"
                                    onClick={() =>
                                        handlePrevStep?.(formik.values)
                                    }
                                >
                                    {t('UnverifiedGamblerIdentityBack')}
                                </button>
                            </div>
                        </form>
                    )
                }}
            </Formik>
        </>
    )
}
