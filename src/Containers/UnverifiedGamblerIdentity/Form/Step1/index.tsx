import React from 'react'
import { useTranslation } from 'react-i18next'
import { Formik, FormikProps } from 'formik'
import DatePicker from 'react-datepicker'
import * as Yup from 'yup'

import InputForm from 'Components/Inputs'
import ErrorMessage from 'Components/RegisterUser/ErrorMessage'

import { IFormChildren, ISetupForm } from '../../index'
import Tooltip from 'Containers/Account/History/BetHistory/BetHistoryTable/Tooltip'

import 'react-datepicker/dist/react-datepicker.css'

// const regexPhoneNumber =
//     // eslint-disable-next-line no-useless-escape
//     /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im

const LoginSchema = Yup.object({
    firstName: Yup.string()
        .min(1)
        .max(30)
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .required('First Name is a required field'),
    lastName: Yup.string()
        .min(1)
        .max(30)
        .trim('This field cannot include leading and trailing spaces')
        .strict(true)
        .required('Last Name is a required  field'),
    birthday: Yup.date()
        .typeError('Please enter a valid date')
        .test('birthday', 'You should be older than 18', function (val) {
            const today = new Date()
            const dob = val ? val : today
            let month_diff = Date.now() - dob.getTime()
            let age_dt = new Date(month_diff)
            let year = age_dt.getUTCFullYear()
            let age = Math.abs(year - 1970)
            return age >= 18
        })
        .required('Birthdate is a required  field'),
    email: Yup.string().email().required('Email is a required  field')
})

const EmailFormInput = ({
    formik,
    hasEmail
}: {
    formik: FormikProps<ISetupForm>
    hasEmail: boolean
}) => {
    if (hasEmail) {
        return (
            <>
                <div className="register__wrapper">
                    <input
                        id="email"
                        placeholder="Please add your email"
                        type="text"
                        className="register__input"
                        disabled
                        {...formik.getFieldProps('email')}
                    />
                </div>
            </>
        )
    }
    return (
        <InputForm
            id="email"
            placeholder="Please add your email"
            type="text"
            {...formik.getFieldProps('email')}
        />
    )
}

export default function Step1({ formData, handleNextStep }: IFormChildren) {
    const hasInitialDataEmail = formData?.email
        ? formData?.email.length > 0
        : false

    const { t } = useTranslation()
    return (
        <>
            <h2>{t('UnverifiedGamblerIdentityHeader')}</h2>
            <p>{t('UnverifiedGamblerIdentityParagraph')}</p>
            <Formik
                initialValues={formData}
                onSubmit={values => {
                    handleNextStep(values)
                }}
                validationSchema={LoginSchema}
            >
                {formik => {
                    const shouldDisableButton = Boolean(
                        formik.errors.email ||
                            formik.errors.firstName ||
                            formik.errors.lastName ||
                            formik.errors.birthday
                    )

                    return (
                        <form onSubmit={formik.handleSubmit}>
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerFirstName')}
                                </label>
                                <InputForm
                                    id="firstName"
                                    placeholder="Please add your first name"
                                    type="text"
                                    {...formik.getFieldProps('firstName')}
                                />
                            </div>
                            {formik.errors.firstName ? (
                                <ErrorMessage
                                    message={formik.errors.firstName}
                                />
                            ) : null}
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerLastName')}
                                </label>
                                <InputForm
                                    id="lastName"
                                    placeholder="Please add your last name"
                                    type="text"
                                    {...formik.getFieldProps('lastName')}
                                />
                            </div>
                            {formik.errors.lastName ? (
                                <ErrorMessage
                                    message={formik.errors.lastName}
                                />
                            ) : null}
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerDOB')}
                                </label>
                                <DatePicker
                                    className="register__input"
                                    name="birthday"
                                    dateFormat="yyyy/MM/dd"
                                    showYearDropdown
                                    scrollableYearDropdown
                                    dropdownMode="select"
                                    onChange={date =>
                                        formik.setFieldValue('birthday', date)
                                    }
                                    selected={
                                        formik.getFieldProps('birthday').value
                                            ? formik.getFieldProps('birthday')
                                                  .value
                                            : null
                                    }
                                    placeholderText="Please insert your birth date (YYYY/MM/DD)"
                                />
                            </div>
                            {formik.errors.birthday ? (
                                <ErrorMessage
                                    message={formik.errors.birthday}
                                />
                            ) : null}
                            <div className="unverifiedIdentity__formInputWrapper">
                                <label className="unverifiedIdentity__label">
                                    {t('UnverifiedGamblerEmail')}{' '}
                                    <Tooltip
                                        topic={t('UnverifiedGamblerEmailInfo')}
                                    />
                                </label>
                                <EmailFormInput
                                    formik={formik}
                                    hasEmail={hasInitialDataEmail}
                                />
                            </div>
                            {formik.errors.email ? (
                                <ErrorMessage message={formik.errors.email} />
                            ) : null}
                            <button
                                disabled={shouldDisableButton}
                                className="registerbtn__nextStep"
                                type="submit"
                            >
                                {t('UnverifiedGamblerIdentityNext')}
                            </button>
                        </form>
                    )
                }}
            </Formik>
        </>
    )
}
