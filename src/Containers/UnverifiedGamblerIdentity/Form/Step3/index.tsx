import React from 'react'
import { IFormChildren } from '../..'

import { setDateBeforeSavingState } from 'Containers/UnverifiedGamblerIdentity/utils'
import { useTranslation } from 'react-i18next'

export default function Step3({
    formData,
    handleNextStep,
    handlePrevStep
}: IFormChildren) {
    const {
        email,
        country,
        firstName,
        lastName,
        birthday,
        city,
        postalCode,
        subDivision,
        addressLine1
    } = formData

    const { t } = useTranslation()

    return (
        <>
            <p className="unverifiedIdentity__title">
                {t('UnverifiedGamblerIdentityConfirmText')}
            </p>
            <div className="unverifiedIdentity__resume">
                <span> {t('UnverifiedGamblerFirstName')}</span>
                <span>{firstName}</span>
            </div>
            <div className="unverifiedIdentity__resume">
                <span>{t('UnverifiedGamblerLastName')}</span>
                <span>{lastName}</span>
            </div>
            <div className="unverifiedIdentity__resume">
                <span>{t('UnverifiedGamblerDOB')}</span>{' '}
                <span>
                    {birthday ? setDateBeforeSavingState(birthday) : ''}
                </span>
            </div>
            <div className="unverifiedIdentity__resume">
                <span>{t('UnverifiedGamblerEmail')}</span>
                <span>{email}</span>
            </div>
            <div className="unverifiedIdentity__resume">
                <span>{t('UnverifiedGamblerCOP')}</span>
                <span>{country}</span>
            </div>
            <div className="unverifiedIdentity__resume">
                <span>{t('UnverifiedGamblerCity')}</span>
                <span>{city}</span>
            </div>
            <div className="unverifiedIdentity__resume">
                <span>{t('UnverifiedGamblerIdentityState')}</span>
                <span>{subDivision}</span>
            </div>
            <div className="unverifiedIdentity__resume">
                <span>{t('UnverifiedGamblerIdentityAddress')}</span>
                <span>{addressLine1}</span>
            </div>
            <div className="unverifiedIdentity__resume">
                <span>{t('UnverifiedGamblerIdentityPostalCode')}</span>
                <span>{postalCode}</span>
            </div>
            <div className="unverifiedIdentity__action">
                <button
                    className="registerbtn__nextStep"
                    onClick={() => handleNextStep(formData, true)}
                >
                    {t('UnverifiedGamblerIdentityConfirmBtn')}
                </button>
                <button
                    className="registerbtn__previousStep width100"
                    onClick={() => handlePrevStep?.(formData)}
                >
                    {t('UnverifiedGamblerIdentityBack')}
                </button>
            </div>
        </>
    )
}
