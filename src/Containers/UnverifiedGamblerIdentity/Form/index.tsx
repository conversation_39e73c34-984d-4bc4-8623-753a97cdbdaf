import React from 'react'
import { ISetupForm } from '..'
import Step1 from './Step1'
import Step2 from './Step2'
import Step3 from './Step3'

export default function FormSetupData({
    data,
    currentStep,
    handleNextStep,
    handlePreviousStep
}: {
    data: ISetupForm
    currentStep: number
    handleNextStep: (data: ISetupForm, final?: boolean) => void
    handlePreviousStep: (data: ISetupForm) => void
}) {
    const steps = [
        <Step1 formData={data} handleNextStep={handleNextStep} />,
        <Step2
            formData={data}
            handleNextStep={handleNextStep}
            handlePrevStep={handlePreviousStep}
        />,
        <Step3
            formData={data}
            handleNextStep={handleNextStep}
            handlePrevStep={handlePreviousStep}
        />
    ]
    return <>{steps[currentStep]}</>
}
