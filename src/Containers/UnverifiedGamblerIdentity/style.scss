@import '../../Styles/colors';

.unverifiedIdentity {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGSignIn.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    font-family: 'Montserrat-Bold', sans-serif;
    gap: 100px;
    min-height: 100vh;
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%;

    &__container {
        background: $white;
        border-radius: 5px;
        padding: 60px;
        max-width: 640px;
        max-height: 832px;

        h2 {
            font-size: 2.2rem;
            line-height: 2.75rem;
            text-align: center;
            margin-bottom: 20px;
        }
    }

    &__formInputWrapper {
        display: flex;
        flex-direction: column;
        margin-top: 10px;

        input {
            margin-top: 10px !important;
        }
    }

    &__label {
        font-size: 0.75rem;
    }

    &__action {
        display: flex;
        flex-direction: column;
        gap: 10px 0;
    }

    &__loader {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__title {
        padding-bottom: 20px;
    }

    &__resume {
        margin: 10px 0;
        display: flex;
        text-align: baseline;
        justify-content: flex-start;
        gap: 20px;

        span {
            flex: 1 1 50%;
        }
    }

    .react-datepicker__time-container {
        .react-datepicker__time {
            .react-datepicker__time-box {
                ul.react-datepicker__time-list {
                    li.react-datepicker__time-list-item {
                        &--selected {
                            background-color: $purple;

                            &:hover {
                                background-color: $purple;
                            }
                        }
                    }
                }
            }
        }
    }

    .react-datepicker__month,
    .react-datepicker__quarter {
        &--selected,
        &--in-selecting-range,
        &--in-range {
            background-color: $purple;

            &:hover {
                background-color: darken($purple, 5%);
            }
        }
    }

    .react-datepicker__day,
    .react-datepicker__month-text,
    .react-datepicker__quarter-text,
    .react-datepicker__year-text {
        &--selected,
        &--in-selecting-range,
        &--in-range {
            background-color: $purple;

            &:hover {
                background-color: darken($purple, 5%);
            }
        }

        &--keyboard-selected {
            background-color: lighten($purple, 10%);

            &:hover {
                background-color: darken($purple, 5%);
            }
        }

        &--in-selecting-range:not(&--in-range) {
            background-color: rgba($purple, 0.5);
        }

        &--in-range:not(&--in-selecting-range) {
            .react-datepicker__month--selecting-range & {
                background-color: $purple;
            }
        }
    }

    .react-datepicker__month-text,
    .react-datepicker__quarter-text {
        &.react-datepicker__month--selected,
        &.react-datepicker__month--in-range,
        &.react-datepicker__quarter--selected,
        &.react-datepicker__quarter--in-range {
            &:hover {
                background-color: $purple;
            }
        }
    }
}

@media (max-width: 1020px) {
    .unverifiedIdentity {
        &__container {
            padding: 40px;
            max-width: 470px;
        }
    }
}

@media (max-width: 500px) {
    .unverifiedIdentity {
        &__container {
            padding: 30px;
            max-width: 370px;
        }
    }
}
