import { Account } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { WithdrawRequest } from '@daml.js/moneymatrix-integration/lib/MoneyMatrixIntegration/Withdraw'
import { useLedger, useParty, useQuery, useStreamQueries } from '@daml/react'
import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { v4 as uuidv4 } from 'uuid'

import PromotionsDropdown from 'Components/PromotionsDropdown'
import { useSelectedPromotionContext } from 'State/SelectPromotion'
import { useVerificationStatusContext } from 'State/VerificationStatusProvider'

import { PromotionWallet } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import CloseXPopup from 'Components/CloseXPopup'
import LoaderSpinner from 'Components/Loader'
import PaymentIntegrationIframe from 'Components/PaymentIntegrationIframe'
import { haveIUsedPromo } from 'Components/PromotionsDropdown/promotion.helper'
import useIsAuthenticated from 'Hooks/useAuth'
import './style.scss'
import WithdrawalForm from './WithdrawalForm'

import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { publicContext } from 'Containers/App'
import { useI18LanguageContext } from 'State/LanguageState'
import { logEvent } from 'Utils/analytics'
import { generateLocaleForPaymentIntegration } from 'Utils/generateLocaleForPaymentIntegration'
import WithdrawalMessages from './Messages'

export default function Withdrawal() {
    const isAuthenticated = useIsAuthenticated()
    const { lang } = useI18LanguageContext()
    const { push } = useNavigate()
    const [isLoading, setIsloading] = useState(false)
    const [hasUserClickedWithdraw, setHasUserClickedWithdraw] = useState(false)
    const partyLedger = useParty()
    const withdrawalLedger = useLedger()
    const [selectedPromotion, setSelectedPromotion] =
        useSelectedPromotionContext()
    const [promo, setPromo] = useState(() =>
        selectedPromotion?.payload.config.action.tag === 'Withdrawal'
            ? selectedPromotion
            : null
    )

    const { contracts: accountContracts, loading: accountLoading } = useQuery(
        Account,
        () => {
            return { customer: partyLedger }
        },
        [partyLedger]
    )

    const { contracts: gamblingServiceQuery, loading: gamblingServiceLoader } =
        useQuery(
            Service,
            () => {
                return { customer: partyLedger }
            },
            [partyLedger]
        )

    const {
        contracts: globalGamblingConfigsContract,
        loading: loadingGlobalGamblingConfigsContract
    } = publicContext.useQuery(GlobalGamblingConfiguration)

    const { contracts, loading } = useQuery(
        PromotionWallet,
        () => {
            return {
                customer: partyLedger
            }
        },
        [partyLedger]
    )

    const { contracts: existingURLContract, loading: existingURLLoading } =
        useStreamQueries(WithdrawRequest, () => [{ client: partyLedger }], [
            partyLedger
        ])

    const [cashierURL, setCashierUrl] = React.useState('')

    function getUrl(uuid: string) {
        let withdrawRequestQuery = withdrawalLedger.streamQueries(
            WithdrawRequest,
            [{ transactionId: uuid }]
        )
        withdrawRequestQuery.on('change', response => {
            const url = response[0]?.payload.cashierURL
            if (!response.length) {
                setIsloading(false)
                return navigate('/');
            }
            if (window.location.pathname !== '/withdrawal') {
                return withdrawRequestQuery.close()
            }
            setCashierUrl(url)
            withdrawRequestQuery.close()
        })
        withdrawRequestQuery.on('close', () => {
            setIsloading(false)
        })
    }

    const initWithdrawal = async (values: {
        requestedAmount: string
        country: string
    }) => {
        setIsloading(true)
        if (!gamblingServiceQuery.length) {
            setIsloading(false)
            return
        }
        const paymentIntegrationpParty =
            globalGamblingConfigsContract[0]?.payload?.integrationParties
                .entriesArray()
                .filter(a => a[0] === 'moneyMatrix')[0][1]
        const uuid = uuidv4()
        const language = generateLocaleForPaymentIntegration(lang)
        const requestConstructor = promo
            ? {
                  integrationParty: paymentIntegrationpParty,
                  requestedAmount: values.requestedAmount,
                  currency: 'USD',
                  country: values.country,
                  transactionId: uuid,
                  promotion: promo?.key,
                  language
              }
            : {
                  integrationParty: paymentIntegrationpParty,
                  requestedAmount: values.requestedAmount,
                  currency: 'USD',
                  country: values.country,
                  transactionId: uuid,
                  promotion: null,
                  language
              }
        try {
            await withdrawalLedger.exercise(
                Service.RequestWithdrawAccount,
                gamblingServiceQuery[0].contractId,
                requestConstructor
            )
            setSelectedPromotion(null)
            setPromo(null)
            setHasUserClickedWithdraw(true)
            logEvent('withdrawal')
            getUrl(uuid)
        } catch (e) {
            console.error('error', e)
            setIsloading(false)
            setHasUserClickedWithdraw(false)
        }
    }

    const { status, loading: loadingStatus } = useVerificationStatusContext()

    const isNotVerified = status === 'failed' || status === 'notStarted'
    const isVerifyingPending = status === 'pending'

    const conditionToRenderLoader =
        accountLoading ||
        gamblingServiceLoader ||
        loading ||
        loadingStatus ||
        isLoading ||
        loadingGlobalGamblingConfigsContract ||
        existingURLLoading

    const WithdrawForm = () =>
        status === 'success' ? (
            <div className="withdrawpage__form">
                <WithdrawalForm
                    handleSubmit={initWithdrawal}
                    promo={promo}
                    PromoComponent={
                        isAuthenticated && (
                            <PromotionsDropdown
                                styleClass="depositpage__promo"
                                promoFilter="Withdrawal"
                                defaultSelected={selectedPromotion}
                                showInfoIcon
                                onValueChange={setPromo}
                                serviceContracts={gamblingServiceQuery}
                                serviceLoading={gamblingServiceLoader}
                                promotionWalletContracts={contracts}
                                isLoadingPromoWallet={loading}
                            />
                        )
                    }
                    haveIUsedPromotion={haveIUsedPromo(contracts[0], promo)}
                    existingURLContract={existingURLContract}
                    globalGamblingConfigsContract={
                        globalGamblingConfigsContract
                    }
                    accountContracts={accountContracts}
                />
            </div>
        ) : null

    const loaderOrWithdraw = () => {
        if (conditionToRenderLoader) {
            return <LoaderSpinner className="depositpage__loader" />
        }
        return <WithdrawForm />
    }

    const renderIFrame = () => {
        if (cashierURL.length > 0) {
            return (
                <div className="withdrawpage__form">
                    <PaymentIntegrationIframe iframeUrl={cashierURL} />
                </div>
            )
        }
        return <LoaderSpinner className="depositpage__loader" />
    }

    const withdrawFormOrIframe = () => {
        if (hasUserClickedWithdraw) {
            return renderIFrame()
        } else {
            return loaderOrWithdraw()
        }
    }

    return (
        <div className="withdrawpage">
            <div className="withdrawpage__container">
                <CloseXPopup className="withdrawclose" path="/" />
                {withdrawFormOrIframe()}
                {conditionToRenderLoader ? null : (
                    <WithdrawalMessages
                        status={status}
                        isNotVerified={isNotVerified}
                        isVerifyingPending={isVerifyingPending}
                    />
                )}
            </div>
        </div>
    )
}
