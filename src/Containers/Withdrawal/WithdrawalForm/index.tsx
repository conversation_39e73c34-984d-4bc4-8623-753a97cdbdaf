import React from 'react'
import { Account } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import { Formik, FormikValues } from 'formik'
import * as Yup from 'yup'
import CurrencyInput from 'react-currency-input-field'
import numeral from 'numeral'
import { Link } from 'react-router-dom'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'

import { CreateEvent } from '@daml/ledger'
import { WithdrawRequest } from '@daml.js/moneymatrix-integration/lib/MoneyMatrixIntegration/Withdraw'
import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { useTranslation } from 'react-i18next'

function promoValidator(promo: any, baseValue: any) {
    const { endDate, minAmount } = promo
    const now = Date.now()
    if (endDate && endDate <= now) return true
    if (minAmount) {
        if (Number(baseValue) >= Number(minAmount)) return true
        return false
    }
    return true
}

function PromoCalculation(promoConfig: any, actionValue: any, baseFee: any) {
    const percentageOrCash = (
        promoValue: any,
        isBonus: boolean,
        baseValue: any
    ) => {
        const { maxAmount } = promoConfig

        const bonusCalculation = () => {
            if (maxAmount) {
                return Number(baseValue) * Number(promoValue.value.value) <=
                    Number(maxAmount)
                    ? Number(baseValue) * Number(promoValue.value.value)
                    : Number(maxAmount)
            }
            return Number(baseValue) * Number(promoValue.value.value)
        }

        if (promoValue.value.tag === 'Cash') {
            return `You will receive a bonus of ${numeral(
                Number(promoValue.value.value)
            ).format('$0,0.00')} by applying this promotion.`
        } else if (promoValue.value.tag === 'Percentage') {
            if (isBonus)
                return `You will receive a bonus of ${numeral(
                    bonusCalculation()
                ).format('$0,0.00')}  by applying this promotion.`

            return `You will receive a bonus of ${numeral(
                bonusCalculation()
            ).format('$0,0.00')} by applying this promotion.`
        }
    }
    const calcValue = () => {
        switch (promoConfig?.action?.value?.tag) {
            case 'Bonus':
                const val = percentageOrCash(
                    promoConfig?.action?.value,
                    true,
                    actionValue
                )
                return val
            case 'Discount':
                const val2 = percentageOrCash(
                    promoConfig?.action?.value,
                    false,
                    actionValue
                )
                return val2
            default:
                return
        }
    }
    return calcValue()
}

export default function WithdrawalForm({
    handleSubmit,
    PromoComponent,
    promo,
    haveIUsedPromotion,
    existingURLContract,
    globalGamblingConfigsContract,
    accountContracts
}: {
    handleSubmit: (values: {
        requestedAmount: string
        country: string
    }) => Promise<void>
    PromoComponent?: React.ReactNode
    promo?: any | null
    haveIUsedPromotion: boolean
    //isAnyWithdrawInProgress: boolean
    existingURLContract: readonly CreateEvent<
        WithdrawRequest,
        WithdrawRequest.Key,
        string
    >[]
    globalGamblingConfigsContract: readonly CreateEvent<
        GlobalGamblingConfiguration,
        GlobalGamblingConfiguration.Key,
        string
    >[]
    accountContracts: readonly CreateEvent<
        Account,
        Account.Key,
        string
    >[]
}) {
    const { t } = useTranslation()
    function checkIfDisabled(formik: FormikValues) {
        return promo
            ? Boolean(
                formik.errors.country ||
                formik.values?.requestedAmount?.length === 0 ||
                formik.errors.requestedAmount ||
                !promoValidator(
                    promo?.payload?.config,
                    formik.values.requestedAmount
                ) ||
                haveIUsedPromotion ||
                existingURLContract.length > 0
                //|| isAnyWithdrawInProgress 
            )
            : Boolean(
                formik.errors.country ||
                formik.values?.requestedAmount?.length === 0 ||
                formik.errors.requestedAmount ||
                existingURLContract.length > 0
                //|| isAnyWithdrawInProgress 
            )
    }
    const isDiscountTypePromo = promo?.payload?.config?.action.value.tag

    let fee = globalGamblingConfigsContract[0]?.payload.withdrawFee
    let minWithdrawAmount =
        globalGamblingConfigsContract[0]?.payload?.minWithdrawAmount

    function feeCalculation(value: string) {
        return Number(value) - Number(fee) * Number(value)
    }

    const validationSchema = Yup.object({
        requestedAmount: Yup.string()
            .max(10, 'The withdraw input accepts only till 10 digits.')
            .test(
                '__testforamount',
                `Minimum withdrawal value is ${numeral(
                    minWithdrawAmount
                ).format('$0,0.00')}`,
                vl => {
                    return Number(vl) >= Number(minWithdrawAmount)
                }
            )
            .test(
                '__testforamountBigger',
                `Maximum withdrawal value is ${numeral(
                    accountContracts[0]?.payload.totalMainBalance
                ).format('$0,0.00')}`,
                vl => {
                    return (
                        Number(vl) <=
                        Number(accountContracts[0]?.payload.totalMainBalance)
                    )
                }
            )
            .required()
    })

    return (
        <>
            <Formik
                initialValues={{
                    requestedAmount: ''
                }}
                onSubmit={({ requestedAmount }, { resetForm }) => {
                    handleSubmit({
                        requestedAmount,
                        country: ''
                    })
                    resetForm()
                }}
                validationSchema={validationSchema}
            >
                {formik => (
                    <form
                        className="contact__form"
                        onSubmit={formik.handleSubmit}
                    >
                        <h3>{t("WidthrawTitle")}</h3>
                        <CurrencyInput
                            intlConfig={{
                                locale: 'en-US',
                                currency: 'USD'
                            }}
                            id="requestedAmount"
                            name="requestedAmount"
                            placeholder={`${t("AmountToWithdraw")}. ${numeral(
                                minWithdrawAmount
                            ).format('$0,0.00')}`}
                            defaultValue={20}
                            decimalsLimit={2}
                            allowNegativeValue={false}
                            step={1}
                            value={formik.values.requestedAmount}
                            onValueChange={value => {
                                formik.setFieldTouched(
                                    'requestedAmount',
                                    true
                                )
                                formik.setFieldValue(
                                    'requestedAmount',
                                    value
                                )
                            }}
                            prefix="$"
                            className="register__input"
                        />
                        {Number(accountContracts[0]?.payload.totalMainBalance) ===
                            0 ? (
                            <ErrorMessage
                                message={
                                    'You don´t have enough money to withdraw'
                                }
                            />
                        ) : formik.touched.requestedAmount &&
                            formik.errors.requestedAmount ? (
                            <ErrorMessage
                                message={formik.errors.requestedAmount}
                            />
                        ) : null}
                        {PromoComponent}
                        <button
                            type="submit"
                            className="btn btn__green"
                            disabled={checkIfDisabled(formik)}
                        >
                            {t("WidthrawBTN")}
                        </button>
                        {formik.values?.requestedAmount?.length <= 0 ||
                            formik.values?.requestedAmount === '0' ||
                            formik.values?.requestedAmount === undefined ||
                            Number(formik.values?.requestedAmount) < Number(minWithdrawAmount)
                            ? ''
                            : (!haveIUsedPromotion ||
                                !isDiscountTypePromo) && (
                                <p className="withdrawpage__form__message">
                                    {t("WithdrawChargesMessage1")}
                                    {' '}
                                    {numeral(
                                        feeCalculation(
                                            formik.values.requestedAmount
                                        )
                                    ).format('$0,0.00')}{' '}
                                    {t("WithdrawChargesMessage2")}{' '}
                                    {Number(fee) * 100} {t("WithdrawChargesMessage3")}{' '}
                                </p>
                            )}
                        {existingURLContract?.length > 0 ? (
                            <p className="depositpage__form__message">
                                {t("WidthrawInP")}{" "}<Link to="/account/payments">{t("WidthrawInP2")}</Link>
                            </p>
                        ) : null}
                        {haveIUsedPromotion ? (
                            <p className="depositpage__form__message">
                                You have already used that promotion.
                            </p>
                        ) : (
                            promo &&
                            Number(formik.values.requestedAmount) >= 20 &&
                            promoValidator(
                                promo?.payload?.config,
                                formik.values.requestedAmount
                            ) && (
                                <p className="depositpage__form__message">
                                    {PromoCalculation(
                                        promo?.payload?.config,
                                        formik.values?.requestedAmount,
                                        fee
                                    )}
                                </p>
                            )
                        )}
                    </form>
                )}
            </Formik>
        </>
    )
}
