import React from 'react'
import HeaderSecurity from 'Containers/Deposit/HeaderSecurity'
import PopularPayments from 'Containers/Deposit/PopularPayments'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'


export default function WithdrawalMessages({
    status,
    isNotVerified,
    isVerifyingPending
}: {
    status: string | undefined,
    isNotVerified: boolean,
    isVerifyingPending: boolean
}) {
    const { t } = useTranslation()
    return (
        <div className="withdrawpage__info">
            <HeaderSecurity />
            {status === 'success' && (
                <p>
                    {t("WidthrawMessagesP1")}
                </p>
            )}
            {(isNotVerified || isVerifyingPending) && (
                <>
                    <p>
                        <b>Specifics:</b>
                    </p>
                    <p>
                        {t("WidthrawMessagesP2")}
                    </p>
                </>
            )}
            {isNotVerified && (
                <p>
                    {t("WidthrawMessagesP3a1")}{" "}<Link to="/account">{t("WidthrawMessagesP3a2")}</Link>{" "}
                    {t("WidthrawMessagesP3a3")}
                </p>
            )}
            {isVerifyingPending && (
                <p>{t("WidthrawMessagesP4")}</p>
            )}
            <PopularPayments />
        </div>
    )
}
