@import '../../Styles/colors';

.withdrawclose {
    position: absolute;
    top: 20px;
    right: 25px;
    cursor: pointer;
    color: $gray;
    font-size: 1.2rem;
    font-weight: bold;
}

.withdrawpage {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGFirstDeposit.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;

    &__container {
        color: $darkGrey;
        text-align: center;
        line-height: 1.7rem;
        font-size: 1.15rem;
        display: flex;
        gap: 30px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        background: $white;
        width: 90%;
        padding: 40px 30px;
        border-radius: 6px;
        position: relative;

        p + p {
            margin-bottom: 15px;
        }

         @media (max-width: 1900px) {
            max-height: 800px;
            overflow-y: scroll;
        }
    }

    &__info {
        flex: 1 1 400px;

        p {
            font-size: 0.95rem;

            a {
                text-decoration: none;
                color: $purple;
                font-weight: bold;
            }
        }

        p + p {
            margin-top: 10px;
        }
    }

    &__form {
        flex: 1 1 400px;
        text-align: left;
        max-height: 750px;

        h3 {
            font-family: 'Montserrat', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 2.188rem;
            line-height: 2.75rem;
        }

        &__p {
            font-family: 'Montserrat', sans-serif;
            font-style: normal;
            font-weight: bold;
            font-size: 0.813rem;
            line-height: 1.25rem;
            letter-spacing: 0.063rem;
            text-transform: uppercase;
        }

        &__message {
            font-size: 0.9rem;
            font-weight: 400;
            color: $darkGrey;
            text-align: center;
        }

        button {
            width: 100%;
            margin: 20px 0 0 0;
        }
    }

    &__radio {
        accent-color: $purple;
        margin: 20px 0px;

        p {
            margin-bottom: 20px;
        }

        label {
            margin-right: 20px;
        }
    }

    &__icon {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    &__loader {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
