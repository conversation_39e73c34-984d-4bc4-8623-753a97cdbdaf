import React from 'react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { publicContext } from 'Containers/App'

import Event from 'Components/Event'
import LoaderSpinner from 'Components/Loader'

import Carousel from 'Components/Dashboard/Carousel'
import useScrollToTop from 'Hooks/useScrollToTop'
import { useParams } from 'react-router-dom'
import { prepareSubmarketPath } from 'Utils/getPathFromSubmarket'
import BreadcrumbsForEventPages from 'Components/BreadcrumbsForEventPages'

export default function FeaturedEvent() {
    const { market, code, tournament } =
        useParams<{ market: string; code: string; tournament: string }>()
    const codeToQuery = React.useMemo(
        () => (market === 'Sport' || market === 'Other_Market' ? code : {}),
        [code, market]
    )
    useScrollToTop()

    const { contracts: eventContracts, loading: eventLoading } =
        publicContext.useStreamQueries(
            EventInstrument,
            () => {
                return [
                    {
                        status: 'Active',
                        details: {
                            eventStatus: 'NotStarted',
                            market: { tag: market, value: codeToQuery }
                        },
                        featured: true
                    } as EventInstrument
                ]
            },
            [market, codeToQuery]
        )

    const filteredEventContracts = eventContracts.filter(
        evt =>
            prepareSubmarketPath(evt.payload.details.submarkets[0].value) ===
            tournament
    )

    const EventRenderChecker = !eventLoading

    if (!EventRenderChecker) {
        return <LoaderSpinner />
    }

    return (
        <>
            <Carousel />
            <div className="dashboard__wrapper">
                <div className="dashboard__card">
                    <BreadcrumbsForEventPages
                        event={filteredEventContracts}
                        levelOfBreadcrumbs="tournament"
                    />
                    {React.Children.toArray(
                        filteredEventContracts.map(description => (
                            <Event event={description} />
                        ))
                    )}
                    {!filteredEventContracts.length ? (
                        <span>There no available events at the moment.</span>
                    ) : null}
                </div>
            </div>
        </>
    )
}
