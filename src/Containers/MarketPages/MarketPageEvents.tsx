import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import LoaderSpinner from 'Components/Loader'
import Event from 'Components/Event'
import useInfiniteScroll from 'Hooks/useInfiniteScroll'

import './style.scss'

export default function MarketPageEvents({
    eventContracts,
    eventLoading
}: {
    eventContracts: readonly CreateEvent<
        EventInstrument,
        EventInstrument.Key,
        string
    >[]
    eventLoading: boolean
}) {
    const { visibleAmount, setElement } = useInfiniteScroll(5)

    const EventRenderChecker = !eventLoading

    if (!EventRenderChecker) {
        return <LoaderSpinner />
    }

    return (
        <div className="dashboard__card">
            {React.Children.toArray(
                eventContracts
                    .slice(0, visibleAmount)
                    .map(description => <Event event={description} />)
            )}
            {eventContracts.length ? (
                <span ref={setElement} style={{ background: 'transparent' }} />
            ) : null}
            {!eventContracts.length ? (
                <span>There no available events at the moment.</span>
            ) : null}
        </div>
    )
}
