@import '../../Styles/colors';

.marketPage {
    padding-top: 20px;
    &__promo {
        width: 100%;
        margin-bottom: 20px;
    }

    &__header {
        word-break: break-word !important;
    }

    &__add {
        background-color: black;
        max-height: 200px;
        margin-bottom: 20px;

        img {
            width: 100%;
            height: 100%;
            max-width: 1103px;
            max-height: 200px;
            object-fit: cover;
        }
    }
    h1 {
        text-transform: capitalize;
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: bold;
        font-size: 2.8rem;
        line-height: 3.1rem;
        color: $darkGrey;
        display: flex;
        width: 100%;
        align-items: center;
        button {
            background: transparent;
            border: none;
            cursor: pointer;
            margin-left: auto;
        }
        svg {
            color: $purple;
            fill: $purple;
            width: 1em;
            margin-right: 1rem;
            &.favorite {
                color: $purple;
                fill: $purple;
                width: 1.5rem;
                height: 1.5rem;
                margin-right: 0;
            }
            &.notFavorite {
                color: $gray;
                fill: $gray;
                width: 1.5rem;
                height: 1.5rem;
                margin-right: 0;
            }
        }
    }

    &__select {
        border: 1px solid $grayInpuBorder;
        border-radius: 6px;
        color: $black;
        font-family: 'Open Sans', sans-serif;
        font-size: 1.25rem;
        font-style: normal;
        font-weight: normal;
        height: 40px;
        line-height: 1.25rem;
        padding: 0 20px;
        width: 100%;
        margin-top: 20px;
        transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        display: none;
        @media (max-width: 600px) {
            display: block;
        }

        &:focus {
            border-color: $blueShaddow;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
        }
    }
}
