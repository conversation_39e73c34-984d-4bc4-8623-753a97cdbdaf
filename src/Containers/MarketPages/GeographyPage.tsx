import React from 'react'
import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { faTrophy } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { publicContext } from 'Containers/App'
import useScrollToTop from 'Hooks/useScrollToTop'
import { useI18LanguageContext } from 'State/LanguageState'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'
import { useTranslation } from 'react-i18next'

import MarketPageEvents from './MarketPageEvents'
import HeaderMarket from './Shared/HeaderMarket'
import MarketPagesWrapper from './Shared/MarketPagesWrapper'
import { MarketPageAllProps } from './Models'
import {
    setLanguage,
    baseName,
    englishName,
    sportIcon,
    checkIfIsCustom
} from './Utils'

import './style.scss'
import EventFilterTabs from 'Components/EventFilterTabs'
import useFeaturedFilteredEvents from 'Hooks/useFeaturedFilteredEvents'
import useDebounce from 'Hooks/useDebounce'
import useFilterEventByEventTitle from 'Hooks/useFilterEventByEventTitle'
import BreadcrumbsForEventPages from 'Components/BreadcrumbsForEventPages'

export default function GeographyPage({
    market,
    marketInfo,
    geography
}: Omit<MarketPageAllProps, 'league'>) {
    useScrollToTop()
    const [isFeatured, setIsFeatured] = React.useState(false)
    const [debouncedValue, search, setSearch] = useDebounce<string>('', 500)
    const { t } = useTranslation()
    const { lang } = useI18LanguageContext()
    const { BR, MX, US } = useSportTranslationsContext()
    const marketMemo = React.useMemo(() => marketInfo[0], [marketInfo])

    const { contracts: eventContracts, loading: eventLoading } =
        publicContext.useStreamQueries(
            EventInstrument,
            () => {
                return [
                    {
                        status: 'Active',
                        details: {
                            eventStatus: 'NotStarted',
                            market: marketMemo,
                            geography: {
                                tag: 'Geography',
                                value: geography
                            }
                        }
                    } as EventInstrument
                ]
            },
            [marketMemo]
        )
    const filteredByFeatureEvents = useFeaturedFilteredEvents(
        eventContracts,
        isFeatured
    )

    const filterEventsByDebounceValue = useFilterEventByEventTitle(
        filteredByFeatureEvents,
        debouncedValue,
        lang
    )

    const eventsToRender = React.useMemo(() => {
        return debouncedValue
            ? filterEventsByDebounceValue
            : filteredByFeatureEvents
    }, [debouncedValue, filterEventsByDebounceValue, filteredByFeatureEvents])

    const languageArray = setLanguage(lang, US, BR, MX)
    const translatedName = baseName(languageArray, market)
    const englishBaseName = englishName(US, market)
    const sportIconBase = sportIcon(englishBaseName)
    const isCustom = checkIfIsCustom(market, translatedName)

    return (
        <MarketPagesWrapper>
            <BreadcrumbsForEventPages
                event={eventContracts}
                levelOfBreadcrumbs="geography"
            />
            <EventFilterTabs
                isFeatured={isFeatured}
                handleFeatured={setIsFeatured}
                search={search}
                searchCallback={setSearch}
            />
            <HeaderMarket
                children={
                    <>
                        {sportIconBase || <FontAwesomeIcon icon={faTrophy} />}
                        <span>
                            {isCustom === 'Entertainment' ||
                            isCustom === 'Politics'
                                ? t(isCustom)
                                : isCustom}{' '}
                            - {geography}
                        </span>
                    </>
                }
            />
            <MarketPageEvents
                eventContracts={eventsToRender}
                eventLoading={eventLoading}
            />
        </MarketPagesWrapper>
    )
}
