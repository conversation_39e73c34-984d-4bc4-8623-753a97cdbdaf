import React, { useEffect, useState } from 'react'
import { useLedger, useParty } from '@daml/react'
import { Market } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { faStar } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

import { useAccountContex } from 'State/AccountContext'
import { addMarket, removeMarket } from './utils'
import { flatten } from 'Utils/flatten'

export default function FavoriteStar({ marketInfo }: { marketInfo: Market }) {
    const ledger = useLedger()
    const party = useParty()
    const { accountContracts } = useAccountContex()
    const marketQueries = React.useMemo(() => {
        return accountContracts.length > 0
            ? flatten(
                  accountContracts[0]?.payload?.favouriteMarkets?.map?.entriesArray()
              ).filter((element: any) => {
                  return Object.keys(element).length !== 0
              })
            : []
    }, [accountContracts])

    const [isMarketFavorite, setIsMarketFavorite] = useState(false)
    useEffect(() => {
        setIsMarketFavorite(
            marketQueries.some(
                (a: { tag: string; value: string }) =>
                    JSON.stringify(a) === JSON.stringify(marketInfo)
            )
        )
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [marketQueries])

    const toggleIsMarketFavorite = async (market: Market) => {
        if (isMarketFavorite) {
            await removeMarket(market, party, ledger)
        } else {
            await addMarket(market, party, ledger)
        }
    }

    return (
        <button
            onClick={() => toggleIsMarketFavorite(marketInfo)}
            title={
                isMarketFavorite ? 'Remove from favorite' : 'Add to favorite'
            }
        >
            <FontAwesomeIcon
                className={isMarketFavorite ? 'favorite' : 'notFavorite'}
                icon={faStar}
            />
        </button>
    )
}
