import { Market } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import Ledger from '@daml/ledger'
import { toast } from 'react-toastify'

export const addMarket = async (
    payload: Market,
    party: string,
    ledger: Ledger
) => {
    ledger
        .query(Service, {
            customer: party
        })
        .then(contract => {
            ledger
                .exercise(Service.AddFavouriteMarkets, contract[0].contractId, {
                    markets: [payload as Market]
                })
                .catch(e => {
                    console.error(`error fetching service: ${e}`)
                    toast.error('Something went wrong please try again later')
                })
        })
        .catch(e => {
            console.error(`error fetching service: ${e}`)
            toast.error('Something went wrong please try again later')
        })
}

export const removeMarket = async (
    payload: Market,
    party: string,
    ledger: Ledger
) => {
    ledger
        .query(Service, {
            customer: party
        })
        .then(contract =>
            ledger
                .exercise(
                    Service.RemoveFavouriteMarkets,
                    contract[0].contractId,
                    { markets: [payload as Market] }
                )
                .catch(e => {
                    console.error(`error fetching service: ${e}`)
                    toast.error('Something went wrong please try again later')
                })
        )
        .catch(e => {
            console.error(`error fetching service: ${e}`)
            toast.error('Something went wrong please try again later')
        })
}
