import { langArray } from 'State/SportTranslationsContext';
import { sports } from 'Constants/sports'

export const setLanguage = (lang: 'US' | 'BR' | 'MX', US: langArray[], BR: langArray[], MX: langArray[]): langArray[] =>
    lang === 'US' ? US : lang === 'BR' ? BR : lang === 'MX' ? MX : US

export const baseName = (languageArray: langArray[], marketStringCode: string) =>
    languageArray?.length > 0 && languageArray?.filter((a) => a[marketStringCode])[0]

export const englishName = (usLangArray: langArray[], marketStringCode: string) => usLangArray?.length > 0 && usLangArray?.filter((a) => a[marketStringCode])[0]

export const sportIcon = (englishName: langArray | boolean) => {
    if (englishName) {
        return sports[englishName && Object.values(englishName)[0]]?.icon
    }
    return false
}

export const checkIfIsCustom = (market: string, translatedName: langArray | boolean) => {
    if (isNaN(Number(market))) {
        return market
    }
    if (translatedName) {
        return Object.values(translatedName)[0]
    }
    return ""
} 