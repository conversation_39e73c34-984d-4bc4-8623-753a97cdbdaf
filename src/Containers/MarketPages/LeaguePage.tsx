import React from 'react'

import { EventInstrument } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Event/Model'
import { faTrophy } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { publicContext } from 'Containers/App'
import useScrollToTop from 'Hooks/useScrollToTop'
import { useI18LanguageContext } from 'State/LanguageState'
import { useSportTranslationsContext } from 'State/SportTranslationsContext'

import MarketPageEvents from './MarketPageEvents'
import HeaderMarket from './Shared/HeaderMarket'
import MarketPagesWrapper from './Shared/MarketPagesWrapper'
import { MarketPageAllProps } from './Models'
import {
    setLanguage,
    baseName,
    englishName,
    sportIcon,
    checkIfIsCustom
} from './Utils'

import './style.scss'
import EventFilterTabs from 'Components/EventFilterTabs'
import useFeaturedFilteredEvents from 'Hooks/useFeaturedFilteredEvents'
import useFilterEventByEventTitle from 'Hooks/useFilterEventByEventTitle'
import useDebounce from 'Hooks/useDebounce'
import BreadcrumbsForEventPages from 'Components/BreadcrumbsForEventPages'

export default function LeaguePage({
    market,
    marketInfo,
    geography,
    league
}: MarketPageAllProps) {
    useScrollToTop()
    const [isFeatured, setIsFeatured] = React.useState(false)
    const [debouncedValue, search, setSearch] = useDebounce<string>('', 500)
    const { lang } = useI18LanguageContext()
    const { BR, MX, US } = useSportTranslationsContext()

    const marketMemo = React.useMemo(() => marketInfo[0], [marketInfo])

    const { contracts: eventContracts, loading: eventLoading } =
        publicContext.useStreamQueries(
            EventInstrument,
            () => {
                return [
                    {
                        status: 'Active',
                        details: {
                            eventStatus: 'NotStarted',
                            market: marketMemo,
                            geography: {
                                tag: 'Geography',
                                value: geography
                            }
                        }
                    } as EventInstrument
                ]
            },
            [marketMemo]
        )

    const languageArray = setLanguage(lang, US, BR, MX)
    const translatedName = baseName(languageArray, market)
    const englishBaseName = englishName(US, market)
    const sportIconBase = sportIcon(englishBaseName)
    const isCustom = checkIfIsCustom(market, translatedName)

    //Needs to be done like this because the
    //the filter of this data structure
    //on the daml hooks is not working as expected
    let leagueEventContracts = eventContracts.filter(event =>
        event.payload.details.submarkets.find(
            (item: { value: string }) => item.value === league
        )
    )

    const filteredByFeatureEvents = useFeaturedFilteredEvents(
        leagueEventContracts,
        isFeatured
    )

    const filterEventsByDebounceValue = useFilterEventByEventTitle(
        filteredByFeatureEvents,
        debouncedValue,
        lang
    )

    const eventsToRender = React.useMemo(() => {
        return debouncedValue
            ? filterEventsByDebounceValue
            : filteredByFeatureEvents
    }, [debouncedValue, filterEventsByDebounceValue, filteredByFeatureEvents])
    return (
        <MarketPagesWrapper>
            <BreadcrumbsForEventPages
                event={leagueEventContracts}
                levelOfBreadcrumbs="tournament"
            />
            <EventFilterTabs
                isFeatured={isFeatured}
                handleFeatured={setIsFeatured}
                search={search}
                searchCallback={setSearch}
            />
            <HeaderMarket
                children={
                    <>
                        {sportIconBase || <FontAwesomeIcon icon={faTrophy} />}
                        <span>
                            {isCustom} - {league}
                        </span>
                    </>
                }
            />
            <MarketPageEvents
                eventContracts={eventsToRender}
                eventLoading={eventLoading}
            />
        </MarketPagesWrapper>
    )
}
