@import '../../Styles/colors';

.callbackPage {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGRegister.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    font-family: 'Montserrat-Bold', sans-serif;
    min-height: 80vh;
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;

    &__container {
        background: $white;
        border-radius: 5px;
        padding: 40px 60px;
        width: 100%;
        max-width: 500px;
        height: 100%;
        max-height: 450px;

        h2 {
            font-size: 2.2rem;
            line-height: 2.75rem;
            text-align: center;
            margin-bottom: 20px;
        }
    }

    &__text {
        max-height: 90%;
        overflow-y: scroll;
        text-align: justify;

        p {
            margin-bottom: 20px;
        }
    }
}