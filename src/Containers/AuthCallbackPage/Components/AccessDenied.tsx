import React from 'react'
import { useTranslation } from 'react-i18next'

const AccessDenied: React.FC<{}> = () => {
    const { t } = useTranslation()
    return (
        <div className="callbackPage">
            <section className="callbackPage__container">
                <h2>{t('BlockedUserTitle')}</h2>
                <div className="callbackPage__text">
                    <p>
                        {t('BlockedUserMessage1')}{' '}
                        <a
                            href="http://help.gambyl.com/en/support/home"
                            target="_blank"
                            rel="noreferrer"
                        >
                            {t('BlockedUserMessage2')}
                        </a>{' '}
                        {t('BlockedUserMessage3')}
                    </p>
                </div>
            </section>
        </div>
    )
}

export default AccessDenied
