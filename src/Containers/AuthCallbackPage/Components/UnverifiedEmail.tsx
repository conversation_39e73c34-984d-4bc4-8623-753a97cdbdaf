import React from "react";
import {useTranslation} from "react-i18next";

const UnverifiedEmail: React.FC<{}> = () => {
    const {t} = useTranslation()
    return (
        <div className='callbackPage'>
            <section className='callbackPage__container'>
                <h2>
                    📨 {t('Auth0UnverifiedEmailHeader')}
                </h2>
                <div className='callbackPage__text'>
                    <p>
                        {t('Auth0UnverifiedEmailMessage')}
                    </p>
                    <p>
                        {t('Auth0UnverifiedEmailMessage2')}
                    </p>
                    <p>
                        {t('Auth0UnverifiedEmailMessage3')}
                    </p>
                </div>
            </section>
        </div>
    )
}

export default UnverifiedEmail
