import React from 'react'
import { useAuth0 } from '@auth0/auth0-react'
import LoaderSpinner from 'Components/Loader'
import UnverifiedEmail from 'Containers/AuthCallbackPage/Components/UnverifiedEmail'
import Generic from 'Containers/AuthCallbackPage/Components/Generic'
import AccessDenied from 'Containers/AuthCallbackPage/Components/AccessDenied'
import ErrorActionLogin from './Components/ErrorActionLogin'

import './style.scss'

type ErrorMessages =
    | 'UnverifiedEmail'
    | 'BlockedService'
    | 'ErrorActionLogin'
    | 'Generic'

const ErrorMarkdown: Record<ErrorMessages, JSX.Element> = {
    UnverifiedEmail: <UnverifiedEmail />,
    BlockedService: <AccessDenied />,
    ErrorActionLogin: <ErrorActionLogin />,
    Generic: (
        <Generic
            message={'An unexpected error occurred, please try again later.'}
        />
    )
}

export const CallbackPage: React.FC = () => {
    const { error } = useAuth0()
    console.log('error auth0', error)

    const errorKey: ErrorMessages = (
        Object.keys(ErrorMarkdown).includes(error?.message as ErrorMessages)
            ? (error?.message as ErrorMessages)
            : 'Generic'
    ) as ErrorMessages

    console.log('errorKey', errorKey)

    const MessageMarkdown = ErrorMarkdown[errorKey] ?? (
        <Generic message={error?.message} />
    )

    if (error) {
        return MessageMarkdown
    }

    return (
        <>
            <LoaderSpinner />
        </>
    )
}
