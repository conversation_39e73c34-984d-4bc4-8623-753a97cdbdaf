import React from 'react'

import { computeLocalCreds } from 'Utils/credentials'
import { PartyInfo } from '@daml/ledger'

import GenericTable from 'Components/Table/Generic'
import LoaderSpinner from 'Components/Loader'
import CopyClipboard from 'Components/Clipboard'
import { publicContext } from 'Containers/App'
import { adminParties } from 'Utils/partiesUtils'

export default function LocalCreds() {
    const [parties, setParties] = React.useState<PartyInfo[]>([])
    const [loading, setLoading] = React.useState(true)
    const publicLedger = publicContext.useLedger()

    React.useEffect(() => {
        publicLedger
            .listKnownParties()
            .then(partiesQueried => {
                let filteredParties = partiesQueried?.filter(party => {
                    if (
                        party?.displayName &&
                        adminParties.includes(party?.displayName)
                    ) {
                        return party
                    }
                    return null
                })
                setParties(filteredParties)
            })
            .catch(error => {
                setParties([])
                console.error('error fetching parties', error)
            })
            .finally(() => setLoading(false))
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    return loading ? (
        <LoaderSpinner />
    ) : parties.length > 0 ? (
        <div className="pagePadding">
            <GenericTable
                tableHeader={
                    <tr>
                        <th>party</th>
                        <th>token</th>
                    </tr>
                }
                tableBodyRow={React.Children.toArray(
                    parties.map(p => {
                        if (p.displayName) {
                            let { token } = computeLocalCreds(
                                p.identifier,
                                p.displayName
                            )
                            return (
                                <tr>
                                    <td>{p.displayName}</td>
                                    <td>
                                        {token.slice(0, 8)}
                                        ...
                                        <CopyClipboard contenToCopy={token} />
                                    </td>
                                </tr>
                            )
                        }
                        return null
                    })
                )}
            />
        </div>
    ) : (
        <p>no data to display</p>
    )
}
