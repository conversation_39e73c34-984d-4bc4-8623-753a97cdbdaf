@import '../../Styles/colors';

.policies {
    display: flex;
    border-radius: 6px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
    width: 90%;
    max-width: 1500px;
    margin: 20px auto;
    padding: 20px;
    gap: 20px;
    text-transform: capitalize;
    color: $darkGrey;

    &__navigation {
        flex: 1 1 300px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        h1 {
            font-family: Montserrat, sans-serif;
            font-style: normal;
            font-weight: bold;
            line-height: 1.5rem;
            margin: 32px 0 24px;
        }

        ul {
            list-style: none;
        }

        li {
            margin-bottom: 24px;
            font-weight: 600;
        }
    }

    &__active {
        color: $purple;
    }

    &__content {
        flex: 1 1 100%;
    }

    a {
        font-family: OpenSans, sans-serif;
        font-size: 0.938rem;
        line-height: 25px;
        text-decoration: none;
        color: inherit;
    }

    @media (max-width: 1014px) {
        flex-direction: column;

        &__navigation {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            flex: 1;

            ul {
                flex: 0;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                gap: 10px;
                justify-content: flex-start;
            }

            li {
                flex: 1 0 150px;
            }
        }
    }
}
