import React, { useEffect } from 'react'
import { Route, Switch, useLocation } from 'react-router'
import { Link } from 'react-router-dom'

import './style.scss'
import Bounty from './Bounty'
import Cookies from './Cookies'
import Privacy from './Privacy'
import Refer from './Refer'
import WelcomeBonus from './WelcomeBonus'

export default function Policies() {
    const { pathname } = useLocation()
    useEffect(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' })
        return () => {}
    }, [])

    return (
        <div className="policies">
            <div className="policies__navigation">
                <h1>Our policies</h1>
                <ul>
                    <li
                        className={
                            pathname === '/policies/bounty'
                                ? 'policies__active'
                                : ''
                        }
                    >
                        <Link to="/policies/bounty">bounty policy</Link>
                    </li>
                    <li
                        className={
                            pathname === '/policies/cookies'
                                ? 'policies__active'
                                : ''
                        }
                    >
                        <Link to="/policies/cookies">cookies policy</Link>
                    </li>
                    <li
                        className={
                            pathname === '/policies/privacy'
                                ? 'policies__active'
                                : ''
                        }
                    >
                        <Link to="/policies/privacy">privacy policy</Link>
                    </li>
                    <li
                        className={
                            pathname === '/policies/welcome_bonus'
                                ? 'policies__active'
                                : ''
                        }
                    >
                        <Link to="/policies/welcome_bonus">
                            welcome bonus policy
                        </Link>
                    </li>
                </ul>
            </div>
            <div className="policies__content">
                <Switch>
                    <Route exact path="/policies/bounty">
                        <Bounty />
                    </Route>
                    <Route exact path="/policies/cookies">
                        <Cookies />
                    </Route>
                    <Route exact path="/policies/privacy">
                        <Privacy />
                    </Route>
                    <Route exact path="/policies/refer">
                        <Refer />
                    </Route>
                    <Route exact path="/policies/welcome_bonus">
                        <WelcomeBonus />
                    </Route>
                </Switch>
            </div>
        </div>
    )
}
