import React from 'react'
import useScrollToTop from 'Hooks/useScrollToTop'

export default function Bounty() {
    useScrollToTop()
    return (
        <div className="documents pagePadding">
            <h1>Bug Bounty</h1>
            <section>
                <h2>Bug Bounty Awards Program</h2>
                <p>
                    Our goal is to deliver the most stable and reliable system
                    for your gaming enjoyment, however, ‘bugs’ sometimes happen
                    in software. If you discover a recurring bug during the
                    course of using Gambyl, we want you to let us know ASAP. If
                    we can verify the bug is significant, we will be happy to
                    award you a “Bug Bounty” bonus!
                </p>
            </section>
            <section>
                <h2>Eligibility</h2>
                <p>
                    The security, stability, and integrity of our platform are
                    of the utmost importance to us. Any bug that we determine is
                    a significant threat in the following areas will be eligible
                    for an award. We do reserve the right to decide if a bug is
                    significant enough to be eligible at our sole discretion. We
                    will judge all reported issues fairly and objectively.
                </p>
                <p>
                    Issues that typically would be eligible for reward include:
                </p>
                <ul>
                    <li>Cross-Site Request Forgery (CSRF)</li>
                    <li>Cross-Site Scripting (XSS)</li>
                    <li>Authentication Bypass</li>
                    <li>Code Injection</li>
                    <li>Clickjacking</li>
                    <li>Leakage of Sensitive Data</li>
                    <li>Privilege Escalation</li>
                    <li>Remote Code Execution</li>
                </ul>
            </section>
            <section>
                <h2>Ineligibility</h2>
                <p>
                    Some situations on our Website are not eligible for the
                    reward bounty:
                </p>
                <ul>
                    <li>
                        Bugs that are already known or previously reported by
                        another user.
                    </li>
                    <li>
                        Bugs that have not been responsibly investigated when
                        reported to us.
                    </li>
                    <li>
                        Issues that we cannot be expected to do anything about.
                    </li>
                    <li>Issues that cannot be replicated.</li>
                    <li>
                        Vulnerabilities on third-party sites that do not impact
                        our Website.
                    </li>
                    <li>
                        Vulnerabilities on any blog, news article, or
                        promotional advertisement we might operate.
                    </li>
                    <li>
                        Vulnerabilities affecting outdated, unsupported, or
                        unpatched browsers.
                    </li>
                    <li>
                        Vulnerabilities requiring a physical attack, social
                        engineering, spamming, DDOS, etc.
                    </li>
                </ul>
            </section>
            <section>
                <h2>Third-party issues</h2>
                <p>
                    Our Website utilizes third-party services and if you feel
                    that one of these might have a vulnerability, we would
                    definitely like to know about it. We cannot guarantee a
                    bounty if this is the case, however, we encourage you to
                    report any issues you discover. If the vulnerability might
                    reasonably affect our users, we'll likely grant a reward
                    bonus! Please note that all rewards for vulnerabilities
                    discovered in third-party services will be determined on a
                    case-by-case basis at our sole discretion.
                </p>
            </section>
            <section>
                <h2>Guidelines</h2>
                <p>
                    When investigating and reporting potential issues, please
                    make a good-faith effort to avoid being disruptive or
                    harmful to us or other users. Please only use your own
                    account when you are investigating the bug. It is essential
                    that you not violate the privacy of other users, destroy
                    data, or disrupt our services. Do not target our physical
                    security infrastructure or attempt to use other measures
                    such as distributed denial of service (DDOS) attacks, etc.
                </p>
            </section>
            <section>
                <h2>Discretion</h2>
                <p>
                    To best assist with this process, we ask that you please
                    report the bug to us immediately and exclusively so that we
                    can investigate. If we determine it needs to be fixed, you
                    will be notified. Please allow a reasonable amount of time
                    for us to implement the fix and we will be sure to keep you
                    in the loop on how it’s progressing. We ask that you not
                    disclose any issues to anyone else without Gambyl’s written
                    permission. If you have any questions about this, please ask
                    us. Thank you!
                </p>
            </section>
            <section>
                <h2>Reward</h2>
                <p>
                    We will provide you with a $50.00 credit to your Gambyl
                    account as a minimum reward for eligible bugs reported and
                    verified by us. Rewards over this amount are at our sole
                    discretion, however, we will pay significantly more for
                    issues that are deemed particularly serious. Please note, we
                    can credit only one (1) reward per bug.
                </p>
            </section>
            <section>
                <h2>How to file a report</h2>
                <p>
                    When you think you’ve discovered a bug, issue, or
                    vulnerability, please research and capture details of it
                    using the following format:
                </p>
                <ol>
                    <li>
                        Description of the issue (What you were doing, where &
                        when it occurred)
                    </li>
                    <li>URL (if applicable)</li>
                    <li>Why you feel it would affect Gambyl</li>
                    <li>The severity of the issue (low, medium, high)</li>
                    <li>Steps to replicate the issue (Please be specific)</li>
                    <li>
                        Please attach any screenshots or video you have of it
                    </li>
                    <li>Your contact information</li>
                </ol>
                <p>
                    When you’ve captured these details, please send them in an
                    email to{' '}
                    <a
                        href="mailto: <EMAIL>?subject=Bug report"
                        target="_blank"
                        rel="noreferrer"
                    >
                        <EMAIL>
                    </a>
                    .
                </p>
                <p>
                    Please allow us two business days to respond before sending
                    another report to us.
                </p>
                <p>
                    Gambyl reserves the right to modify the terms of this
                    program or terminate it at any time. Any changes will affect
                    the program going forward from the time of the change.
                </p>
            </section>
        </div>
    )
}
