import React from 'react'
import { Link } from 'react-router-dom'
import useScrollToTop from 'Hooks/useScrollToTop'

export default function WelcomeBonus() {
    useScrollToTop()

    return (
        <div className="documents pagePadding">
            <h1>Welcome Bonus</h1>
            <section>
                <h2>Welcome Bonus</h2>
                <p>
                    We are so sure you’ll love Gambyl that we want to offer all
                    new members a special gift. Once you have signed up and made
                    your first deposit of $50 or more (USD equivalent), we will
                    credit your new account with a $20 signing bonus!
                </p>
                <p>It’s that easy!</p>
                <Link to="/">
                    <button className="btn btn__green">Get started now</button>
                </Link>
            </section>
            <section>
                <h1>Terms and conditions</h1>
                <p>
                    This bonus will only apply to your first deposit. The bonus
                    money will have a 5X rollover restriction, which means you
                    cannot request a withdrawal on it until you have bet five
                    times that amount. For example, if you deposited $200 and
                    received a $20 bonus, you cannot withdraw that $20 bonus
                    until you have placed bets five times that amount ($100).
                    You must have funds in your account to use the bonus. Gambyl
                    reserves the right to withdraw this offer at any time at our
                    sole discretion.
                </p>
            </section>
        </div>
    )
}
