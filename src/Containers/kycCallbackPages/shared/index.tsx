import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCheckCircle, faTimesCircle } from '@fortawesome/free-solid-svg-icons'

import '../style.scss'

interface Props {
    status: 'success' | 'failure'
}

const KYCSuccess = () => (
    <>
        <h2>Your KYC was successfully submitted.</h2>
        <FontAwesomeIcon icon={faCheckCircle} size={'5x'} color="#00b71d" />
        <p>Your verification is in progress.</p>
    </>
)

const KYCFail = () => (
    <>
        <h2>Your KYC verification failed.</h2>
        <FontAwesomeIcon icon={faTimesCircle} size={'5x'} color="#cc0000" />
        <p>
            Please reach out{' '}
            <a
                href="mailto:<EMAIL>"
                title="mailto:<EMAIL>"
            >
                <EMAIL>
            </a>{' '}
            or create a ticket{' '}
            <a href="https://help.gambyl.com/en/support/tickets/new">HERE</a>{' '}
            for support.
        </p>
    </>
)

const KycBaseCallbackPage = ({ status }: Props) => {
    return (
        <section className="kyccallback__page">
            <div className="kyccallback__container">
                <div className="kyccallback__content">
                    {status === 'failure' ? <KYCFail /> : null}
                    {status === 'success' ? <KYCSuccess /> : null}
                    <p>Thank you. You may now close this page.</p>
                </div>
            </div>
        </section>
    )
}

export default KycBaseCallbackPage
