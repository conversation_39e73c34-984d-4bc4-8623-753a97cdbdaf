@import '../../Styles/colors';

.kyccallback {
    &__page {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        background-image: url('../../Assets/coverBGRegister.png');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        font-family: 'Montserrat-Bold', sans-serif;
        position: fixed;
        align-items: center;
        display: flex;
        justify-content: center;
    }

    &__container {
        background: $white;
        border-radius: 5px;
        min-width: 150px;
        max-width: 650px;
        width: 90%;
        box-shadow: 0 8px 10px rgba(0, 0, 0, 0.2);
    }

    &__content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
        padding: 20px;
        text-align: center;
    }

    &__header {
        height: 50px;
        background-color: $black;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top-right-radius: 5px;
        border-top-left-radius: 5px;

        img {
            max-width: 100%;
            height: auto;
            max-height: 35px;
        }
    }
}
