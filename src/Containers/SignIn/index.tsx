import React from 'react'
import { useHistory } from 'react-router-dom'
import { Formik } from 'formik'
import * as Yup from 'yup'

import ErrorMessage from 'Components/RegisterUser/ErrorMessage'

import { loginUser, useUserDispatch } from 'State/UserContext'
import { computeLocalCreds } from 'Utils/credentials'
import { publicContext } from 'Containers/App'
import { adminParties } from 'Utils/partiesUtils'

import useScrollToTop from 'Hooks/useScrollToTop'
import LoaderSpinner from 'Components/Loader'

import './style.scss'

const LoginSchema = Yup.object({
    partyData: Yup.string().required('Required field')
})

export default function Login() {
    const history = useHistory()
    const userDispatch = useUserDispatch()
    const [parties, setParties] = React.useState<any>(null)
    const [loading, setLoading] = React.useState(true)
    const publicLedger = publicContext.useLedger()

    useScrollToTop()

    React.useEffect(() => {
        //TODO: REFACTOR HERE TO USE LEDGER FROM PUBLIC TOKEN
        publicLedger
            .listKnownParties()
            .then(partiesQueried => {
                let filteredParties = partiesQueried?.filter(party => {
                    const noDisplayName = !party.displayName
                    const isAdmin =
                        party.displayName &&
                        adminParties.includes(party.displayName)
                    if (isAdmin || noDisplayName) {
                        return null
                    }
                    return party
                })
                setParties(filteredParties)
                setLoading(false)
            })
            .catch(error => console.error('error fetching parties', error))
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    const handleLogin = async (values: { partyData: string }) => {
        const { partyData } = values
        let parsedData = JSON.parse(partyData)
        const credentials = computeLocalCreds(
            parsedData?.identifier,
            parsedData?.displayName
        )
        loginUser(userDispatch, history, credentials)
    }

    return loading ? (
        <LoaderSpinner />
    ) : (
        <div className="login">
            <div className="login__container">
                <h2>Sign in to your account</h2>
                <Formik
                    initialValues={{ partyData: '' }}
                    onSubmit={values => {
                        handleLogin(values)
                    }}
                    validationSchema={LoginSchema}
                >
                    {formik => (
                        <form
                            className="login__form"
                            onSubmit={formik.handleSubmit}
                        >
                            <select
                                id="partyData"
                                placeholder="Please add your username"
                                className={`register__input`}
                                defaultValue={''}
                                {...formik.getFieldProps('partyData')}
                            >
                                <option
                                    value=""
                                    label={'Please add your username'}
                                >
                                    {'Please add your username'}
                                </option>

                                {parties?.map((party: any) => (
                                    <option
                                        key={party.identifier}
                                        value={JSON.stringify(party)}
                                        label={party.displayName}
                                    >
                                        {party.displayName}
                                    </option>
                                ))}
                            </select>
                            {formik.touched.partyData &&
                            formik.errors.partyData ? (
                                <ErrorMessage
                                    message={formik.errors.partyData}
                                />
                            ) : null}
                            <button
                                disabled={Boolean(formik.errors.partyData)}
                                className="registerbtn__nextStep"
                                type="submit"
                            >
                                Sign In
                            </button>
                        </form>
                    )}
                </Formik>
            </div>
        </div>
    )
}
