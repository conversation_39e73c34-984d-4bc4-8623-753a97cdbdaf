@import '../../Styles/colors';

.login {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGSignIn.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    font-family: 'Montserrat-Bold', sans-serif;
    gap: 100px;
    min-height: 100vh;
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%;

    &__container {
        background: $white;
        border-radius: 5px;
        padding: 60px;
        max-width: 640px;
        max-height: 832px;

        h2 {
            font-size: 2.2rem;
            line-height: 2.75rem;
            text-align: center;
            margin-bottom: 20px;
        }
    }

    &__action {
        display: flex;
        flex-direction: column;
        gap: 10px 0;
    }

    &__loader {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

@media (max-width: 1020px) {
    .login {
        &__container {
            padding: 40px;
            max-width: 470px;
        }
    }
}

@media (max-width: 500px) {
    .login {
        &__container {
            padding: 30px;
            max-width: 370px;
        }
    }
}
