import React from 'react'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import Ledger, { CreateEvent } from '@daml/ledger'

import LoaderSpinner from 'Components/Loader'
import BasicModal from 'Components/Modals/BasicModal'

import { expirePromotion } from './utils'

const ButtonExpirePromotion = ({
    ledger,
    promotion
}: {
    ledger: Ledger
    promotion: CreateEvent<Promotion, Promotion.Key, string>
}) => {
    const [isConfirmModalOpen, setIsConfirmModalOpen] = React.useState(false)
    const [isSubmittingRequest, setIsSubmittingRequest] = React.useState(false)
    const handleClose = () => {
        setIsConfirmModalOpen(false)
    }
    const handleOpen = () => {
        setIsConfirmModalOpen(true)
    }
    const handleConfirm = async () => {
        setIsSubmittingRequest(true)
        await expirePromotion(ledger, promotion)
        setIsSubmittingRequest(false)
        handleClose()
    }
    return (
        <>
            <BasicModal
                body={
                    isSubmittingRequest ? (
                        <LoaderSpinner />
                    ) : (
                        <p>
                            Would you like to expire this promotion{' '}
                            <strong>
                                {promotion.payload.title.entriesArray()[0][1]}
                            </strong>{' '}
                            ?
                        </p>
                    )
                }
                isOpenModal={isConfirmModalOpen}
                handleClose={handleClose}
                footerBody={
                    isSubmittingRequest ? null : (
                        <>
                            <button
                                className="btn btn__primary"
                                onClick={handleConfirm}
                            >
                                Yes
                            </button>
                            <button
                                className="btn btn__grey"
                                onClick={handleClose}
                            >
                                No
                            </button>
                        </>
                    )
                }
                shouldCloseOnOverlayClickProp={false}
            />
            <button
                disabled={promotion.payload.status === 'Expired'}
                className="promotion__card__action"
                onClick={handleOpen}
            >
                Expire Promotion
            </button>
        </>
    )
}

export default ButtonExpirePromotion
