import React from 'react'
import { GambylAdminContext } from '../GambylLedgerWrapper'
import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import LoaderSpinner from 'Components/Loader'
import Table from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import FilterContainer from 'Components/Table/Filter'
import FilterInput from 'Components/Table/Filter/Input'
import { getSearchState, handleSearch } from 'Components/Table/Filter/utils'
import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable,
    ColumnFiltersState
} from '@tanstack/react-table'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import ButtonExpirePromotion from './ButtonExpirePromotion'
import { CreateEvent } from '@daml/ledger'
import getFormattedDate from 'Containers/Dashboard/getFormattedDate'

interface ITableData {
    title: string
    status: string
    expireDate: string
    startDate: string
    promotion: CreateEvent<Promotion, Promotion.Key, string>
}

export const colFiltersInitState = [{ id: 'title', value: '' }]

const ExpirePromotion = () => {
    const ledger = GambylAdminContext.useLedger()
    const { loading, contracts } =
        GambylAdminContext.useStreamQueries(Promotion)

    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>(colFiltersInitState)
    const columnHelper = createColumnHelper<ITableData>()

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('title', {
                header: 'Title',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('status', {
                header: 'Status',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('startDate', {
                header: 'Start Date',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('expireDate', {
                header: 'Expire Date',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('promotion', {
                header: 'Action',
                cell: info => (
                    <ButtonExpirePromotion
                        ledger={ledger}
                        promotion={info.getValue()}
                    />
                )
            })
        ],
        [columnHelper, ledger]
    )

    let data = React.useMemo(
        () =>
            contracts.map(contract => ({
                title: contract.payload.title.entriesArray()[0][1],
                status: contract.payload.status,
                expireDate: contract.payload.config.endDate ?? '-',
                startDate: getFormattedDate(contract.payload.startDate),
                promotion: contract
            })),
        [contracts]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: false,
        state: {
            columnFilters
        },
        onColumnFiltersChange: setColumnFilters
    })

    if (loading) {
        return (
            <>
                <LoaderSpinner />
            </>
        )
    }
    return (
        <>
            <FilterContainer>
                {React.Children.toArray(
                    colFiltersInitState.map(col => {
                        const title = descriptCamelCase(col.id)
                        return (
                            <FilterInput
                                label={title.toLocaleUpperCase()}
                                type="text"
                                placeholder={`Filter by ${title.toLocaleLowerCase()}`}
                                onChange={e =>
                                    handleSearch(table, e.target.value, col.id)
                                }
                                value={
                                    (getSearchState(table, col.id) as string) ??
                                    ''
                                }
                            />
                        )
                    })
                )}
            </FilterContainer>
            <Table
                tableHeader={table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                            <th key={header.id}>
                                {header.column.columnDef.header}
                            </th>
                        ))}
                    </tr>
                ))}
                tableBodyRow={table.getRowModel().rows.map(row => (
                    <tr key={row.id}>
                        {row.getVisibleCells().map(cell => (
                            <td key={cell.id}>
                                {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                )}
                            </td>
                        ))}
                    </tr>
                ))}
            />
            <Pagination table={table} />
        </>
    )
}

export default ExpirePromotion
