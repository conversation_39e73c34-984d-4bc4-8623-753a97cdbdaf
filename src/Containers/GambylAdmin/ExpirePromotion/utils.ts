import { Promotion } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Model'
import Ledger, { CreateEvent } from '@daml/ledger'
import { Service as MarketingManagerService } from '@daml.js/gambyl-ledger/lib/Gambyl/Marketing/Service'
import { toast } from 'react-toastify'

export const expirePromotion = async (
    ledger: Ledger,
    promotion: CreateEvent<Promotion, Promotion.Key, string>
) => {
    try {
        const {
            payload: { operator, provider, customer },
            contractId
        } = promotion
        await ledger.exerciseByKey(
            MarketingManagerService.ExpirePromotion,
            { _1: operator, _2: provider, _3: customer },
            { promotionCid: contractId }
        )
        toast.success('Expired Promotion')
    } catch (error) {
        console.error(error)
        toast.error('Something went wrong')
    }
}
