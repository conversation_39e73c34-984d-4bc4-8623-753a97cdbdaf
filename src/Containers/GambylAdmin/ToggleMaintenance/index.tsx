import React from 'react'
import Switch from 'react-switch'
import * as damlTypes from '@daml/types'
import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'
import { Role } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Role'
import { toast } from 'react-toastify'

import { GambylAdminContext } from 'Containers/GambylAdmin/GambylLedgerWrapper'
import LoaderSpinner from 'Components/Loader'

export default function ToggleMaintenance() {
    const gambylAdminLedger = GambylAdminContext.useLedger()
    const { loading: loadingRole, contracts: roleContracts } =
        GambylAdminContext.useQuery(Role)
    const { loading, contracts } = GambylAdminContext.useStreamQueries(
        GlobalGamblingConfiguration
    )
    const isOnMaintenance = contracts[0]?.payload?.isOnMaintenance

    const empty = damlTypes.emptyMap<string, damlTypes.Party>()

    const ToggleMaintenance = () => {
        if (!roleContracts.length) {
            return toast.error('Something went wrong, please try again later')
        }
        const maintenanceValue = !isOnMaintenance
        let choiceConstructor = {
            newFlaggedAmount: null,
            newUnverifiedAccountMaxAmount: null,
            newMinDepositAmount: null,
            newMinWithdrawAmount: null,
            newBetFee: null,
            newDepositFee: null,
            newWithdrawFee: null,
            newArchiveEventDays: null,
            newLegalAge: null,
            newIsOnMaintenance: maintenanceValue,
            newDaysPostponedEventExpires: null,
            newMinOdd: null,
            newMinutesMarketMapExpires: null,
            newIntegrationParties: empty as damlTypes.Map<
                string,
                damlTypes.Party
            >,
            newAllowedPeriod: null,
            newMinStake: null,
            newDefaultOdds: null
        }
        gambylAdminLedger
            .exercise(
                Role.ChangeGlobalGamblingConfiguration,
                roleContracts[0].contractId,
                choiceConstructor
            )
            .then(() =>
                toast.success(
                    `Maintenance turned ${maintenanceValue ? 'on' : 'off'}`
                )
            )
            .catch(() =>
                toast.error('Something went wrong, please try again later')
            )
    }

    return loading && loadingRole ? (
        <LoaderSpinner />
    ) : (
        <div className="gambyladminpanel__toggleMaintenance">
            <label htmlFor="maintenance">maintenance:</label>
            <Routes
                checked={isOnMaintenance ?? false}
                onChange={() => ToggleMaintenance()}
                onColor="#a000e1"
            />
        </div>
    );
}
