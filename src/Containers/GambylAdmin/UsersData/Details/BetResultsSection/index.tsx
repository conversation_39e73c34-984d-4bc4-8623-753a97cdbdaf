import React from 'react'
import { BetHistory } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { CreateEvent } from '@daml/ledger'
import {
    CellContext,
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import numeral from 'numeral'

import GenericTable from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import EventTitleCell from 'Containers/Account/History/shared/Cells/EventTitleCell'
import BetPlacementIdCell from 'Containers/Account/History/shared/Cells/BetPlacementIdCell'
import getFormattedDate from 'Containers/Dashboard/getFormattedDate'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import { generateCSVDownload } from 'Utils/generateCsvDownload'
import { toast } from 'react-toastify'

interface IBetResultsTable {
    ticketId: string
    date: string
    outcome: string
    side: string
    eventTitle: { title: string; eventStartDate: string }
    wager: string
    fees: string
    winnings: string
    result: string
    counterBet: string
}

interface Props {
    betResultsContracts: readonly CreateEvent<
        BetHistory,
        BetHistory.Key,
        string
    >[]
    emailAddress: string
    setIsLoadingCsv: (val: boolean) => void
}

const BetResultsSection = ({
    betResultsContracts,
    emailAddress,
    setIsLoadingCsv
}: Props) => {
    const columnHelper = createColumnHelper<IBetResultsTable>()

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('ticketId', {
                header: 'ticket id',
                cell: info =>
                    flexRender(BetPlacementIdCell, {
                        betPlacementId: info.getValue()
                    })
            }),
            columnHelper.accessor('date', {
                header: 'date',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('outcome', {
                header: 'outcome',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('side', {
                header: 'side',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('eventTitle', {
                header: 'details',
                cell: info =>
                    flexRender(EventTitleCell, {
                        eventTitle: info.getValue().title,
                        startDate: info.getValue().eventStartDate
                    })
            }),
            columnHelper.accessor('wager', {
                header: 'wager',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('fees', {
                header: 'fees',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('winnings', {
                header: 'winnings',
                cell: info => <>{info.getValue()}</>
            }),
            columnHelper.accessor('result', {
                header: 'result',
                meta: {
                    getCellContext: (
                        context: CellContext<IBetResultsTable, string>
                    ) => {
                        return {
                            className:
                                context.getValue() === 'Won'
                                    ? 'success'
                                    : 'fail'
                        }
                    }
                },
                cell: info => <strong>{info.getValue()}</strong>
            }),
            columnHelper.accessor('counterBet', {
                header: 'counter bet',
                cell: info =>
                    flexRender(BetPlacementIdCell, {
                        counterBet: info.getValue()
                    })
            })
        ],
        [columnHelper]
    )

    const data = React.useMemo(
        () =>
            betResultsContracts.map(data => ({
                ticketId: data.payload.details.betPlacementId,
                date: getFormattedDate(data.payload.placedAt),
                outcome: descriptCamelCase(
                    data.payload.details.side.value.outcome.type_.tag
                ),
                side: data.payload.details.side.tag,
                eventTitle: {
                    title: getEventTitleFromLang(
                        data.payload.details?.eventTitle,
                        'en'
                    ),
                    eventStartDate: getFormattedDate(
                        data.payload.details.eventStartDate
                    )
                },
                wager: numeral(data.payload.details.side.value.stake).format(
                    '$0,0.00'
                ),
                fees: numeral(
                    Number(data?.payload.winnings?.grossAmount) -
                        Number(data?.payload.winnings?.netAmount)
                ).format('$0,0.00'),
                winnings: numeral(data?.payload.winnings?.netAmount).format(
                    '$0,0.00'
                ),
                result: data.payload.won ? 'Won' : 'Lost',
                counterBet: data.payload.counterBetPlacementId
            })),
        [betResultsContracts]
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: true
    })

    const getBetResultsCSV = () => {
        let dataToDownload = data.map(val => {
            return {
                ...val,
                eventTitle: val.eventTitle.title,
                eventStartDate: val.eventTitle.eventStartDate
            }
        })
        setIsLoadingCsv(true)
        generateCSVDownload(dataToDownload, `bet-results-${emailAddress}`)
        setIsLoadingCsv(false)
        toast.success('Download complete.')
    }

    return (
        <>
            <h3 className="marginTop20">Bet Results:</h3>
            <hr />
            <div className="gambyladminpanel__containerDownloadNoTitle">
                <button
                    className="registerbtn__nextStep"
                    onClick={() => getBetResultsCSV()}
                    disabled={!data.length}
                >
                    Get Bet Results CSV
                </button>
            </div>
            {data.length > 0 ? (
                <>
                    <GenericTable
                        tableHeader={table
                            .getHeaderGroups()
                            .map(headerGroup => (
                                <tr key={headerGroup.id}>
                                    {headerGroup.headers.map(header => {
                                        return (
                                            <th key={header.id}>
                                                {header.column.columnDef.header}
                                            </th>
                                        )
                                    })}
                                </tr>
                            ))}
                        tableBodyRow={table.getRowModel().rows.map(row => (
                            <tr key={row.id}>
                                {row.getVisibleCells().map(cell => {
                                    let hasMeta =
                                        cell.getContext().cell.column.columnDef
                                            .meta
                                    return (
                                        <td
                                            key={cell.id}
                                            {...(hasMeta && {
                                                ...hasMeta.getCellContext(
                                                    cell.getContext()
                                                )
                                            })}
                                        >
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </td>
                                    )
                                })}
                            </tr>
                        ))}
                    />
                    <Pagination table={table} />
                </>
            ) : (
                <p>No data to display</p>
            )}
        </>
    )
}

export default BetResultsSection
