import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { BetPlacement } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import {
    CellContext,
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import {
    calculateBetResult,
    prepareOddForDisplay
} from 'Components/MyBets/utils'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import getFormattedDate from 'Containers/Dashboard/getFormattedDate'
import numeral from 'numeral'
import BetPlacementIdCell from 'Containers/Account/History/shared/Cells/BetPlacementIdCell'
import EventTitleCell from 'Containers/Account/History/shared/Cells/EventTitleCell'

import GenericTable from 'Components/Table/Generic'
import Pagination from 'Components/Table/Pagination'
import { generateCSVDownload } from 'Utils/generateCsvDownload'
import { toast } from 'react-toastify'
import { getEventTitleFromLang } from 'Utils/getEventTitleFromLang'
import {
    calculateTotalExposure,
    calculateTotalProfit,
    calculateTotalWager
} from 'Containers/Account/History/utils'

interface IBetHistoryTable {
    betPlacementId: string
    placedAt: string
    status: string
    outcomeTypeTag: string
    sideTag: string
    eventTitle: { title: string; eventStartDate: string }
    stake: string
    odd: string
    profitLoss: { amount: string; side: string }
}

const BetHistorySection = ({
    betHistoryContracts,
    emailAddress,
    setIsLoadingCsv
}: {
    betHistoryContracts: readonly CreateEvent<
        BetPlacement,
        BetPlacement.Key,
        string
    >[]
    emailAddress: string
    setIsLoadingCsv: (value: boolean) => void
}) => {
    const columnHelper = createColumnHelper<IBetHistoryTable>()

    const columns = React.useMemo(
        () => [
            columnHelper.accessor('betPlacementId', {
                header: 'ticket id',
                cell: info =>
                    flexRender(BetPlacementIdCell, {
                        betPlacementId: info.getValue()
                    })
            }),
            columnHelper.accessor('placedAt', {
                header: 'date',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('status', {
                header: 'status',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('outcomeTypeTag', {
                header: 'outcome',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('sideTag', {
                header: 'side',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('eventTitle', {
                header: 'details',
                cell: info =>
                    flexRender(EventTitleCell, {
                        eventTitle: info.getValue().title,
                        startDate: info.getValue().eventStartDate
                    })
            }),
            columnHelper.accessor('stake', {
                header: 'wager',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('odd', {
                header: 'odds',
                cell: info => info.getValue()
            }),
            columnHelper.accessor('profitLoss', {
                header: 'profit/loss',
                meta: {
                    getCellContext: (
                        context: CellContext<
                            IBetHistoryTable,
                            { amount: string; side: string }
                        >
                    ) => {
                        return {
                            className:
                                context.getValue().side === 'Back'
                                    ? 'success'
                                    : 'fail'
                        }
                    }
                },
                cell: info => <>{info.getValue().amount}</>
            })
        ],
        [columnHelper]
    )

    const data = React.useMemo(
        () =>
            betHistoryContracts.map(data => ({
                betPlacementId: data.payload.details.betPlacementId,
                placedAt: getFormattedDate(data.payload.placedAt),
                status: data.payload.status,
                outcomeTypeTag: descriptCamelCase(
                    data.payload.details.side.value.outcome.type_.tag
                ),
                sideTag: data.payload.details.side.tag,
                eventTitle: {
                    title: getEventTitleFromLang(
                        data.payload.details?.eventTitle,
                        'en'
                    ),
                    eventStartDate: getFormattedDate(
                        data.payload.details?.eventStartDate
                    )
                },
                stake: numeral(data.payload.details.side.value.stake).format(
                    '$0,0.00'
                ),
                odd: prepareOddForDisplay(data, 'Decimal').toString(),
                profitLoss: {
                    amount: `${
                        data.payload.details.side.tag === 'Back' ? '+ ' : '- '
                    } ${numeral(
                        calculateBetResult(
                            Number(data.payload.details.side.value.odd.value),
                            data.payload.details.side.value.odd.tag,
                            data.payload.details.side.value.stake
                        )
                    ).format('$0,0.00')}`,
                    side: data.payload.details.side.tag
                }
            })),
        [betHistoryContracts]
    )
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: true
    })

    const getBetHistoryCSV = () => {
        let dataToDownload = data.map(val => {
            return {
                ...val,
                eventTitle: val.eventTitle.title,
                eventStartDate: val.eventTitle.eventStartDate,
                profitLoss: val.profitLoss.amount
            }
        })
        setIsLoadingCsv(true)
        generateCSVDownload(dataToDownload, `bet-history-${emailAddress}`)
        setIsLoadingCsv(false)
        toast.success('Download complete.')
    }

    const totalWager = calculateTotalWager([...betHistoryContracts])
    const totalProfit = calculateTotalProfit([...betHistoryContracts])
    const totalExposure = calculateTotalExposure([...betHistoryContracts])

    return (
        <>
            <h3 className="marginTop20">Bet History:</h3>
            <hr />
            <div className="gambyladminpanel__containerDownloadNoTitle">
                <button
                    className="registerbtn__nextStep"
                    onClick={() => getBetHistoryCSV()}
                    disabled={!data.length}
                >
                    Get Bet History CSV
                </button>
            </div>
            <div className="history__cards">
                <div className="history__card">
                    <p>Total Bets Placed</p>
                    <strong>{betHistoryContracts.length}</strong>
                </div>
                <div className="history__card">
                    <p>Total Staked</p>
                    <strong>{numeral(totalWager).format('$0,0.00')}</strong>
                </div>
                <div className="history__card">
                    <p>Total Profit</p>
                    <strong>{numeral(totalProfit).format('$0,0.00')}</strong>
                </div>
                <div className="history__card">
                    <p>Total Liability</p>
                    <strong>{numeral(totalExposure).format('$0,0.00')}</strong>
                </div>
            </div>
            {data?.length > 0 ? (
                <>
                    <GenericTable
                        tableHeader={table
                            .getHeaderGroups()
                            .map(headerGroup => (
                                <tr key={headerGroup.id}>
                                    {headerGroup.headers.map(header => {
                                        return (
                                            <th key={header.id}>
                                                {header.column.columnDef.header}
                                            </th>
                                        )
                                    })}
                                </tr>
                            ))}
                        tableBodyRow={table.getRowModel().rows.map(row => (
                            <tr key={row.id}>
                                {row.getVisibleCells().map(cell => {
                                    let hasMeta =
                                        cell.getContext().cell.column.columnDef
                                            .meta
                                    return (
                                        <td
                                            key={cell.id}
                                            {...(hasMeta && {
                                                ...hasMeta.getCellContext(
                                                    cell.getContext()
                                                )
                                            })}
                                        >
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </td>
                                    )
                                })}
                            </tr>
                        ))}
                    />
                    <Pagination table={table} />
                </>
            ) : (
                <p>No data available</p>
            )}
        </>
    )
}

export default BetHistorySection
