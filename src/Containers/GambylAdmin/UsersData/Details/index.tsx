import React from 'react'
import { Link, useLocation, useParams } from 'react-router-dom'
import {
    BlockedService,
    Service
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import {
    GamblerUnverifiedIdentity,
    GamblerIdentity,
    PendingIdentity,
    RejectedIdentity
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import {
    Account,
    TransactionHistory
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import {
    BetHistory,
    BetPlacement
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Model'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'
import numeral from 'numeral'

import { GambylAdminContext } from 'Containers/GambylAdmin/GambylLedgerWrapper'

import BlockModal from './BlockModal'
import UnblockModal from './UnblockModal'
import BlockedUser from './BlockedUser'
import PersonalDetails from './PersonalDetails'
import BetHistorySection from './BetHistorySection'
import TransactionHistorySection from './TransactionHistorySection'
import LoaderSpinner from 'Components/Loader'

import getUserStatus from 'Utils/getUserStatus'
import BetResultsSection from './BetResultsSection'

export default function UserDetails() {
    const { partyId } = useParams<{ partyId: string }>()
    const { search } = useLocation()

    const [openBlock, setOpenBlock] = React.useState(false)
    const [openUnBlock, setOpenUnBlock] = React.useState(false)
    const [loadingCsv, setIsLoadingCsv] = React.useState(false)

    const {
        contracts: gamblerUnverifiedIdentity,
        loading: gamblerUnverifiedIdentityLoader
    } = GambylAdminContext.useQuery(
        GamblerUnverifiedIdentity,
        () => {
            return { customer: partyId }
        },
        [partyId]
    )

    const { contracts: gamblerIdentity, loading: gamblerIdentityLoader } =
        GambylAdminContext.useQuery(
            GamblerIdentity,
            () => {
                return { customer: partyId }
            },
            [partyId]
        )

    const { contracts: pendingIdentity, loading: pendingIdentityLoader } =
        GambylAdminContext.useQuery(
            PendingIdentity,
            () => {
                return { customer: partyId }
            },
            [partyId]
        )

    const { contracts: rejectedIdentity, loading: rejectedIdentityLoader } =
        GambylAdminContext.useQuery(
            RejectedIdentity,
            () => {
                return { customer: partyId }
            },
            [partyId]
        )

    const { contracts: account, loading: accountLoading } =
        GambylAdminContext.useQuery(
            Account,
            () => {
                return { customer: partyId }
            },
            [partyId]
        )

    const {
        contracts: transactionHistory,
        loading: transactionHistoryLoading
    } = GambylAdminContext.useQuery(
        TransactionHistory,
        () => {
            return { customer: partyId }
        },
        [partyId]
    )

    const { contracts: blockedContracts, loading: blockedLoading } =
        GambylAdminContext.useStreamQueries(
            BlockedService,
            () => [{ service: { customer: partyId } }],
            [partyId]
        )

    const { contracts: serviceContracts, loading: serviceLoading } =
        GambylAdminContext.useStreamQueries(
            Service,
            () => [{ customer: partyId }],
            [partyId]
        )

    const { contracts: betPlacementContracts, loading: betPlacementLoading } =
        GambylAdminContext.useStreamQueries(
            BetPlacement,
            () => [{ customer: partyId }],
            [partyId]
        )

    const { contracts: betResultsContracts, loading: betResultsLoading } =
        GambylAdminContext.useStreamQueries(
            BetHistory,
            () => [{ customer: partyId }],
            [partyId]
        )

    const isLoading =
        accountLoading ||
        rejectedIdentityLoader ||
        pendingIdentityLoader ||
        gamblerIdentityLoader ||
        gamblerUnverifiedIdentityLoader ||
        transactionHistoryLoading ||
        blockedLoading ||
        serviceLoading ||
        loadingCsv ||
        betPlacementLoading ||
        betResultsLoading

    const loaderKYC =
        rejectedIdentityLoader || pendingIdentityLoader || gamblerIdentityLoader

    let KYCStatus = getUserStatus({
        gamblerIdentity,
        rejectedIdentity,
        pendingIdentity,
        isLoading: loaderKYC
    })

    const totalInAccount = () =>
        numeral(
            Number(account[0]?.payload.totalBetBalance) +
                Number(account[0]?.payload.totalWithdrawBalance) +
                Number(account[0]?.payload.totalMainBalance)
        ).format('$0,0.00')

    if (isLoading) {
        return <LoaderSpinner />
    }

    return (
        <>
            <BlockModal
                isOpen={openBlock}
                handleClose={() => setOpenBlock(false)}
                serviceContracts={serviceContracts}
            />
            <UnblockModal
                isOpen={openUnBlock}
                handleClose={() => setOpenUnBlock(false)}
                blockedContracts={blockedContracts}
            />
            <Link
                to={`/gambyladmin/users${search}`}
                className="gambyladminpanel__backarrow"
            >
                <FontAwesomeIcon
                    icon={faArrowLeft}
                    style={{ fontSize: '0.7rem' }}
                    color="#a000e1"
                />{' '}
                Back to users lists
            </Link>
            <BlockedUser
                blockedContracts={blockedContracts}
                setOpenBlock={setOpenBlock}
                setOpenUnBlock={setOpenUnBlock}
            />
            <PersonalDetails
                KYCStatus={KYCStatus}
                partyId={partyId}
                gamblerIdentity={gamblerIdentity}
                gamblerUnverifiedIdentity={gamblerUnverifiedIdentity}
                account={account}
                blockedContracts={blockedContracts}
                setIsLoadingCsv={setIsLoadingCsv}
                totalInAccount={totalInAccount}
                serviceContracts={serviceContracts}
            />
            <TransactionHistorySection
                account={account}
                totalInAccount={totalInAccount}
                transactionHistory={transactionHistory}
                emailAddress={
                    gamblerUnverifiedIdentity[0]?.payload?.emailAddress
                }
                setIsLoadingCsv={setIsLoadingCsv}
            />
            <BetHistorySection
                betHistoryContracts={betPlacementContracts}
                emailAddress={
                    gamblerUnverifiedIdentity[0]?.payload?.emailAddress
                }
                setIsLoadingCsv={setIsLoadingCsv}
            />
            <BetResultsSection
                betResultsContracts={betResultsContracts}
                emailAddress={
                    gamblerUnverifiedIdentity[0]?.payload?.emailAddress
                }
                setIsLoadingCsv={setIsLoadingCsv}
            />
        </>
    )
}
