import React from 'react'
import CopyClipboard from 'Components/Clipboard'
import { displayProp } from 'Containers/Account/Info/utils'
import { convertCountryCodeToName } from 'Containers/UnverifiedGamblerIdentity/utils'
import { CreateEvent } from '@daml/ledger'
import {
    GamblerIdentity,
    GamblerUnverifiedIdentity
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { generateCSVDownload } from 'Utils/generateCsvDownload'
import { toast } from 'react-toastify'
import numeral from 'numeral'
import {
    BlockedService,
    Service
} from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { Account } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'

interface Props {
    partyId: string
    KYCStatus: string
    gamblerIdentity: readonly CreateEvent<
        GamblerIdentity,
        GamblerIdentity.Key,
        string
    >[]
    gamblerUnverifiedIdentity: readonly CreateEvent<
        GamblerUnverifiedIdentity,
        GamblerUnverifiedIdentity.Key,
        string
    >[]
    blockedContracts: readonly CreateEvent<
        BlockedService,
        BlockedService.Key,
        string
    >[]
    account: readonly CreateEvent<Account, Account.Key, string>[]
    serviceContracts: readonly CreateEvent<Service, Service.Key, string>[]
    totalInAccount: () => string
    setIsLoadingCsv: (val: boolean) => void
}

const PersonalDetails = ({
    partyId,
    KYCStatus,
    gamblerIdentity,
    gamblerUnverifiedIdentity,
    blockedContracts,
    account,
    totalInAccount,
    setIsLoadingCsv,
    serviceContracts
}: Props) => {
    const getUsersCsv = () => {
        let userDetails = [
            {
                party: partyId,
                status: blockedContracts.length > 0 ? 'Blocked' : 'Active',
                serviceCreatedAt: displayServicerData(),
                blockedReason:
                    blockedContracts.length > 0
                        ? blockedContracts[0].payload.reason
                        : '-',
                kycStatus: KYCStatus,
                firstName: displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.firstName,
                    gamblerUnverifiedIdentity[0]?.payload?.firstName
                ),
                lastName: displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.lastName,
                    gamblerUnverifiedIdentity[0]?.payload?.lastName
                ),
                DOB: displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.birthday,
                    gamblerUnverifiedIdentity[0]?.payload?.birthday
                ),
                email:
                    gamblerUnverifiedIdentity[0]?.payload?.emailAddress ?? '-',
                phoneNumber:
                    gamblerUnverifiedIdentity[0]?.payload?.phoneNumber ?? '-',
                paymentCountry: gamblerUnverifiedIdentity[0]?.payload
                    ?.countryCode
                    ? convertCountryCodeToName(
                          gamblerUnverifiedIdentity[0]?.payload?.countryCode
                      )
                    : '-',
                countryResidence: gamblerIdentity[0]?.payload?.jumioUserData
                    ?.country
                    ? gamblerIdentity[0]?.payload?.jumioUserData?.country
                    : '-',
                city: displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.city,
                    gamblerUnverifiedIdentity[0]?.payload?.city
                ),
                stateDistrict: displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.subDivision,
                    gamblerUnverifiedIdentity[0]?.payload?.subDivision
                ),
                addressLine1: displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.addressLine1,
                    gamblerUnverifiedIdentity[0]?.payload?.addressLine1
                ),
                postcode: displayProp(
                    gamblerIdentity[0]?.payload?.jumioUserData?.postalCode,
                    gamblerUnverifiedIdentity[0]?.payload?.postalCode
                ),
                totalFundsInAccount: totalInAccount(),
                pendingWagers: numeral(
                    account[0]?.payload.totalBetBalance
                ).format('$0,0.00'),
                pedingWithdraws: numeral(
                    account[0]?.payload.totalWithdrawBalance
                ).format('$0,0.00'),
                bonusAvailable: numeral(
                    account[0]?.payload.totalBonusBalance
                ).format('$0,0.00'),
                totalFeesPaid: numeral(account[0]?.payload.totalFees).format(
                    '$0,0.00'
                ),
                availableBalance: numeral(
                    account[0]?.payload.totalMainBalance
                ).format('$0,0.00')
            }
        ]
        setIsLoadingCsv(true)
        generateCSVDownload(
            userDetails,
            `users-details-${gamblerUnverifiedIdentity[0]?.payload?.emailAddress}`
        )
        setIsLoadingCsv(false)
        toast.success('Download complete.')
    }

    const displayServicerData = () => {
        if (!serviceContracts.length) {
            return '-'
        }
        const dateSplitArray =
            serviceContracts[0]?.payload?.createdAt.split('T')
        return `${dateSplitArray[0]} ${dateSplitArray[1].split('.')[0]} UTC`
    }

    return (
        <>
            <div className="gambyladminpanel__containerWithDownloadBtn">
                <h1 className="marginTop20">User Details</h1>
                <button className="registerbtn__nextStep" onClick={getUsersCsv}>
                    Get User Details CSV
                </button>
            </div>
            <h3 className="marginTop20">Service creation</h3>
            <hr />
            <div className="gambyladminpanel__userDetails">
                <div>
                    <p>Created at:</p>
                    <p>{displayServicerData()}</p>
                </div>
            </div>
            <h3 className="marginTop20">Personal Details</h3>
            <hr />
            <div className="gambyladminpanel__userDetails">
                <div>
                    <p>Party</p>
                    <p>
                        {partyId.slice(0, 8)}...
                        <CopyClipboard contenToCopy={partyId} />
                    </p>
                </div>
                <div>
                    <p>KYC Status</p>
                    <p>{KYCStatus}</p>
                </div>
                <div>
                    <p>First name</p>
                    <p>
                        {displayProp(
                            gamblerIdentity[0]?.payload?.jumioUserData
                                ?.firstName,
                            gamblerUnverifiedIdentity[0]?.payload?.firstName
                        )}
                    </p>
                </div>
                <div>
                    <p>Last name</p>
                    <p>
                        {displayProp(
                            gamblerIdentity[0]?.payload?.jumioUserData
                                ?.lastName,
                            gamblerUnverifiedIdentity[0]?.payload?.lastName
                        )}
                    </p>
                </div>
                <div>
                    <p>Date of Birth</p>
                    <p>
                        {displayProp(
                            gamblerIdentity[0]?.payload?.jumioUserData
                                ?.birthday,
                            gamblerUnverifiedIdentity[0]?.payload?.birthday
                        )}
                    </p>
                </div>
                <div>
                    <p>Email</p>
                    <p>
                        {gamblerUnverifiedIdentity[0]?.payload?.emailAddress ??
                            '-'}
                    </p>
                </div>
                <div>
                    <p>Phone number</p>
                    <p>
                        {gamblerUnverifiedIdentity[0]?.payload?.phoneNumber ??
                            '-'}
                    </p>
                </div>
                <div>
                    <p>Payment Country</p>
                    <p>
                        {gamblerUnverifiedIdentity[0]?.payload?.countryCode
                            ? convertCountryCodeToName(
                                  gamblerUnverifiedIdentity[0]?.payload
                                      ?.countryCode
                              )
                            : '-'}
                    </p>
                </div>
                <div>
                    <p>Country of Residence</p>
                    <p>
                        {gamblerIdentity[0]?.payload?.jumioUserData?.country ??
                            '-'}
                    </p>
                </div>
                <div>
                    <p>City</p>
                    <p>
                        {displayProp(
                            gamblerIdentity[0]?.payload?.jumioUserData?.city,
                            gamblerUnverifiedIdentity[0]?.payload?.city
                        )}
                    </p>
                </div>
                <div>
                    <p>State or District</p>
                    <p>
                        {displayProp(
                            gamblerIdentity[0]?.payload?.jumioUserData
                                ?.subDivision,
                            gamblerUnverifiedIdentity[0]?.payload?.subDivision
                        )}
                    </p>
                </div>
                <div>
                    <p>Address line 1</p>
                    <p>
                        {displayProp(
                            gamblerIdentity[0]?.payload?.jumioUserData
                                ?.addressLine1,
                            gamblerUnverifiedIdentity[0]?.payload?.addressLine1
                        )}
                    </p>
                </div>
                <div>
                    <p>Address line 2</p>
                    <p>
                        {gamblerIdentity[0]?.payload?.jumioUserData
                            ?.addressLine2 ?? '-'}
                    </p>
                </div>
                <div>
                    <p>Post-Code</p>
                    <p>
                        {displayProp(
                            gamblerIdentity[0]?.payload?.jumioUserData
                                ?.postalCode,
                            gamblerUnverifiedIdentity[0]?.payload?.postalCode
                        )}
                    </p>
                </div>
            </div>
        </>
    )
}

export default PersonalDetails
