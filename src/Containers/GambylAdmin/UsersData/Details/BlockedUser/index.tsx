import { BlockedService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { CreateEvent } from '@daml/ledger'
import React from 'react'

interface Props {
    blockedContracts: readonly CreateEvent<
        BlockedService,
        BlockedService.Key,
        string
    >[]
    setOpenUnBlock: (val: boolean) => void
    setOpenBlock: (val: boolean) => void
}

const BlockedUser = ({
    blockedContracts,
    setOpenUnBlock,
    setOpenBlock
}: Props) => {
    return (
        <>
            <h3 className="marginTop20">Block/Unblock user</h3>
            <hr />
            <div className="marginTop20">
                {blockedContracts.length > 0 ? (
                    // STYLE HERE
                    <div style={{ display: 'flex', gap: '20px' }}>
                        <button
                            className="gambyladminpanel__logout"
                            onClick={() => setOpenUnBlock(true)}
                        >
                            Unblock
                        </button>
                        <div>
                            <h4>Blocked Reason:</h4>
                            <p>{blockedContracts[0].payload.reason}</p>
                        </div>
                    </div>
                ) : (
                    <button
                        className="gambyladminpanel__logout"
                        onClick={() => setOpenBlock(true)}
                    >
                        Block
                    </button>
                )}
            </div>
        </>
    )
}

export default BlockedUser
