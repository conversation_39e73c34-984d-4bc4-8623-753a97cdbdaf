import * as Yup from 'yup'

export const ENUM_REASON = {
    selfExclusion: 'Self Exclusion',
    underLegalAge: 'Under Legal Age',
    fraudulentBehaviour: 'Fraudulent Behaviour',
    other: 'Other'
}

export const INIT_STATE_BLOCK = { reason: "", otherReason: "" }

export type TBlockUserForm = typeof INIT_STATE_BLOCK

export const validationSchema = Yup.object({
    reason: Yup.string()
        .min(1)
        .required('Reason is a required field'),
    otherReason:
        Yup.string()
            .when("reason", {
                is: (reason: string) => reason === ENUM_REASON.other,
                then: Yup.string().min(1)
                    .required('Other reason is a required  field'),
            })
})
