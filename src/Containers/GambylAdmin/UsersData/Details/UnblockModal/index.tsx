import React from 'react'
import { CreateEvent } from '@daml/ledger'
import { BlockedService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'

import BasicModal from 'Components/Modals/BasicModal'
import { GambylAdminContext } from 'Containers/GambylAdmin/GambylLedgerWrapper'
import { toast } from 'react-toastify'

const UnblockModal = ({
    isOpen,
    handleClose,
    blockedContracts
}: { isOpen: boolean, handleClose: () => void, blockedContracts: readonly CreateEvent<BlockedService, BlockedService.Key, string>[] }) => {
    let ledger = GambylAdminContext.useLedger()


    const testUnBlock = () => {
        if (!blockedContracts.length) {
            return toast.error("Something went wrong, please try again later")
        }
        ledger.exercise(BlockedService.Reinstate, blockedContracts[0].contractId, {})
            .then(() => toast.success("Requested user unblock"))
            .catch(() => toast.error("Something went wrong, please try again later"))
            .finally(() => handleClose())
    }

    return (
        <BasicModal
            isOpenModal={isOpen}
            body={<p>Are you sure, you want to unblock this User? They will be able to access their Gambyl account</p>}
            footerBody={
                <>
                    <button className="btn btn__primary" onClick={testUnBlock}>
                        Yes
                    </button>
                    <button className="btn btn__grey" onClick={handleClose}>
                        No
                    </button>
                </>
            }
            handleClose={handleClose} />
    )
}

export default UnblockModal