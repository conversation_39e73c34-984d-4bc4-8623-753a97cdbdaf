import React from 'react'
import {
    useReactTable,
    createColumnHelper,
    getCoreRowModel,
    flexRender,
    getPaginationRowModel,
    getFilteredRowModel
} from '@tanstack/react-table'
import GenericTable from 'Components/Table/Generic'
import { CreateEvent } from '@daml/ledger'
import { TransactionHistory } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import numeral from 'numeral'
import { Account } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Account/Model'
import Pagination from 'Components/Table/Pagination'
import { toast } from 'react-toastify'
import { generateCSVDownload } from 'Utils/generateCsvDownload'

interface Props {
    account: readonly CreateEvent<Account, Account.Key, string>[]
    transactionHistory: readonly CreateEvent<
        TransactionHistory,
        TransactionHistory.Key,
        string
    >[]
    totalInAccount: () => string
    emailAddress: string
    setIsLoadingCsv: (val: boolean) => void
}

const TransactionHistorySection = ({
    transactionHistory,
    totalInAccount,
    account,
    emailAddress,
    setIsLoadingCsv
}: Props) => {
    const columnHelper = createColumnHelper<{
        date: string
        type: string
        transactionId: string
        amount: string
    }>()
    const columns = [
        columnHelper.accessor('date', {
            header: 'Date',
            cell: info => info.getValue()
        }),
        columnHelper.accessor('type', {
            header: 'Type',
            cell: info => info.getValue()
        }),
        columnHelper.accessor('transactionId', {
            header: 'Transaction ID',
            cell: info => info.getValue()
        }),
        columnHelper.accessor('amount', {
            header: 'Amount',
            cell: info => info.getValue()
        })
    ]

    let data = transactionHistory
        .map(contract => ({
            date: contract.payload.terminatedAt.slice(0, 10),
            type: contract.payload.transactionType,
            transactionId: contract.payload.transactionId,
            amount: numeral(contract.payload.confirmedAmount).format('$0,0.00')
        }))
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: false
    })

    const getTransactionHistoryCsv = () => {
        setIsLoadingCsv(true)
        generateCSVDownload(data, `transaction-history-${emailAddress}`)
        setIsLoadingCsv(false)
        toast.success('Download complete.')
    }
    return (
        <>
            <h3 className="marginTop20">Account Balance:</h3>
            <hr />
            <div className="gambyladminpanel__userDetails">
                <div>
                    <p>Total Funds in Account</p>
                    <p>{totalInAccount()}</p>
                </div>
                <div>
                    <p>Pending Wagers</p>
                    <p>
                        {numeral(account[0]?.payload.totalBetBalance).format(
                            '$0,0.00'
                        )}
                    </p>
                </div>
                <div>
                    <p>Pending Withdraws</p>
                    <p>
                        {numeral(
                            account[0]?.payload.totalWithdrawBalance
                        ).format('$0,0.00')}
                    </p>
                </div>
                <div>
                    <p>Bonus Available</p>
                    <p>
                        {numeral(account[0]?.payload.totalBonusBalance).format(
                            '$0,0.00'
                        )}
                    </p>
                </div>
                <div>
                    <p>Total Fees paid</p>
                    <p>
                        {numeral(account[0]?.payload.totalFees).format(
                            '$0,0.00'
                        )}
                    </p>
                </div>
                <div>
                    <p>Available Balance</p>
                    <p>
                        {numeral(account[0]?.payload.totalMainBalance).format(
                            '$0,0.00'
                        )}
                    </p>
                </div>
            </div>
            <h3 className="marginTop20">Transaction History:</h3>
            <hr />
            <div className="gambyladminpanel__containerDownloadNoTitle">
                <button
                    className="registerbtn__nextStep"
                    onClick={() => getTransactionHistoryCsv()}
                    disabled={!data.length}
                >
                    Get Transaction History CSV
                </button>
            </div>
            {data.length > 0 ? (
                <>
                    {' '}
                    <GenericTable
                        tableHeader={table
                            .getHeaderGroups()
                            .map(headerGroup => (
                                <tr key={headerGroup.id}>
                                    {headerGroup.headers.map(header => (
                                        <th key={header.id}>
                                            {header.column.columnDef.header}
                                        </th>
                                    ))}
                                </tr>
                            ))}
                        tableBodyRow={table.getRowModel().rows.map(row => (
                            <tr key={row.id}>
                                {row.getVisibleCells().map(cell => (
                                    <td key={cell.id}>
                                        {flexRender(
                                            cell.column.columnDef.cell,
                                            cell.getContext()
                                        )}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    />
                    <Pagination table={table} />
                </>
            ) : (
                <p>No data available</p>
            )}
        </>
    )
}

export default TransactionHistorySection
