import React from 'react'
import { Field, Form, useFormikContext } from 'formik'
import { ENUM_REASON, TBlockUserForm } from '../../utils'

const BlockForm = () => {
    const { values: { reason }, setFieldValue } = useFormikContext<TBlockUserForm>()

    React.useEffect(() => {
        setFieldValue("otherReason", "")
    }, [reason, setFieldValue])

    return (
        <Form role="group" aria-labelledby="my-radio-group">
            <h3 className='gambyladminpanel__blockHeader'>Please  select a reason to block this user:</h3>
            {React.Children.toArray(Object.keys(ENUM_REASON).map((reason) =>
                <div className='gambyladminpanel__blockForm'>
                    <label htmlFor="reason">{ENUM_REASON[reason as keyof typeof ENUM_REASON]}</label>
                    <Field type="radio" name="reason" value={ENUM_REASON[reason as keyof typeof ENUM_REASON]} />
                </div>))}
            {reason === "Other" ?
                <div className='gambyladminpanel__otherReason'>
                    <label htmlFor="otherReason">Reason:</label>
                    <Field className='register__input' type="text" name="otherReason" />
                </div>
                : null}
        </Form>
    )
}

export default BlockForm