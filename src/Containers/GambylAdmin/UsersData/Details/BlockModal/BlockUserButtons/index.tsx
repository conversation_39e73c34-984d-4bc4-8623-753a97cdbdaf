import React from 'react'
import { useFormikContext } from 'formik'
import { TBlockUserForm } from '../../utils'

const BlockUserButtons = ({ finalStep, handleClose, handleStep }:
    { finalStep: boolean, handleClose: () => void, handleStep: () => void }) => {
    const { resetForm, submitForm, isValid, validateForm } = useFormikContext<TBlockUserForm>()

    function ManageClose() {
        resetForm()
        handleClose()
    }

    React.useEffect(() => {
        validateForm()
    }, [validateForm])

    return (
        <>
            {
                finalStep ?
                    <button disabled={!isValid} className="btn btn__primary" onClick={submitForm}>
                        Yes
                    </button> :
                    <button disabled={!isValid} className="btn btn__primary" onClick={handleStep}>
                        Confirm
                    </button>
            }
            <button className="btn btn__grey" onClick={ManageClose}>
                No
            </button>
        </>
    )
}

export default BlockUserButtons