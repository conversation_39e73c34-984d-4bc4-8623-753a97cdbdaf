import React from 'react'
import { Formik } from 'formik'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'

import { GambylAdminContext } from 'Containers/GambylAdmin/GambylLedgerWrapper'
import BasicModal from 'Components/Modals/BasicModal'
import BlockForm from './BlockForm'
import BlockUserButtons from './BlockUserButtons'
import ConfirmBlock from './ConfirmBlock'
import { toast } from 'react-toastify'
import { CreateEvent } from '@daml/ledger'
import { INIT_STATE_BLOCK, validationSchema, ENUM_REASON } from '../utils'

const FormWrapper = ({ finalStep }: { finalStep: boolean }) => finalStep ? <ConfirmBlock /> : <BlockForm />

const BlockModal = ({ isOpen, handleClose, serviceContracts }: { isOpen: boolean, handleClose: () => void, serviceContracts: readonly CreateEvent<Service, Service.Key, string>[] }) => {
    let ledger = GambylAdminContext.useLedger()
    let gambylParty = GambylAdminContext.useParty()
    const [isFinalStep, setIsFinalStep] = React.useState(false)

    const closeModal = () => {
        setIsFinalStep(false)
        handleClose()
    }

    const BlockExercise = (reason: string) => {
        ledger.exercise(Service.BlockService, serviceContracts[0].contractId, { party: gambylParty, reason })
            .then(() => toast.success("Requested user block"))
            .catch(() => toast.error("Something went wrong, please try again later"))
            .finally(() => closeModal())
    }

    return (
        <Formik
            initialValues={INIT_STATE_BLOCK}
            validationSchema={validationSchema}
            onSubmit={async (values, { resetForm }) => {
                const { reason, otherReason } = values
                const reasonToPass = reason === ENUM_REASON.other ? otherReason : reason
                await BlockExercise(reasonToPass)
                resetForm()
            }}>
            <BasicModal
                isOpenModal={isOpen}
                body={<FormWrapper finalStep={isFinalStep} />}
                footerBody={
                    <BlockUserButtons
                        finalStep={isFinalStep}
                        handleClose={closeModal}
                        handleStep={() => setIsFinalStep(true)}
                    />}
                handleClose={closeModal} />
        </Formik>
    )
}

export default BlockModal