import React from 'react'
import { Link } from 'react-router-dom'
import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table'
import { GamblerUnverifiedIdentity } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Identity/Model'
import { BlockedService } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'

import { GambylAdminContext } from 'Containers/GambylAdmin/GambylLedgerWrapper'
import LoaderSpinner from 'Components/Loader'
import GenericTable from 'Components/Table/Generic'
import Clipboard from 'Components/Clipboard'
import { RECORDS_PER_PAGE } from './constants'
import useQueryParam from 'Hooks/useQueryParam'
import { deleteQueryParam } from '../utils/deleteQueryParam'

import { toast } from 'react-toastify'
import { generateCSVDownload } from 'Utils/generateCsvDownload'

const PAGE_PARAM = 'fromPage'
const SEARCH_PARAM = 'searchQuery'

export default function UserData() {
    const {
        contracts: gamblerUnverifiedIdentityContracts,
        loading: loadingGamblerUnverifiedIdentityContracts
    } = GambylAdminContext.useQuery(GamblerUnverifiedIdentity)
    const { contracts: blockedContracts, loading: blockedLoading } =
        GambylAdminContext.useStreamQueries(BlockedService)
    const queryParam = useQueryParam()
    const pageURL = queryParam.get(PAGE_PARAM)
    const searchURL = queryParam.get(SEARCH_PARAM)
    const colFiltersInitState = [{ id: 'email', value: '' }]
    const [loadingCsv, setIsLoadingCsv] = React.useState(false)

    const [columnFilters, setColumnFilters] =
        React.useState(colFiltersInitState)

    const columnHelper = createColumnHelper<{
        partyId: string
        firstName: string
        lastName: string
        email: string
        accountStatus: string
    }>()
    const columns = [
        columnHelper.accessor('partyId', {
            header: 'party id',
            cell: info => (
                <>
                    {info.getValue().slice(0, 8)}...
                    <Clipboard contenToCopy={info.getValue()} />
                </>
            )
        }),
        columnHelper.accessor('firstName', {
            header: 'first name',
            cell: info => info.getValue()
        }),
        columnHelper.accessor('lastName', {
            header: 'last name',
            cell: info => info.getValue()
        }),
        columnHelper.accessor('email', {
            header: 'email',
            cell: info => info.getValue()
        }),
        columnHelper.accessor('accountStatus', {
            header: 'Account Status',
            cell: info => info.getValue()
        }),
        columnHelper.accessor('partyId', {
            header: '',
            cell: info => (
                <Link
                    className="gambyladminpanel__link"
                    to={`/gambyladmin/user/${info.getValue()}?fromPage=${
                        table.getState().pagination.pageIndex + 1
                    }${
                        columnFilters[0].value.length > 0
                            ? `&searchQuery=${columnFilters[0].value}`
                            : ''
                    }`}
                >
                    See Details
                </Link>
            )
        })
    ]

    let data = gamblerUnverifiedIdentityContracts.map(contract => ({
        partyId: contract.payload.customer,
        firstName: contract.payload.firstName,
        lastName: contract.payload.lastName,
        email: contract.payload.emailAddress,
        accountStatus:
            blockedContracts.filter(
                bc => bc.payload.service.customer === contract.payload.customer
            ).length > 0
                ? 'Blocked'
                : 'Active'
    }))

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: false,
        state: {
            columnFilters
        }
    })

    const handleSearch = (val: string) => {
        setColumnFilters([{ id: 'email', value: val }])
        table.setPageIndex(0)
    }

    React.useEffect(() => {
        if (pageURL && searchURL) {
            setColumnFilters([{ id: 'email', value: searchURL }])
            table.setPageIndex(Number(pageURL) - 1)
            deleteQueryParam(PAGE_PARAM)
            deleteQueryParam(SEARCH_PARAM)
            return () => {}
        }
        if (pageURL) {
            table.setPageIndex(Number(pageURL) - 1)
            deleteQueryParam(PAGE_PARAM)
            return () => {}
        }
    }, [pageURL, table, searchURL])

    const getUsersCsv = () => {
        setIsLoadingCsv(true)
        generateCSVDownload(data, 'users-list')
        setIsLoadingCsv(false)
        toast.success('Download complete.')
    }

    return (
        <>
            {loadingGamblerUnverifiedIdentityContracts ||
            blockedLoading ||
            loadingCsv ? (
                <LoaderSpinner />
            ) : (
                <>
                    <div className="gambyladminpanel__searchContainer">
                        <input
                            className="register__input"
                            type="text"
                            placeholder="Filter by email"
                            value={columnFilters[0].value}
                            onChange={e => handleSearch(e.target.value)}
                        />
                        <button
                            className="registerbtn__nextStep"
                            onClick={getUsersCsv}
                        >
                            Get CSV
                        </button>
                    </div>
                    <GenericTable
                        tableHeader={table
                            .getHeaderGroups()
                            .map(headerGroup => (
                                <tr key={headerGroup.id}>
                                    {headerGroup.headers.map(header => (
                                        <th key={header.id}>
                                            {header.column.columnDef.header}
                                        </th>
                                    ))}
                                </tr>
                            ))}
                        tableBodyRow={table.getRowModel().rows.map(row => (
                            <tr key={row.id}>
                                {row.getVisibleCells().map(cell => (
                                    <td key={cell.id}>
                                        {flexRender(
                                            cell.column.columnDef.cell,
                                            cell.getContext()
                                        )}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    />
                    <div className="gambyladminpanel__paginationContainer">
                        <div className="reactTable__pagination">
                            <button
                                className={
                                    !table.getCanPreviousPage()
                                        ? 'reactTable__pagination--disabled'
                                        : 'reactTable__pagination--available'
                                }
                                onClick={() => table.setPageIndex(0)}
                                disabled={!table.getCanPreviousPage()}
                            >
                                {'<<'}
                            </button>
                            <button
                                className={
                                    !table.getCanPreviousPage()
                                        ? 'reactTable__pagination--disabled'
                                        : 'reactTable__pagination--available'
                                }
                                onClick={() => table.previousPage()}
                                disabled={!table.getCanPreviousPage()}
                            >
                                {'<'}
                            </button>
                            <strong>
                                {table.getState().pagination.pageIndex + 1} of{' '}
                                {table.getPageCount()}
                            </strong>
                            <button
                                className={
                                    !table.getCanNextPage()
                                        ? 'reactTable__pagination--disabled'
                                        : 'reactTable__pagination--available'
                                }
                                onClick={() => table.nextPage()}
                                disabled={!table.getCanNextPage()}
                            >
                                {'>'}
                            </button>
                            <button
                                className={
                                    !table.getCanNextPage()
                                        ? 'reactTable__pagination--disabled'
                                        : 'reactTable__pagination--available'
                                }
                                onClick={() =>
                                    table.setPageIndex(table.getPageCount() - 1)
                                }
                                disabled={!table.getCanNextPage()}
                            >
                                {'>>'}
                            </button>
                        </div>
                        <div className="gambyladminpanel__paginationSelect">
                            <select
                                value={table.getState().pagination.pageSize}
                                onChange={e => {
                                    table.setPageSize(Number(e.target.value))
                                }}
                                className="register__input marginTop0"
                            >
                                {RECORDS_PER_PAGE.map(pageSize => (
                                    <option key={pageSize} value={pageSize}>
                                        {pageSize} records
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </>
            )}
        </>
    )
}
