import React from 'react'
import { CreateEvent } from '@daml/ledger';
import { GlobalGamblingConfiguration } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Model'

import { publicContext } from 'Containers/App';

export const useFetchGlobalGamblingConfig = () => {
    const publicLedger = publicContext.useLedger()
    const [GlobalGamblingConfigurationContract, setGlobalGamblingConfigurationContract] = React.useState<readonly CreateEvent<
        GlobalGamblingConfiguration,
        GlobalGamblingConfiguration.Key,
        string
    >[]>([])
    const [GlobalGamblingConfigurationLoader, setGlobalGamblingConfigurationLoader] = React.useState(false)

    const fetchGlobalGamblingConfiguration = React.useCallback(async () => {
        setGlobalGamblingConfigurationLoader(true)
        try {
            const response = await publicLedger.query(GlobalGamblingConfiguration);
            setGlobalGamblingConfigurationContract(response)
        } catch (e) {
            console.error("error on fetch global configs", e)
            setGlobalGamblingConfigurationContract([])
        }
        finally {
            setGlobalGamblingConfigurationLoader(false)
        }
    }, [publicLedger])

    React.useEffect(() => {
        fetchGlobalGamblingConfiguration()
    }, [fetchGlobalGamblingConfiguration])

    return { GlobalGamblingConfigurationContract, GlobalGamblingConfigurationLoader, fetchGlobalGamblingConfiguration }
}
