import React from 'react'
import { useFormikContext, Field, FormikErrors } from 'formik'

import { IData } from '..'
import descriptCamelCase from 'Containers/Dashboard/descriptCamelCase'
import ErrorMessage from 'Components/RegisterUser/ErrorMessage'

import '../style.scss'

const oddOptions = ['Decimal', 'Fractional', 'Moneyline']

const PercentageMessage = ({ value }: { value: string }) => {
    return (
        <p className="attentionMessage">
            <strong>Attention:</strong> This field is displayed as a decimal
            type. Be aware that it has a range that goes from 0.01 - equivalent
            to 1% - to 1 - equivalent to 100%.{' '}
            <strong>Current discount value: {Number(value) * 100}%</strong>
        </p>
    )
}

const ConfigInput = ({ val, values }: { val: string; values: IData }) => (
    <>
        <label htmlFor={val}>{descriptCamelCase(val)}</label>
        <Field className="register__input" type="text" name={val} />
        {val === 'betFee' || val === 'depositFee' || val === 'withdrawFee' ? (
            <PercentageMessage value={values[val]} />
        ) : null}
    </>
)

const ConfigSelectInput = ({ val }: { val: string }) => (
    <>
        <label htmlFor={val}>{descriptCamelCase(val)}</label>
        <Field className="register__input" as="select" name={val}>
            {oddOptions.map(option => (
                <option key={option} value={option} label={option}>
                    {option}
                </option>
            ))}
        </Field>
    </>
)

const ConfigErrors = ({
    errors,
    val
}: {
    errors: FormikErrors<IData>
    val: string
}) =>
    typeof errors[val as keyof IData] === 'string' &&
    errors[val as keyof IData] !== undefined ? (
        <ErrorMessage message={errors[val as keyof IData] as string} />
    ) : null

const UpdateConfigsForm = ({ handleClick }: { handleClick: () => void }) => {
    const { validateForm, values, isValid, errors } = useFormikContext<IData>()

    const validateCallBack = React.useCallback(() => {
        validateForm()
    }, [validateForm])

    React.useEffect(() => {
        validateCallBack()
    }, [validateCallBack])

    return (
        <div className="configPage">
            <div className="configPage__gridcontainer">
                {React.Children.toArray(
                    Object.keys(values).map(val => (
                        <div>
                            {val !== 'defaultOdds' ? (
                                <ConfigInput val={val} values={values} />
                            ) : (
                                <ConfigSelectInput val={val} />
                            )}
                            <ConfigErrors val={val} errors={errors} />
                        </div>
                    ))
                )}
            </div>
            <button
                disabled={!isValid}
                className="registerbtn__nextStep"
                onClick={handleClick}
            >
                Submit
            </button>
        </div>
    )
}

export default UpdateConfigsForm
