import React from 'react'

import BasicModal from 'Components/Modals/BasicModal'
import { useFormikContext } from 'formik'
import LoaderSpinner from 'Components/Loader'

import "./style.scss"


const BodyMarkup = ({ isLoading }: { isLoading: boolean }) => isLoading ? <LoaderSpinner className='loader__modal' /> :
    <p>Are you sure you want to update the global gambling configurations?</p>

const FooterMarkup = ({ isLoading, submitForm, handleClose }: { isLoading: boolean, submitForm: () => void, handleClose: () => void }) => isLoading ? null :
    <>
        <button className="btn btn__primary" onClick={submitForm}>
            Yes
        </button>
        <button className="btn btn__grey" onClick={handleClose}>
            No
        </button>
    </>


const ConfirmModal = ({ isOpen, handleClose }: { isOpen: boolean, handleClose: () => void }) => {
    const { submitForm, isSubmitting } = useFormikContext()

    return (
        <BasicModal
            isOpenModal={isOpen}
            body={<BodyMarkup isLoading={isSubmitting} />}
            footerBody={
                <FooterMarkup isLoading={isSubmitting} submitForm={submitForm} handleClose={handleClose} />
            }
            handleClose={handleClose}
            shouldCloseOnOverlayClickProp={false}
        />
    )
}

export default ConfirmModal
