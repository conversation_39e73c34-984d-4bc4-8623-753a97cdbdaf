import * as Yup from 'yup'
import { regexDecimal } from 'Components/Admin/Form/constants'

const wholeNumberRegex = /^\d+$/

export const ValidationSchemaConfigs = Yup.object({
    betFee: Yup.string()
        .min(1)
        .matches(regexDecimal, 'Please add a valid Bet Fee value')
        .required('Bet Fee title is a required field'),
    depositFee: Yup.string()
        .min(1)
        .matches(regexDecimal, 'Please add a valid Deposit Fee value')
        .required('Deposit Fee is a required field'),
    minDepositAmount: Yup.string()
        .min(1)
        .matches(regexDecimal, 'Please add a valid Min Deposit Amount value')
        .required('Min Deposit Amount is a required field'),
    minWithdrawAmount: Yup.string()
        .min(1)
        .matches(regexDecimal, 'Please add a valid Min Withdraw Amount value')
        .required('Min Withdraw Amount is a required field'),
    withdrawFee: Yup.string()
        .min(1)
        .matches(regexDecimal, 'Please add a valid Withdraw Fee value')
        .required('Withdraw Fee is a required field'),
    unverifiedAccountMaxAmount: Yup.string()
        .min(1)
        .matches(regexDecimal, 'Please add a valid Unverified Account Max Amount value')
        .required('Unverified Account Max Amount is a required field'),
    minOdd: Yup.string()
        .min(1)
        .matches(regexDecimal, 'Please add a valid Min Odd value')
        .required('Min Odd title is a required field'),
    allowedPeriod: Yup.string()
        .min(1)
        .matches(wholeNumberRegex, 'Please add a valid Allowed Period value')
        .required('Allowed Period title is a required field'),
})