import React from 'react'
import { Formik } from 'formik'
import * as damlTypes from '@daml/types'
import { Role } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Role'
import { OddType } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Bet/Odds/Model'
import { toast } from 'react-toastify'

import { GambylAdminContext } from 'Containers/GambylAdmin/GambylLedgerWrapper'

import LoaderSpinner from 'Components/Loader'
import UpdateConfigsForm from './Form'
import { ValidationSchemaConfigs } from './utils'
import ConfirmModal from './ConfirmModal'
import { useFetchGlobalGamblingConfig } from './Hooks/useFetchGlobalGamblingConfig'

export interface IData {
    betFee: string
    depositFee: string
    minDepositAmount: string
    minWithdrawAmount: string
    withdrawFee: string
    unverifiedAccountMaxAmount: string
    minOdd: string
    allowedPeriod: string
    defaultOdds: 'Fractional' | 'Decimal' | 'Moneyline'
}

const Configs = () => {
    const {
        GlobalGamblingConfigurationContract,
        GlobalGamblingConfigurationLoader,
        fetchGlobalGamblingConfiguration
    } = useFetchGlobalGamblingConfig()
    const [openModal, setOpenModal] = React.useState(false)
    const gambylAdminLedger = GambylAdminContext.useLedger()
    const { loading: loadingRole, contracts: roleContracts } =
        GambylAdminContext.useQuery(Role)

    if (GlobalGamblingConfigurationLoader || loadingRole) {
        return <LoaderSpinner />
    }

    if (GlobalGamblingConfigurationContract.length < 0) {
        return (
            <div className="gambyladminpanel__centeredContainer">
                <p>
                    There is no data available to show you, please try again
                    later.
                </p>
            </div>
        )
    }

    const {
        betFee,
        depositFee,
        minDepositAmount,
        minOdd,
        minWithdrawAmount,
        allowedPeriod,
        unverifiedAccountMaxAmount,
        withdrawFee,
        defaultOdds
    } = GlobalGamblingConfigurationContract[0].payload

    const initData: IData = {
        betFee,
        depositFee,
        minDepositAmount,
        minWithdrawAmount,
        withdrawFee,
        unverifiedAccountMaxAmount,
        minOdd: minOdd.value,
        allowedPeriod,
        defaultOdds: defaultOdds.tag
    }

    function handleSubmit(values: IData) {
        if (!roleContracts.length) {
            return toast.error('Something went wrong, please try again later')
        }
        const {
            betFee,
            depositFee,
            minDepositAmount,
            minWithdrawAmount,
            withdrawFee,
            unverifiedAccountMaxAmount,
            minOdd,
            allowedPeriod,
            defaultOdds
        } = values
        const empty = damlTypes.emptyMap<string, damlTypes.Party>()
        let choiceConstructor = {
            newFlaggedAmount: null,
            newUnverifiedAccountMaxAmount: unverifiedAccountMaxAmount,
            newMinDepositAmount: minDepositAmount,
            newMinWithdrawAmount: minWithdrawAmount,
            newBetFee: betFee,
            newDepositFee: depositFee,
            newWithdrawFee: withdrawFee,
            newArchiveEventDays: null,
            newLegalAge: null,
            newIsOnMaintenance: null,
            newDaysPostponedEventExpires: null,
            newMinOdd: {
                tag: 'Decimal',
                value: minOdd
            } as unknown as OddType,
            newMinutesMarketMapExpires: null,
            newIntegrationParties: empty as damlTypes.Map<
                string,
                damlTypes.Party
            >,
            newAllowedPeriod: allowedPeriod,
            newMinStake: null,
            newDefaultOdds: {
                tag: defaultOdds,
                value: '0'
            }
        }
        gambylAdminLedger
            .exercise(
                Role.ChangeGlobalGamblingConfiguration,
                roleContracts[0].contractId,
                choiceConstructor
            )
            .then(() => {
                toast.success('Requested change on global configurations')
                setOpenModal(false)
            })
            .catch(() => {
                toast.error('Something went wrong, please try again later')
                setOpenModal(false)
            })
            .finally(() => fetchGlobalGamblingConfiguration())
    }

    return (
        <div className="gambyladminpanel__centeredContainer">
            <Formik
                initialValues={initData}
                onSubmit={async values => {
                    await handleSubmit(values)
                }}
                validationSchema={ValidationSchemaConfigs}
            >
                <>
                    <UpdateConfigsForm handleClick={() => setOpenModal(true)} />
                    <ConfirmModal
                        isOpen={openModal}
                        handleClose={() => setOpenModal(false)}
                    />
                </>
            </Formik>
        </div>
    )
}

export default Configs
