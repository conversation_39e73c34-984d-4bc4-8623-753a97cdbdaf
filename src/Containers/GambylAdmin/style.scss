@import '../../Styles/colors';

.gambyladminpanel {
    background-attachment: fixed;
    background-color: $black;
    background-image: url('../../Assets/coverBGRegister.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $gray;

    &__container {
        width: 90%;
        background: $white;
        padding: 40px;
        border-radius: 6px;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: center;
        height: 100%;
        max-height: 700px;
        overflow-y: scroll;
    }

    &__main {
        width: 100%;

        section {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
        }
    }

    &__centeredContainer {
        display: flex;
        justify-content: center;
    }

    &__form {
        height: 100%;
        max-width: 450px;
        width: 50%;
        display: flex;
        flex-direction: column;
        gap: 20px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
    }

    &__userDetails {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 20px;
        padding: 20px 0;

        & > div {
            display: flex;
            flex-direction: column;
            gap: 10px;

            & p:first-of-type {
                font-weight: bold;
            }
        }
    }

    &__logout {
        padding: 5px 10px;
        background: $purple;
        border: none;
        border-radius: 6px;
        color: $white;
        cursor: pointer;
        font-size: 0.813rem;
        font-weight: bold;
        line-height: 1.563rem;
        text-align: center;
        transition: filter 0.2s;
        &:hover {
            filter: brightness(0.9);
        }
    }

    &__toggleMaintenance {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 50px;

        label {
            font-weight: bold;
            text-transform: capitalize;
        }
    }

    &__link {
        text-decoration: none;
        font-weight: bold;
        color: $purple;
    }

    &__backarrow {
        font-style: normal;
        font-weight: bold;
        font-size: 1rem;
        line-height: 25px;
        color: $purple;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 10px;
        cursor: pointer;
    }

    &__searchContainer {
        width: 100%;
        padding-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;

        input {
            width: 350px;
        }

        button {
            width: 150px;
        }
    }

    &__paginationContainer {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    &__paginationSelect {
        width: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &__blockHeader {
        padding: 0 0 10px 0;
    }

    &__blockForm {
        display: flex;
        justify-content: space-between;
        gap: 20px;
        padding: 5px 0;
    }

    &__otherReason {
        text-align: left;
        padding: 5px 0;
    }

    &__containerWithDownloadBtn {
        display: flex;
        justify-content: space-between;

        button {
            width: 200px;
        }
    }

    &__containerDownloadNoTitle {
        display: flex;
        justify-content: flex-end;

        button {
            width: 200px;
            margin-bottom: 10px;
        }
    }
}
