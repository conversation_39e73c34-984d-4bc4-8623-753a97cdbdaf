import React from 'react'
import { Formik } from 'formik'
import * as Yup from 'yup'

const LoginSchema = Yup.object({
    gambylAdminToken: Yup.string().required('Required field')
})

export default function GambylAdminLogin(
    { handleLogin }:
        { handleLogin: (tkn: string) => void }) {

    return (
        <Formik
            initialValues={{ gambylAdminToken: '' }}
            onSubmit={({ gambylAdminToken }) => {
                handleLogin(gambylAdminToken)
            }}
            validationSchema={LoginSchema}
        >
            {formik => (
                <form
                    className='gambyladminpanel__form'
                    onSubmit={formik.handleSubmit}
                >
                    <h2>Admin Area</h2>
                    <input
                        id="gambylAdminToken"
                        placeholder="Please add your token"
                        className='register__input'
                        {...formik.getFieldProps('gambylAdminToken')}
                    />
                    <button className='registerbtn__nextStep' type="submit">
                        Sign In
                    </button>
                </form>
            )}
        </Formik>
    )
}
