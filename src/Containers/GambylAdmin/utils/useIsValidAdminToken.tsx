import React from 'react'
import { useLocation } from 'react-router-dom'

import { isLocalDev } from 'config'
import { isTokenExpired } from 'Utils/isLocalTokenExpired'
import { cache } from 'Utils/cache'
import { isAdminTokenExpired } from 'Utils/validateGambylAdminToken'

const { remove } = cache()

export default function useIsValidAdminToken(
    token: string,
    logout: () => void
) {
    const location = useLocation()

    React.useEffect(() => {
        if (!isLocalDev) {
            return isAdminTokenExpired(token)
                ? () => {
                      remove('bets')
                      logout()
                  }
                : () => {}
        }
        return isTokenExpired(token)
            ? () => {
                  remove('bets')
                  logout()
              }
            : () => {}

        //eslint-disable-next-line
    }, [location])
}
