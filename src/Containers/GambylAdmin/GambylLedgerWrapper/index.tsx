import React from 'react'
import IdleTimer from 'react-idle-timer'
import { Switch, Route, useHistory, useLocation } from 'react-router'
import { createLedgerContext } from '@daml/react'

import ToggleMaintenance from 'Containers/GambylAdmin/ToggleMaintenance'
import BonusMoney from 'Containers/GambylAdmin/BonusMoney'
import ResetKYC from 'Containers/GambylAdmin/ResetKYC'
import UsersData from 'Containers/GambylAdmin/UsersData'
import UserDetails from 'Containers/GambylAdmin/UsersData/Details'
import TabsWithoutLink from 'Components/TabsWithoutLink'

import { httpBaseUrl, wsBaseUrl } from 'config'
import Configs from '../Configs'
import useIsValidAdminToken from '../utils/useIsValidAdminToken'
import ExpirePromotion from '../ExpirePromotion'

export const GambylAdminContext = createLedgerContext()

export default function GambylLedgerWrapper({
    credentials,
    handleLogout
}: {
    credentials: { token: string; party: string }
    handleLogout: () => void
}) {
    const idleTimer2Ref = React.useRef<any>(null)
    const { push } = useHistory()
    const { pathname } = useLocation()
    const tabs = [
        {
            label: 'Maintenance',
            value: '/gambyladmin',
            onclick: () => push('/gambyladmin')
        },
        {
            label: 'Bonus',
            value: '/gambyladmin/bonus',
            onclick: () => push('/gambyladmin/bonus')
        },
        {
            label: 'Reset KYC',
            value: '/gambyladmin/resetkyc',
            onclick: () => push('/gambyladmin/resetkyc')
        },
        {
            label: 'Users data',
            value: '/gambyladmin/users',
            onclick: () => push('/gambyladmin/users')
        },
        {
            label: 'configuration',
            value: '/gambyladmin/configs',
            onclick: () => push('/gambyladmin/configs')
        },
        {
            label: 'Expire Promotion',
            value: '/gambyladmin/expirepromotion',
            onclick: () => push('/gambyladmin/expirepromotion')
        }
    ]

    const routes = [
        {
            path: '/gambyladmin',
            element: () => <ToggleMaintenance />,
            isExact: true
        },
        {
            path: '/gambyladmin/bonus',
            element: () => <BonusMoney />,
            isExact: true
        },
        {
            path: '/gambyladmin/resetkyc',
            element: () => <ResetKYC />,
            isExact: true
        },
        {
            path: '/gambyladmin/users',
            element: () => <UsersData />,
            isExact: true
        },
        {
            path: '/gambyladmin/configs',
            element: () => <Configs />,
            isExact: true
        },
        {
            path: '/gambyladmin/user/:partyId',
            element: () => <UserDetails />,
            isExact: false
        },
        {
            path: '/gambyladmin/expirepromotion',
            element: () => <ExpirePromotion />,
            isExact: true
        }
    ]

    useIsValidAdminToken(credentials.token, handleLogout)

    return (
        <GambylAdminContext.DamlLedger
            token={credentials.token}
            party={credentials.party}
            httpBaseUrl={httpBaseUrl}
            wsBaseUrl={wsBaseUrl}
        >
            <div className="gambyladminpanel__main">
                <section>
                    <TabsWithoutLink tabs={tabs} selectedTab={pathname} />
                    <button
                        className="gambyladminpanel__logout"
                        onClick={handleLogout}
                    >
                        Logout
                    </button>
                </section>
                <Switch>
                    {React.Children.toArray(
                        routes.map(route => (
                            <Route
                                exact={route.isExact}
                                path={route.path}
                                component={route.element}
                            />
                        ))
                    )}
                </Switch>
                <IdleTimer
                    ref={idleTimer2Ref as any}
                    timeout={60 * 60 * 1000}
                    onIdle={handleLogout}
                />
            </div>
        </GambylAdminContext.DamlLedger>
    )
}
