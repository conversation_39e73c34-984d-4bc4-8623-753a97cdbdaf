import React from 'react'
import { useHistory } from 'react-router'

import GambylLedgerWrapper from './GambylLedgerWrapper'
import GambylAdminLogin from './GambylAdminLogin'

import { validateGambylAdminToken } from 'Utils/validateGambylAdminToken'
import { toast } from 'react-toastify'

import { cache } from 'Utils/cache'

import './style.scss'

const initCredentialsState = { token: '', party: '' }
const CREDENTIALS_STORAGE_GAMBYL_ADMIN = 'GAMBYL_ADMIN_CRED'

export default function GambylAdmin() {
    const { push } = useHistory()
    let { load, save, remove } = cache({ permanent: true })

    const [credentials, setCredentials] = React.useState<{
        token: string
        party: string
    }>(() => {
        const credentialsJson = load(CREDENTIALS_STORAGE_GAMBYL_ADMIN)

        if (!credentialsJson) {
            return initCredentialsState
        }
        return JSON.parse(credentialsJson)
    })

    const handleLogin = async (tkn: string) => {
        if (tkn.length === 0) {
            return toast.error('Wrong credentials')
        }
        let { token, party, isGambylAdmin } = await validateGambylAdminToken(
            tkn
        )
        if (isGambylAdmin) {
            setCredentials({ token, party })
            save(
                CREDENTIALS_STORAGE_GAMBYL_ADMIN,
                JSON.stringify({ token, party })
            )
            return toast.success('Logged in successfully')
        } else {
            return toast.error('Wrong credentials')
        }
    }

    const handleLogout = () => {
        push('/gambyladmin')
        setCredentials(initCredentialsState)
        remove(CREDENTIALS_STORAGE_GAMBYL_ADMIN)
        remove('bets')
    }

    return (
        <main className="gambyladminpanel">
            <div className="gambyladminpanel__container">
                {credentials.party.length > 0 &&
                credentials.token.length > 0 ? (
                    <GambylLedgerWrapper
                        credentials={credentials}
                        handleLogout={handleLogout}
                    />
                ) : (
                    <GambylAdminLogin handleLogin={handleLogin} />
                )}
            </div>
        </main>
    )
}
