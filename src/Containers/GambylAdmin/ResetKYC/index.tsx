import React from 'react'
import * as Yup from 'yup'
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { toast } from 'react-toastify';
import { Formik } from 'formik'

import { GambylAdminContext } from "Containers/GambylAdmin/GambylLedgerWrapper"

import ErrorMessage from 'Components/RegisterUser/ErrorMessage';
import BasicModal from 'Components/Modals/BasicModal';
import LoaderSpinner from 'Components/Loader';

import { generatedKey } from 'Containers/GambylAdmin/utils/keyConstructor';

const ValidationSchema = Yup.object({
    userToResetKYC: Yup.string()
        .required('Party Id to reset is a required field'),
})


export default function ResetKYC() {
    const [isSubmittingRequest, setIsSubmittingRequest] = React.useState(false);
    const [dataToSubmit, setDataToSubmit] = React.useState("")
    const [isConfirmModalOpen, setIsConfirmModalOpen] = React.useState(false);
    const gambylAdminLedger = GambylAdminContext.useLedger();
    const gambylParty = GambylAdminContext.useParty()

    const handleClose = () => {
        setIsConfirmModalOpen(false)
        setDataToSubmit("")
    }

    const resetKYC = (userParty: string) => {
        setIsSubmittingRequest(true)
        gambylAdminLedger.exerciseByKey(Service.RemoveKYC, generatedKey(gambylParty, userParty), {})
            .then(() => toast.success("KYC reseted"))
            .catch(() => toast.error("Something went wrong"))
            .finally(() => {
                handleClose()
                setIsSubmittingRequest(false)
            })
    }


    const handleConfirm = () => {
        if (!dataToSubmit.length) {
            handleClose()
            return toast.error("Something went wrong")
        }
        resetKYC(dataToSubmit)
    }

    return (
        <div className='gambyladminpanel__centeredContainer'>
            <Formik
                initialValues={{ userToResetKYC: "" }}
                onSubmit={({ userToResetKYC }, { resetForm }) => {
                    setDataToSubmit(userToResetKYC)
                    setIsConfirmModalOpen(true)
                    resetForm();
                }}
                validationSchema={ValidationSchema}
            >
                {formik => (
                    <form
                        className='gambyladminpanel__form'
                        onSubmit={formik.handleSubmit}
                    >
                        <h2>Reset KYC</h2>
                        <input
                            id="userToResetKYC"
                            placeholder="Party Id to reset KYC"
                            className='register__input'
                            {...formik.getFieldProps('userToResetKYC')}
                        />
                        {formik.touched.userToResetKYC &&
                            formik.errors.userToResetKYC ? (
                            <ErrorMessage
                                message={formik.errors.userToResetKYC}
                            />
                        ) : null}
                        <button className='registerbtn__nextStep' type="submit">
                            Reset KYC
                        </button>
                    </form>
                )}
            </Formik>
            <BasicModal
                body={
                    isSubmittingRequest ?
                        <LoaderSpinner /> :
                        <p>Would you like to reset the KYC of this party id: <strong>{dataToSubmit}</strong> ?</p>}
                isOpenModal={isConfirmModalOpen}
                handleClose={handleClose}
                footerBody={
                    isSubmittingRequest ? null :
                        <>
                            <button className="btn btn__primary" onClick={handleConfirm}>
                                Yes
                            </button>
                            <button className="btn btn__grey" onClick={handleClose}>
                                No
                            </button>
                        </>
                }
                shouldCloseOnOverlayClickProp={false}
            />
        </div>
    )
}
