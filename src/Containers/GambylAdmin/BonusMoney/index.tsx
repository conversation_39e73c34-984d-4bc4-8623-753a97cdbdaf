import React from 'react'
import CurrencyInput from 'react-currency-input-field'
import * as Yup from 'yup'
import numeral from 'numeral';
import { Service } from '@daml.js/gambyl-ledger/lib/Gambyl/Gambling/Service'
import { toast } from 'react-toastify';
import { Formik } from 'formik'

import { GambylAdminContext } from "Containers/GambylAdmin/GambylLedgerWrapper"

import ErrorMessage from 'Components/RegisterUser/ErrorMessage';
import BasicModal from 'Components/Modals/BasicModal';
import LoaderSpinner from 'Components/Loader';

import { generatedKey } from 'Containers/GambylAdmin/utils/keyConstructor';

const ValidationSchema = Yup.object({
    userToGiveBonusParty: Yup.string()
        .required('Party Id to give bonus is a required field'),
    amount: Yup.string()
        .max(10, 'The bonus input accepts only till 10 digits.')
        .required('Bonus is a required field')
})

const initState = { userToGiveBonusParty: '', amount: "" }

export default function BonusMoney() {
    const [isSubmittingRequest, setIsSubmittingRequest] = React.useState(false);
    const [dataToSubmit, setDataToSubmit] = React.useState<{ userToGiveBonusParty: string, amount: string }>(initState)
    const [isConfirmModalOpen, setIsConfirmModalOpen] = React.useState(false);
    const gambylAdminLedger = GambylAdminContext.useLedger();
    const gambylParty = GambylAdminContext.useParty()

    const handleClose = () => {
        setIsConfirmModalOpen(false)
        setDataToSubmit(initState)
    }

    const giveBonus = (bonusAmount: string, userParty: string) => {
        setIsSubmittingRequest(true)
        gambylAdminLedger.exerciseByKey(Service.GiveBonusBounty, generatedKey(gambylParty, userParty),
            {
                bonusAmount: bonusAmount,
                currency: "USD",
            })
            .then(() => toast.success("Bonus sent to user"))
            .catch(() => toast.error("Something went wrong"))
            .finally(() => {
                handleClose()
                setIsSubmittingRequest(false)
            })
    }


    const handleConfirm = () => {
        if (!dataToSubmit.amount || !dataToSubmit.userToGiveBonusParty) {
            handleClose()
            return toast.error("Something went wrong")
        }
        giveBonus(dataToSubmit.amount, dataToSubmit.userToGiveBonusParty)
    }

    return (
        <div className='gambyladminpanel__centeredContainer'>
            <Formik
                initialValues={initState}
                onSubmit={(values, { resetForm }) => {
                    setDataToSubmit(values)
                    setIsConfirmModalOpen(true)
                    resetForm();
                }}
                validationSchema={ValidationSchema}
            >
                {formik => (
                    <form
                        className='gambyladminpanel__form'
                        onSubmit={formik.handleSubmit}
                    >
                        <h2>Give Bonus</h2>
                        <input
                            id="userToGiveBonusParty"
                            placeholder="Party Id to give bonus"
                            className='register__input'
                            {...formik.getFieldProps('userToGiveBonusParty')}
                        />
                        {formik.touched.userToGiveBonusParty &&
                            formik.errors.userToGiveBonusParty ? (
                            <ErrorMessage
                                message={formik.errors.userToGiveBonusParty}
                            />
                        ) : null}
                        <CurrencyInput
                            intlConfig={{
                                locale: 'en-US',
                                currency: 'USD'
                            }}
                            id="requestedAmount"
                            name="requestedAmount"
                            placeholder={"Amount to give bonus"}
                            decimalsLimit={2}
                            allowNegativeValue={false}
                            step={0.01}
                            value={formik.values.amount}
                            onValueChange={value => {
                                formik.setFieldTouched(
                                    'amount',
                                    true
                                )
                                formik.setFieldValue(
                                    'amount',
                                    value
                                )
                            }}
                            prefix="$"
                            className="register__input"
                        />
                        {formik.touched.amount &&
                            formik.errors.amount ? (
                            <ErrorMessage
                                message={formik.errors.amount}
                            />
                        ) : null}
                        <button className='registerbtn__nextStep' type="submit">
                            Give Bonus
                        </button>
                    </form>
                )}
            </Formik>
            <BasicModal
                body={
                    isSubmittingRequest ?
                        <LoaderSpinner /> :
                        <p>Are you sure you want to give a <strong>{numeral(dataToSubmit.amount).format('$0,0.00')}</strong> bonus to the party id: <strong>{dataToSubmit.userToGiveBonusParty}</strong> ?</p>}
                isOpenModal={isConfirmModalOpen}
                handleClose={handleClose}
                footerBody={
                    isSubmittingRequest ? null :
                        <>
                            <button className="btn btn__primary" onClick={handleConfirm}>
                                Yes
                            </button>
                            <button className="btn btn__grey" onClick={handleClose}>
                                No
                            </button>
                        </>
                }
                shouldCloseOnOverlayClickProp={false}
            />
        </div>
    )
}
