import React, { useState, useEffect } from 'react'

import Stepper from 'Components/Stepper'
import PersonalDetailsForm from 'Components/RegisterUser/PersonalDetails'
import AccountDetails from 'Components/RegisterUser/AccountDetails'
import Security from 'Components/RegisterUser/Security'
import Benefits from 'Components/RegisterUser/Benefits'

import './style.scss'

export interface IPersonalDetails {
    firstName?: string
    lastName?: string
    emailAddress?: string
    country?: string
    postcode?: string
    dateOfBirth?: Date | null
    phoneNumber?: number | null
    currency?: string
    promoCode?: string
    ageConfirm?: boolean
    emailList?: boolean
}

export default function RegisterUser() {
    const [userData, setUserData] = useState<IPersonalDetails>({
        firstName: '',
        lastName: '',
        emailAddress: '',
        country: '',
        postcode: '',
        dateOfBirth: null,
        phoneNumber: null,
        currency: '',
        promoCode: '',
        ageConfirm: false,
        emailList: true
    })

    const [step, setStep] = useState(0)

    const handleNextStep = (data: IPersonalDetails, final: boolean = false) => {
        setUserData(prevState => ({ ...prevState, ...data }))
        if (final) {
            return
        }
        setStep(prev => prev + 1)
    }

    const handlePreviousStep = (data: IPersonalDetails) => {
        setUserData(prevState => ({ ...prevState, ...data }))
        setStep(prev => prev - 1)
    }

    const formSteps = [
        <PersonalDetailsForm
            formData={userData}
            handleNextStep={handleNextStep}
        />,
        <AccountDetails
            formData={userData}
            handleNextStep={handleNextStep}
            handlePreviousStep={handlePreviousStep}
        />,
        <Security
            formData={userData}
            handleNextStep={handleNextStep}
            handlePreviousStep={handlePreviousStep}
        />
    ]

    const stepTitle = ['PERSONAL DETAILS', 'ACCOUNT DETAILS', 'SECURITY']

    return (
        <div className="signup__container">
            <div className="form__container">
                <div>
                    <h2>Sign up for your free account</h2>
                    <div>
                        <p className="step__text">
                            STEP {step + 1}: {stepTitle[step]}
                        </p>
                        <Stepper step={step} />
                        {formSteps[step]}
                    </div>
                </div>
            </div>
            <Benefits />
        </div>
    )
}
