/**
 * J<PERSON><PERSON>hi<PERSON> transform to migrate React 17 to React 18
 *
 * This transform handles:
 * 1. ReactDOM.render() -> createRoot().render()
 * 2. ReactDOM.unmountComponentAtNode() -> root.unmount()
 * 3. ReactDOM.hydrate() -> hydrateRoot()
 * 4. Updates import statements for new APIs
 */

const { statement } = require('jscodeshift')

module.exports = function transformer(fileInfo, api) {
    const j = api.jscodeshift
    const root = j(fileInfo.source)

    let hasChanges = false
    let needsCreateRoot = false
    let needsHydrateRoot = false
    let reactDOMImportPath = null

    // Find ReactDOM import to determine import style
    const reactDOMImports = root.find(j.ImportDeclaration, {
        source: { value: 'react-dom' }
    })

    if (reactDOMImports.length === 0) {
        return fileInfo.source // No ReactDOM imports, nothing to transform
    }

    // Get the import path and style
    reactDOMImports.forEach(path => {
        reactDOMImportPath = path
    })

    // Transform ReactDOM.render() calls
    root.find(j.CallExpression, {
        callee: {
            type: 'MemberExpression',
            object: { name: 'ReactDOM' },
            property: { name: 'render' }
        }
    }).forEach(path => {
        const renderCall = path.value
        const [component, container] = renderCall.arguments

        if (!component || !container) return

        // For the specific pattern in index.tsx, we need to create a root variable
        // and then call render on it
        const parentStatement = j(path).closest(j.ExpressionStatement)

        if (parentStatement.length > 0) {
            // Create root variable declaration
            const rootDeclaration = j.variableDeclaration('const', [
                j.variableDeclarator(
                    j.identifier('root'),
                    j.callExpression(j.identifier('createRoot'), [container])
                )
            ])

            // Create render call
            const renderStatement = j.expressionStatement(
                j.callExpression(
                    j.memberExpression(
                        j.identifier('root'),
                        j.identifier('render')
                    ),
                    [component]
                )
            )

            // Replace the original statement with both statements
            j(parentStatement.get()).replaceWith([
                rootDeclaration,
                renderStatement
            ])

            hasChanges = true
            needsCreateRoot = true
        }
    })

    // Transform ReactDOM.hydrate() calls
    root.find(j.CallExpression, {
        callee: {
            type: 'MemberExpression',
            object: { name: 'ReactDOM' },
            property: { name: 'hydrate' }
        }
    }).forEach(path => {
        const hydrateCall = path.value
        const [component, container] = hydrateCall.arguments

        if (!component || !container) return

        // Create the new hydrateRoot pattern
        const hydrateRootCall = j.callExpression(
            j.memberExpression(
                j.identifier('ReactDOM'),
                j.identifier('hydrateRoot')
            ),
            [container, component]
        )

        j(path).replaceWith(hydrateRootCall)

        hasChanges = true
        needsHydrateRoot = true
    })

    // Transform ReactDOM.unmountComponentAtNode() calls
    // Note: This is more complex as we need to track the root instance
    root.find(j.CallExpression, {
        callee: {
            type: 'MemberExpression',
            object: { name: 'ReactDOM' },
            property: { name: 'unmountComponentAtNode' }
        }
    }).forEach(path => {
        // Add a comment suggesting manual review
        const comment = j.commentBlock(
            ' TODO: Replace with root.unmount() - requires manual review '
        )
        path.value.comments = path.value.comments || []
        path.value.comments.push(comment)
        hasChanges = true
    })

    // Update imports if we made changes
    if (hasChanges && reactDOMImportPath) {
        const importDeclaration = reactDOMImportPath.value

        // Check current import style
        const defaultImport = importDeclaration.specifiers.find(
            spec => spec.type === 'ImportDefaultSpecifier'
        )

        const namedImports = importDeclaration.specifiers.filter(
            spec => spec.type === 'ImportSpecifier'
        )

        if (defaultImport) {
            // Default import style: import ReactDOM from 'react-dom'
            // Add named imports for new APIs
            const newImports = []

            if (needsCreateRoot) {
                newImports.push(j.importSpecifier(j.identifier('createRoot')))
            }
            if (needsHydrateRoot) {
                newImports.push(j.importSpecifier(j.identifier('hydrateRoot')))
            }

            if (newImports.length > 0) {
                // Create a new import for react-dom/client
                const clientImport = j.importDeclaration(
                    newImports,
                    j.literal('react-dom/client')
                )

                // Insert after the existing ReactDOM import
                j(reactDOMImportPath).insertAfter(clientImport)
            }
        } else if (namedImports.length > 0) {
            // Named import style: import { render } from 'react-dom'
            // Update to import from react-dom/client for new APIs
            const newImports = []

            if (needsCreateRoot) {
                newImports.push(j.importSpecifier(j.identifier('createRoot')))
            }
            if (needsHydrateRoot) {
                newImports.push(j.importSpecifier(j.identifier('hydrateRoot')))
            }

            if (newImports.length > 0) {
                const clientImport = j.importDeclaration(
                    newImports,
                    j.literal('react-dom/client')
                )

                j(reactDOMImportPath).insertAfter(clientImport)
            }
        }
    }

    return hasChanges ? root.toSource({ quote: 'single' }) : fileInfo.source
}

module.exports.parser = 'tsx'
